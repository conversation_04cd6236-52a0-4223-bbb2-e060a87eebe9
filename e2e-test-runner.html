<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E2E Test Runner - Chat Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.success {
            background-color: #28a745;
        }
        .button.warning {
            background-color: #ffc107;
            color: #212529;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .credentials {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
        .instructions {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 E2E Test Runner - Chat Application</h1>
        
        <div class="section">
            <h2>📋 Prerequisites</h2>
            <div class="instructions">
                <strong>Before running tests:</strong>
                <ol>
                    <li>Make sure all services are running (backend, frontend, socket server)</li>
                    <li>Navigate to the chat application at <a href="http://localhost:5000" target="_blank">http://localhost:5000</a></li>
                    <li>Open browser developer tools (F12) and go to the Console tab</li>
                    <li>Copy and paste the E2E test script into the console</li>
                </ol>
            </div>
        </div>

        <div class="section">
            <h2>👥 Test User Credentials</h2>
            <p>The following test users have been created in the database:</p>
            
            <div class="credentials">
<strong>Alice Smith:</strong>
Username: alice
Password: alicepass123
Email: <EMAIL>

<strong>Bob Johnson:</strong>
Username: bob  
Password: bobpass123
Email: <EMAIL>

<strong>Test User 1:</strong>
Username: testuser1
Password: testpass123
Email: <EMAIL>

<strong>Test User 2:</strong>
Username: testuser2
Password: testpass123
Email: <EMAIL>
            </div>
        </div>

        <div class="section">
            <h2>🚀 Quick Test Commands</h2>
            <p>Copy these commands and paste them into the browser console:</p>
            
            <button class="button" onclick="copyToClipboard('loadScript')">
                📥 Load E2E Test Script
            </button>
            <button class="button success" onclick="copyToClipboard('runFullTest')">
                ▶️ Run Full E2E Test
            </button>
            <button class="button warning" onclick="copyToClipboard('runLoginTest')">
                🔑 Test Login Only
            </button>
            <button class="button danger" onclick="copyToClipboard('runMessageTest')">
                💬 Test Messaging Only
            </button>

            <div id="command-output" class="log-output" style="margin-top: 15px;">
Click a button above to copy the corresponding command to your clipboard.
            </div>
        </div>

        <div class="section">
            <h2>📝 Manual Testing Steps</h2>
            <ol>
                <li><strong>Load the test script:</strong> Copy the E2E test script and paste it in the console</li>
                <li><strong>Initialize test:</strong> Run <code>const test = new E2ETest();</code></li>
                <li><strong>Run full test:</strong> Run <code>test.runFullTest();</code></li>
                <li><strong>Monitor results:</strong> Watch the console for test progress and results</li>
                <li><strong>Check encryption:</strong> Look for 🔐 emoji in console logs to verify encryption is working</li>
            </ol>
        </div>

        <div class="section">
            <h2>🔍 What the Test Covers</h2>
            <ul>
                <li>✅ User authentication (login)</li>
                <li>✅ User search functionality</li>
                <li>✅ Conversation creation</li>
                <li>✅ Message sending with encryption</li>
                <li>✅ Real-time message updates</li>
                <li>✅ Encryption status verification</li>
                <li>✅ Socket connection testing</li>
            </ul>
        </div>

        <div class="section">
            <h2>🐛 Troubleshooting</h2>
            <ul>
                <li><strong>Login fails:</strong> Check if backend server is running on port 6000</li>
                <li><strong>Socket errors:</strong> Check if socket server is running on port 3001</li>
                <li><strong>Encryption errors:</strong> Check browser console for crypto support messages</li>
                <li><strong>UI elements not found:</strong> Make sure you're on the correct page and elements have loaded</li>
            </ul>
        </div>
    </div>

    <script>
        const commands = {
            loadScript: `// Load E2E Test Script
fetch('/e2e-console-test.js')
  .then(response => response.text())
  .then(script => {
    eval(script);
    console.log('✅ E2E Test script loaded successfully!');
    console.log('Run: const test = new E2ETest(); test.runFullTest();');
  })
  .catch(error => {
    console.error('❌ Failed to load script:', error);
    console.log('Please copy and paste the script manually from e2e-console-test.js');
  });`,
            
            runFullTest: `// Run Full E2E Test
const test = new E2ETest();
test.runFullTest();`,
            
            runLoginTest: `// Test Login Only
const test = new E2ETest();
test.testLogin('alice', 'alicepass123');`,
            
            runMessageTest: `// Test Messaging (run after login)
const test = new E2ETest();
test.testMessageSending('Hello from E2E test! 🚀');`
        };

        function copyToClipboard(commandKey) {
            const command = commands[commandKey];
            navigator.clipboard.writeText(command).then(() => {
                document.getElementById('command-output').textContent = command;
                console.log('Command copied to clipboard!');
            }).catch(err => {
                console.error('Failed to copy: ', err);
                document.getElementById('command-output').textContent = command;
            });
        }
    </script>
</body>
</html>
