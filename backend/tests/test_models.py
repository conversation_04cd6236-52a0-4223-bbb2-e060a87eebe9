# backend/tests/test_models.py
import pytest
import uuid
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.contrib.auth import get_user_model
from messaging.models import Conversation, ConversationParticipant, Message
from tests.factories import (
    UserFactory, ConversationFactory, GroupConversationFactory,
    ConversationParticipantFactory, MessageFactory
)

User = get_user_model()

@pytest.mark.django_db
class TestUserModel:
    """Test User model functionality."""
    
    def test_create_user(self):
        """Test creating a user with valid data."""
        user = UserFactory()
        assert user.id is not None
        assert isinstance(user.id, uuid.UUID)
        assert user.email
        assert user.username
        assert user.first_name
        assert user.last_name
        assert user.is_active
        assert user.check_password('testpass123')
    
    def test_user_str_representation(self):
        """Test user string representation."""
        user = UserFactory(email='<EMAIL>')
        assert str(user) == '<EMAIL>'
    
    def test_email_uniqueness(self):
        """Test that email must be unique."""
        UserFactory(email='<EMAIL>')
        with pytest.raises(IntegrityError):
            UserFactory(email='<EMAIL>')
    
    def test_username_uniqueness(self):
        """Test that username must be unique."""
        UserFactory(username='testuser')
        with pytest.raises(IntegrityError):
            UserFactory(username='testuser')
    
    def test_user_relationships(self):
        """Test user relationships with conversations and messages."""
        user = UserFactory()
        conversation = ConversationFactory()
        
        # Test conversation participation
        participant = ConversationParticipantFactory(user=user, conversation=conversation)
        assert participant in user.conversation_memberships.all()
        
        # Test sent messages
        message = MessageFactory(sender=user, conversation=conversation)
        assert message in user.sent_messages.all()

@pytest.mark.django_db
class TestConversationModel:
    """Test Conversation model functionality."""
    
    def test_create_direct_conversation(self):
        """Test creating a direct conversation."""
        conversation = ConversationFactory(type='DIRECT')
        assert conversation.id is not None
        assert isinstance(conversation.id, uuid.UUID)
        assert conversation.type == 'DIRECT'
        assert conversation.name is None
        assert conversation.created_at
        assert conversation.updated_at
    
    def test_create_group_conversation(self):
        """Test creating a group conversation."""
        conversation = GroupConversationFactory()
        assert conversation.type == 'GROUP'
        assert conversation.name is not None
    
    def test_conversation_str_representation(self):
        """Test conversation string representation."""
        # Direct conversation
        direct_conv = ConversationFactory(type='DIRECT')
        expected = f"Direct conversation {direct_conv.id}"
        assert str(direct_conv) == expected
        
        # Group conversation
        group_conv = GroupConversationFactory(name='Test Group')
        assert str(group_conv) == 'Test Group'
        
        # Group conversation without name
        group_conv_no_name = ConversationFactory(type='GROUP', name=None)
        expected = f"Group {group_conv_no_name.id}"
        assert str(group_conv_no_name) == expected
    
    def test_conversation_ordering(self):
        """Test that conversations are ordered by updated_at descending."""
        conv1 = ConversationFactory()
        conv2 = ConversationFactory()
        
        # Update conv1 to make it more recent
        conv1.save()
        
        conversations = Conversation.objects.all()
        assert conversations[0] == conv1
        assert conversations[1] == conv2
    
    def test_conversation_participants_relationship(self):
        """Test conversation participants relationship."""
        conversation = ConversationFactory()
        user1 = UserFactory()
        user2 = UserFactory()
        
        participant1 = ConversationParticipantFactory(
            conversation=conversation, user=user1
        )
        participant2 = ConversationParticipantFactory(
            conversation=conversation, user=user2
        )
        
        participants = conversation.participants.all()
        assert participant1 in participants
        assert participant2 in participants
        assert len(participants) == 2
    
    def test_conversation_messages_relationship(self):
        """Test conversation messages relationship."""
        conversation = ConversationFactory()
        user = UserFactory()
        
        message1 = MessageFactory(conversation=conversation, sender=user)
        message2 = MessageFactory(conversation=conversation, sender=user)
        
        messages = conversation.messages.all()
        assert message1 in messages
        assert message2 in messages
        assert len(messages) == 2

@pytest.mark.django_db
class TestConversationParticipantModel:
    """Test ConversationParticipant model functionality."""
    
    def test_create_participant(self):
        """Test creating a conversation participant."""
        participant = ConversationParticipantFactory()
        assert participant.id is not None
        assert isinstance(participant.id, uuid.UUID)
        assert participant.conversation
        assert participant.user
        assert participant.role == 'MEMBER'
        assert participant.joined_at
    
    def test_participant_str_representation(self):
        """Test participant string representation."""
        user = UserFactory(username='testuser')
        conversation = ConversationFactory()
        participant = ConversationParticipantFactory(
            user=user, conversation=conversation
        )
        expected = f"testuser in {conversation}"
        assert str(participant) == expected
    
    def test_unique_user_conversation_constraint(self):
        """Test that user can only participate once per conversation."""
        user = UserFactory()
        conversation = ConversationFactory()
        
        ConversationParticipantFactory(user=user, conversation=conversation)
        
        with pytest.raises(IntegrityError):
            ConversationParticipantFactory(user=user, conversation=conversation)
    
    def test_participant_roles(self):
        """Test different participant roles."""
        admin_participant = ConversationParticipantFactory(role='ADMIN')
        member_participant = ConversationParticipantFactory(role='MEMBER')
        
        assert admin_participant.role == 'ADMIN'
        assert member_participant.role == 'MEMBER'
    
    def test_cascade_delete_conversation(self):
        """Test that participants are deleted when conversation is deleted."""
        conversation = ConversationFactory()
        participant = ConversationParticipantFactory(conversation=conversation)
        participant_id = participant.id
        
        conversation.delete()
        
        assert not ConversationParticipant.objects.filter(id=participant_id).exists()
    
    def test_cascade_delete_user(self):
        """Test that participants are deleted when user is deleted."""
        user = UserFactory()
        participant = ConversationParticipantFactory(user=user)
        participant_id = participant.id
        
        user.delete()
        
        assert not ConversationParticipant.objects.filter(id=participant_id).exists()

@pytest.mark.django_db
class TestMessageModel:
    """Test Message model functionality."""
    
    def test_create_message(self):
        """Test creating a message."""
        message = MessageFactory()
        assert message.id is not None
        assert isinstance(message.id, uuid.UUID)
        assert message.conversation
        assert message.sender
        assert message.content
        assert message.message_type == 'TEXT'
        assert message.created_at
        assert message.updated_at
    
    def test_message_str_representation(self):
        """Test message string representation."""
        user = UserFactory(username='testuser')
        conversation = ConversationFactory()
        message = MessageFactory(sender=user, conversation=conversation)
        expected = f"Message from testuser in {conversation}"
        assert str(message) == expected
    
    def test_message_types(self):
        """Test different message types."""
        text_message = MessageFactory(message_type='TEXT')
        image_message = MessageFactory(message_type='IMAGE')
        file_message = MessageFactory(message_type='FILE')
        system_message = MessageFactory(message_type='SYSTEM')
        
        assert text_message.message_type == 'TEXT'
        assert image_message.message_type == 'IMAGE'
        assert file_message.message_type == 'FILE'
        assert system_message.message_type == 'SYSTEM'
    
    def test_message_ordering(self):
        """Test that messages are ordered by created_at ascending."""
        conversation = ConversationFactory()
        user = UserFactory()
        
        message1 = MessageFactory(conversation=conversation, sender=user)
        message2 = MessageFactory(conversation=conversation, sender=user)
        
        messages = Message.objects.filter(conversation=conversation)
        assert messages[0] == message1
        assert messages[1] == message2
    
    def test_cascade_delete_conversation(self):
        """Test that messages are deleted when conversation is deleted."""
        conversation = ConversationFactory()
        message = MessageFactory(conversation=conversation)
        message_id = message.id
        
        conversation.delete()
        
        assert not Message.objects.filter(id=message_id).exists()
    
    def test_cascade_delete_sender(self):
        """Test that messages are deleted when sender is deleted."""
        user = UserFactory()
        message = MessageFactory(sender=user)
        message_id = message.id
        
        user.delete()
        
        assert not Message.objects.filter(id=message_id).exists()
    
    def test_message_content_validation(self):
        """Test message content validation."""
        # Test empty content
        with pytest.raises(ValidationError):
            message = MessageFactory.build(content='')
            message.full_clean()
        
        # Test very long content (should be allowed up to TextField limit)
        long_content = 'a' * 10000
        message = MessageFactory(content=long_content)
        assert message.content == long_content

@pytest.mark.django_db
class TestModelRelationships:
    """Test complex model relationships and queries."""

    def test_user_conversations_query(self):
        """Test querying conversations for a user."""
        user = UserFactory()
        other_user = UserFactory()

        # Create conversations where user participates
        conv1 = ConversationFactory()
        conv2 = ConversationFactory()
        conv3 = ConversationFactory()  # User doesn't participate

        ConversationParticipantFactory(user=user, conversation=conv1)
        ConversationParticipantFactory(user=user, conversation=conv2)
        ConversationParticipantFactory(user=other_user, conversation=conv3)

        user_conversations = Conversation.objects.filter(
            participants__user=user
        ).distinct()

        assert conv1 in user_conversations
        assert conv2 in user_conversations
        assert conv3 not in user_conversations
        assert len(user_conversations) == 2

    def test_conversation_with_messages_and_participants(self):
        """Test complex query with messages and participants."""
        user1 = UserFactory()
        user2 = UserFactory()
        conversation = ConversationFactory()

        # Add participants
        ConversationParticipantFactory(user=user1, conversation=conversation)
        ConversationParticipantFactory(user=user2, conversation=conversation)

        # Add messages
        MessageFactory(conversation=conversation, sender=user1, content="Hello")
        MessageFactory(conversation=conversation, sender=user2, content="Hi there")

        # Query conversation with related data
        conv_with_data = Conversation.objects.prefetch_related(
            'participants__user', 'messages__sender'
        ).get(id=conversation.id)

        participants = conv_with_data.participants.all()
        messages = conv_with_data.messages.all()

        assert len(participants) == 2
        assert len(messages) == 2
        assert participants[0].user in [user1, user2]
        assert participants[1].user in [user1, user2]

    def test_find_direct_conversation_between_users(self):
        """Test finding existing direct conversation between two users."""
        user1 = UserFactory()
        user2 = UserFactory()
        user3 = UserFactory()

        # Create direct conversation between user1 and user2
        direct_conv = ConversationFactory(type='DIRECT')
        ConversationParticipantFactory(user=user1, conversation=direct_conv)
        ConversationParticipantFactory(user=user2, conversation=direct_conv)

        # Create another conversation with user3
        other_conv = ConversationFactory(type='DIRECT')
        ConversationParticipantFactory(user=user1, conversation=other_conv)
        ConversationParticipantFactory(user=user3, conversation=other_conv)

        # Find conversation between user1 and user2
        found_conv = Conversation.objects.filter(
            type='DIRECT',
            participants__user=user1
        ).filter(
            participants__user=user2
        ).first()

        assert found_conv == direct_conv

        # Verify we don't find conversation between user1 and user3 when looking for user2
        not_found = Conversation.objects.filter(
            type='DIRECT',
            participants__user=user1
        ).filter(
            participants__user=user2
        ).filter(
            participants__user=user3
        ).first()

        assert not_found is None

@pytest.mark.django_db
class TestModelConstraintsAndEdgeCases:
    """Test model constraints and edge cases."""

    def test_conversation_type_choices(self):
        """Test conversation type choices validation."""
        # Valid types
        direct_conv = ConversationFactory(type='DIRECT')
        group_conv = ConversationFactory(type='GROUP')

        assert direct_conv.type == 'DIRECT'
        assert group_conv.type == 'GROUP'

    def test_participant_role_choices(self):
        """Test participant role choices validation."""
        # Valid roles
        admin = ConversationParticipantFactory(role='ADMIN')
        member = ConversationParticipantFactory(role='MEMBER')

        assert admin.role == 'ADMIN'
        assert member.role == 'MEMBER'

    def test_message_type_choices(self):
        """Test message type choices validation."""
        # Valid message types
        text_msg = MessageFactory(message_type='TEXT')
        image_msg = MessageFactory(message_type='IMAGE')
        file_msg = MessageFactory(message_type='FILE')
        system_msg = MessageFactory(message_type='SYSTEM')

        assert text_msg.message_type == 'TEXT'
        assert image_msg.message_type == 'IMAGE'
        assert file_msg.message_type == 'FILE'
        assert system_msg.message_type == 'SYSTEM'

    def test_user_email_validation(self):
        """Test user email validation."""
        # Valid email
        user = UserFactory(email='<EMAIL>')
        assert user.email == '<EMAIL>'

        # Test that Django's email validation works
        with pytest.raises(ValidationError):
            user = UserFactory.build(email='invalid-email')
            user.full_clean()

    def test_conversation_name_length(self):
        """Test conversation name length constraints."""
        # Test maximum length (100 characters)
        long_name = 'a' * 100
        conversation = ConversationFactory(name=long_name)
        assert conversation.name == long_name

        # Test name can be null
        conversation_no_name = ConversationFactory(name=None)
        assert conversation_no_name.name is None

    def test_bulk_operations(self):
        """Test bulk database operations."""
        users = UserFactory.create_batch(5)
        conversation = ConversationFactory()

        # Bulk create participants
        participants = [
            ConversationParticipant(
                conversation=conversation,
                user=user,
                role='MEMBER'
            ) for user in users
        ]
        ConversationParticipant.objects.bulk_create(participants)

        assert conversation.participants.count() == 5

        # Bulk create messages
        messages = [
            Message(
                conversation=conversation,
                sender=user,
                content=f"Message from {user.username}",
                message_type='TEXT'
            ) for user in users
        ]
        Message.objects.bulk_create(messages)

        assert conversation.messages.count() == 5

    def test_database_indexes_and_performance(self):
        """Test that database queries use proper indexes."""
        # Create test data
        users = UserFactory.create_batch(10)
        conversations = ConversationFactory.create_batch(5)

        # Add participants to conversations
        for i, conv in enumerate(conversations):
            for j in range(3):  # 3 participants per conversation
                user_index = (i * 3 + j) % len(users)
                ConversationParticipantFactory(
                    conversation=conv,
                    user=users[user_index]
                )

        # Test efficient queries
        user = users[0]

        # This should use the index on participants__user
        user_conversations = Conversation.objects.filter(
            participants__user=user
        ).distinct()

        assert len(user_conversations) >= 1

        # Test message queries with proper ordering
        conversation = conversations[0]
        MessageFactory.create_batch(10, conversation=conversation)

        # This should use the ordering index
        recent_messages = conversation.messages.all()[:5]
        assert len(recent_messages) == 5
