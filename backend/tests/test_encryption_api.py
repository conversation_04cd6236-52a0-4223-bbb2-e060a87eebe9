# backend/tests/test_encryption_api.py
import pytest
import json
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from encryption.models import User<PERSON>eyBundle, OneTimePreKey, ConversationSession
from messaging.models import Conversation, ConversationParticipant

User = get_user_model()


class EncryptionAPITestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        self.other_user = User.objects.create_user(
            email='<EMAIL>',
            username='otheruser',
            password='testpass123',
            first_name='Other',
            last_name='User'
        )
        self.client.force_authenticate(user=self.user)

    def test_upload_key_bundle(self):
        """Test uploading a user's key bundle"""
        url = reverse('encryption:upload_key_bundle')
        data = {
            'identity_public_key': 'mock_identity_key_base64',
            'signed_prekey_id': 1,
            'signed_prekey_public': 'mock_signed_prekey_base64',
            'signed_prekey_signature': 'mock_signature_base64',
            'one_time_prekeys': [
                {'key_id': 1, 'public_key': 'mock_prekey_1_base64'},
                {'key_id': 2, 'public_key': 'mock_prekey_2_base64'},
            ]
        }

        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('key_bundle_id', response.data)
        self.assertEqual(response.data['one_time_prekeys_created'], 2)

        # Verify key bundle was created
        key_bundle = UserKeyBundle.objects.get(user=self.user)
        self.assertEqual(key_bundle.identity_public_key, 'mock_identity_key_base64')
        self.assertEqual(key_bundle.signed_prekey_id, 1)

        # Verify one-time pre-keys were created
        prekeys = OneTimePreKey.objects.filter(user=self.user)
        self.assertEqual(prekeys.count(), 2)

    def test_upload_key_bundle_replaces_existing(self):
        """Test that uploading a new key bundle replaces the existing one"""
        # Create initial key bundle
        UserKeyBundle.objects.create(
            user=self.user,
            identity_public_key='old_key',
            signed_prekey_id=1,
            signed_prekey_public='old_prekey',
            signed_prekey_signature='old_signature'
        )

        url = reverse('encryption:upload_key_bundle')
        data = {
            'identity_public_key': 'new_identity_key',
            'signed_prekey_id': 2,
            'signed_prekey_public': 'new_signed_prekey',
            'signed_prekey_signature': 'new_signature',
        }

        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify old key bundle was replaced
        key_bundles = UserKeyBundle.objects.filter(user=self.user)
        self.assertEqual(key_bundles.count(), 1)
        self.assertEqual(key_bundles.first().identity_public_key, 'new_identity_key')

    def test_get_key_bundle(self):
        """Test retrieving another user's key bundle"""
        # Create key bundle for other user
        key_bundle = UserKeyBundle.objects.create(
            user=self.other_user,
            identity_public_key='other_identity_key',
            signed_prekey_id=1,
            signed_prekey_public='other_signed_prekey',
            signed_prekey_signature='other_signature'
        )

        # Create one-time pre-key
        prekey = OneTimePreKey.objects.create(
            user=self.other_user,
            key_id=1,
            public_key='other_prekey_1'
        )

        url = reverse('encryption:get_key_bundle', kwargs={'user_id': self.other_user.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['identity_public_key'], 'other_identity_key')
        self.assertEqual(response.data['signed_prekey_id'], 1)
        self.assertIn('one_time_prekey', response.data)
        self.assertEqual(response.data['one_time_prekey']['key_id'], 1)

        # Verify one-time pre-key was marked as used
        prekey.refresh_from_db()
        self.assertTrue(prekey.is_used)
        self.assertIsNotNone(prekey.used_at)

    def test_get_key_bundle_not_found(self):
        """Test retrieving key bundle for user without one"""
        url = reverse('encryption:get_key_bundle', kwargs={'user_id': self.other_user.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('error', response.data)

    def test_upload_one_time_prekeys(self):
        """Test uploading batch of one-time pre-keys"""
        url = reverse('encryption:upload_one_time_prekeys')
        data = {
            'prekeys': [
                {'key_id': 10, 'public_key': 'prekey_10'},
                {'key_id': 11, 'public_key': 'prekey_11'},
                {'key_id': 12, 'public_key': 'prekey_12'},
            ]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['count'], 3)
        self.assertEqual(response.data['skipped'], 0)

        # Verify pre-keys were created
        prekeys = OneTimePreKey.objects.filter(user=self.user)
        self.assertEqual(prekeys.count(), 3)

    def test_upload_duplicate_prekeys_skipped(self):
        """Test that duplicate pre-key IDs are skipped"""
        # Create existing pre-key
        OneTimePreKey.objects.create(
            user=self.user,
            key_id=10,
            public_key='existing_prekey'
        )

        url = reverse('encryption:upload_one_time_prekeys')
        data = {
            'prekeys': [
                {'key_id': 10, 'public_key': 'duplicate_prekey'},
                {'key_id': 11, 'public_key': 'new_prekey'},
            ]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['count'], 1)  # Only new one created
        self.assertEqual(response.data['skipped'], 1)  # Duplicate skipped

    def test_get_prekey_count(self):
        """Test getting pre-key count statistics"""
        # Create some pre-keys
        OneTimePreKey.objects.create(user=self.user, key_id=1, public_key='key1')
        OneTimePreKey.objects.create(user=self.user, key_id=2, public_key='key2', is_used=True)
        OneTimePreKey.objects.create(user=self.user, key_id=3, public_key='key3')

        url = reverse('encryption:get_prekey_count')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_prekeys'], 3)
        self.assertEqual(response.data['unused_prekeys'], 2)
        self.assertEqual(response.data['usage_percentage'], 33.33)

    def test_create_conversation_session(self):
        """Test creating a conversation session"""
        # Create a conversation
        conversation = Conversation.objects.create(type='DIRECT')
        ConversationParticipant.objects.create(
            conversation=conversation,
            user=self.user,
            role='MEMBER'
        )

        url = reverse('encryption:create_conversation_session')
        data = {
            'conversation_id': str(conversation.id),
            'participant_id': str(self.user.id),
            'session_state': {'test': 'data'},
            'root_key': 'mock_root_key',
            'message_number_send': 0,
            'message_number_receive': 0,
            'previous_chain_length': 0,
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('session_id', response.data)
        self.assertTrue(response.data['created'])

        # Verify session was created
        session = ConversationSession.objects.get(
            conversation=conversation,
            participant=self.user
        )
        self.assertEqual(session.root_key, 'mock_root_key')
        self.assertEqual(session.session_state, {'test': 'data'})

    def test_create_session_access_denied(self):
        """Test creating session for conversation user doesn't have access to"""
        # Create conversation without adding user as participant
        conversation = Conversation.objects.create(type='DIRECT')

        url = reverse('encryption:create_conversation_session')
        data = {
            'conversation_id': str(conversation.id),
            'participant_id': str(self.user.id),
            'session_state': {'test': 'data'},
            'root_key': 'mock_root_key',
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('error', response.data)

    def test_get_conversation_session(self):
        """Test retrieving a conversation session"""
        # Create conversation and session
        conversation = Conversation.objects.create(type='DIRECT')
        ConversationParticipant.objects.create(
            conversation=conversation,
            user=self.user,
            role='MEMBER'
        )
        session = ConversationSession.objects.create(
            conversation=conversation,
            participant=self.user,
            session_state={'test': 'data'},
            root_key='mock_root_key'
        )

        url = reverse('encryption:get_conversation_session', kwargs={'conversation_id': conversation.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['root_key'], 'mock_root_key')
        self.assertEqual(response.data['session_state'], {'test': 'data'})

    def test_update_conversation_session(self):
        """Test updating a conversation session"""
        # Create conversation and session
        conversation = Conversation.objects.create(type='DIRECT')
        ConversationParticipant.objects.create(
            conversation=conversation,
            user=self.user,
            role='MEMBER'
        )
        session = ConversationSession.objects.create(
            conversation=conversation,
            participant=self.user,
            session_state={'old': 'data'},
            root_key='old_root_key',
            message_number_send=0
        )

        url = reverse('encryption:update_conversation_session', kwargs={'conversation_id': conversation.id})
        data = {
            'session_state': {'new': 'data'},
            'message_number_send': 5,
        }

        response = self.client.put(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('updated_fields', response.data)

        # Verify session was updated
        session.refresh_from_db()
        self.assertEqual(session.session_state, {'new': 'data'})
        self.assertEqual(session.message_number_send, 5)
        self.assertEqual(session.root_key, 'old_root_key')  # Should remain unchanged

    def test_authentication_required(self):
        """Test that all endpoints require authentication"""
        self.client.force_authenticate(user=None)

        endpoints = [
            reverse('encryption:upload_key_bundle'),
            reverse('encryption:get_key_bundle', kwargs={'user_id': self.other_user.id}),
            reverse('encryption:upload_one_time_prekeys'),
            reverse('encryption:get_prekey_count'),
            reverse('encryption:create_conversation_session'),
        ]

        for url in endpoints:
            response = self.client.post(url, {})
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_encryption_security_features(self):
        """Test security features of the encryption system"""
        # Test that server cannot decrypt messages
        url = reverse('encryption:upload_key_bundle')
        data = {
            'identity_public_key': 'mock_identity_key_base64',
            'signed_prekey_id': 1,
            'signed_prekey_public': 'mock_signed_prekey_base64',
            'signed_prekey_signature': 'mock_signature_base64',
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify that only public keys are stored
        key_bundle = UserKeyBundle.objects.get(user=self.user)
        self.assertIn('public', key_bundle.identity_public_key.lower())
        self.assertIn('public', key_bundle.signed_prekey_public.lower())

    def test_forward_secrecy_with_one_time_prekeys(self):
        """Test that one-time pre-keys provide forward secrecy"""
        # Upload one-time pre-keys
        url = reverse('encryption:upload_one_time_prekeys')
        data = {
            'prekeys': [
                {'key_id': 1, 'public_key': 'prekey_1'},
                {'key_id': 2, 'public_key': 'prekey_2'},
            ]
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Create key bundle for other user
        UserKeyBundle.objects.create(
            user=self.other_user,
            identity_public_key='other_identity_key',
            signed_prekey_id=1,
            signed_prekey_public='other_signed_prekey',
            signed_prekey_signature='other_signature'
        )

        # First key exchange should consume a one-time pre-key
        url = reverse('encryption:get_key_bundle', kwargs={'user_id': self.user.id})
        response = self.client.force_authenticate(user=self.other_user)
        self.client.force_authenticate(user=self.other_user)
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('one_time_prekey', response.data)
        first_prekey_id = response.data['one_time_prekey']['key_id']

        # Second key exchange should get a different one-time pre-key
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('one_time_prekey', response.data)
        second_prekey_id = response.data['one_time_prekey']['key_id']

        # Should be different pre-keys
        self.assertNotEqual(first_prekey_id, second_prekey_id)

        # Verify first pre-key is marked as used
        first_prekey = OneTimePreKey.objects.get(user=self.user, key_id=first_prekey_id)
        self.assertTrue(first_prekey.is_used)
        self.assertIsNotNone(first_prekey.used_at)

    def test_session_isolation(self):
        """Test that conversation sessions are properly isolated"""
        # Create two conversations
        conversation1 = Conversation.objects.create(type='DIRECT')
        conversation2 = Conversation.objects.create(type='DIRECT')

        ConversationParticipant.objects.create(
            conversation=conversation1, user=self.user, role='MEMBER'
        )
        ConversationParticipant.objects.create(
            conversation=conversation2, user=self.user, role='MEMBER'
        )

        # Create sessions for both conversations
        url = reverse('encryption:create_conversation_session')

        session1_data = {
            'conversation_id': str(conversation1.id),
            'participant_id': str(self.user.id),
            'session_state': {'conversation': '1'},
            'root_key': 'root_key_1',
        }

        session2_data = {
            'conversation_id': str(conversation2.id),
            'participant_id': str(self.user.id),
            'session_state': {'conversation': '2'},
            'root_key': 'root_key_2',
        }

        response1 = self.client.post(url, session1_data, format='json')
        response2 = self.client.post(url, session2_data, format='json')

        self.assertEqual(response1.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response2.status_code, status.HTTP_201_CREATED)

        # Verify sessions are isolated
        session1 = ConversationSession.objects.get(conversation=conversation1, participant=self.user)
        session2 = ConversationSession.objects.get(conversation=conversation2, participant=self.user)

        self.assertEqual(session1.root_key, 'root_key_1')
        self.assertEqual(session2.root_key, 'root_key_2')
        self.assertEqual(session1.session_state, {'conversation': '1'})
        self.assertEqual(session2.session_state, {'conversation': '2'})

    def test_key_bundle_signature_verification_data(self):
        """Test that signature data is properly stored for verification"""
        url = reverse('encryption:upload_key_bundle')
        data = {
            'identity_public_key': 'mock_identity_key_base64',
            'signed_prekey_id': 1,
            'signed_prekey_public': 'mock_signed_prekey_base64',
            'signed_prekey_signature': 'mock_signature_base64',
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify signature is stored for client-side verification
        key_bundle = UserKeyBundle.objects.get(user=self.user)
        self.assertEqual(key_bundle.signed_prekey_signature, 'mock_signature_base64')

        # Get key bundle and verify signature is included
        url = reverse('encryption:get_key_bundle', kwargs={'user_id': self.user.id})
        self.client.force_authenticate(user=self.other_user)
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['signed_prekey_signature'], 'mock_signature_base64')
