# Generated by Django 5.2.4 on 2025-08-03 07:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('messaging', '0002_messagestatus'),
    ]

    operations = [
        migrations.AddField(
            model_name='message',
            name='encrypted_content',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='message',
            name='iv',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='message',
            name='message_key_id',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='message',
            name='message_number',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='message',
            name='previous_chain_length',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='message',
            name='sender_ratchet_key',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='message',
            name='content',
            field=models.TextField(blank=True),
        ),
    ]
