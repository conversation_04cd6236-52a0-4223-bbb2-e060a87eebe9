# backend/messaging/views.py
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.contrib.auth import get_user_model
from django.http import Http404
from .models import Conversation, ConversationParticipant, Message, MessageStatus
from .schemas import UserBasic

User = get_user_model()

class UserPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 50

def serialize_user(user):
    """Convert Django User model to Pydantic UserBasic"""
    return UserBasic(
        id=user.id,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        profile_picture=user.profile_picture
    )













@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def search_users(request):
    """Search for users by username, first name, or last name"""
    query = request.GET.get('q', '').strip()

    if not query:
        return Response({
            'success': True,
            'results': []
        }, status=status.HTTP_200_OK)

    if len(query) < 2:
        return Response({
            'success': False,
            'error': 'Search query must be at least 2 characters long'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Search users by username, first name, or last name
        # Exclude the current user from results
        users = User.objects.filter(
            Q(username__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query)
        ).exclude(
            id=request.user.id
        ).order_by('username')[:10]  # Limit to 10 results

        # Serialize users
        user_data = []
        for user in users:
            user_data.append({
                'id': str(user.id),
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'profile_picture': user.profile_picture,
                'full_name': f"{user.first_name} {user.last_name}".strip()
            })

        return Response({
            'success': True,
            'results': user_data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_user_profile(request, user_id):
    """Get a user's profile by ID"""
    try:
        # Get user by ID
        user = get_object_or_404(User, id=user_id)

        # Serialize user data
        user_data = {
            'id': str(user.id),
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'profile_picture': user.profile_picture,
            'full_name': f"{user.first_name} {user.last_name}".strip()
        }

        return Response({
            'success': True,
            'data': user_data
        }, status=status.HTTP_200_OK)

    except Http404:
        return Response({
            'success': False,
            'error': 'User not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_conversations(request):
    """Get user's conversations"""
    try:
        # Get conversations where user is a participant
        conversations = Conversation.objects.filter(
            participants__user=request.user
        ).distinct().order_by('-updated_at')

        conversation_data = []
        for conversation in conversations:
            # Get participants
            participants = []
            for participant in conversation.participants.all():
                participants.append({
                    'id': str(participant.user.id),
                    'username': participant.user.username,
                    'first_name': participant.user.first_name,
                    'last_name': participant.user.last_name,
                    'profile_picture': participant.user.profile_picture,
                })

            # Get last message
            last_message = conversation.messages.order_by('-created_at').first()
            last_message_data = None
            if last_message:
                last_message_data = {
                    'id': str(last_message.id),
                    'content': last_message.content,
                    'sender': {
                        'username': last_message.sender.username,
                    },
                    'createdAt': last_message.created_at.isoformat(),
                }

            conversation_data.append({
                'id': str(conversation.id),
                'type': conversation.type,
                'name': conversation.name,
                'participants': participants,
                'lastMessage': last_message_data,
                'createdAt': conversation.created_at.isoformat(),
                'updatedAt': conversation.updated_at.isoformat(),
            })

        return Response({
            'success': True,
            'data': {
                'results': conversation_data,
                'count': len(conversation_data),
                'next': None,
                'previous': None,
            }
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_conversation(request):
    """Create a new conversation"""
    try:
        data = request.data
        conversation_type = data.get('type', 'DIRECT')
        name = data.get('name')
        participant_ids = data.get('participant_ids', [])

        # Validate participant IDs
        if not participant_ids:
            return Response({
                'success': False,
                'error': 'At least one participant is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create conversation
        conversation = Conversation.objects.create(
            type=conversation_type,
            name=name
        )

        # Add creator as participant
        ConversationParticipant.objects.create(
            conversation=conversation,
            user=request.user,
            role='ADMIN' if conversation_type == 'GROUP' else 'MEMBER'
        )

        # Add other participants
        for participant_id in participant_ids:
            try:
                participant_user = User.objects.get(id=participant_id)
                ConversationParticipant.objects.create(
                    conversation=conversation,
                    user=participant_user,
                    role='MEMBER'
                )
            except User.DoesNotExist:
                continue

        # Return conversation data
        participants = []
        for participant in conversation.participants.all():
            participants.append({
                'id': str(participant.user.id),
                'username': participant.user.username,
                'first_name': participant.user.first_name,
                'last_name': participant.user.last_name,
                'profile_picture': participant.user.profile_picture,
            })

        conversation_data = {
            'id': str(conversation.id),
            'type': conversation.type,
            'name': conversation.name,
            'participants': participants,
            'lastMessage': None,
            'createdAt': conversation.created_at.isoformat(),
            'updatedAt': conversation.updated_at.isoformat(),
        }

        return Response({
            'success': True,
            'data': conversation_data
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_conversation(request, conversation_id):
    """Get a specific conversation"""
    try:
        conversation = get_object_or_404(
            Conversation.objects.filter(participants__user=request.user),
            id=conversation_id
        )

        # Get participants
        participants = []
        for participant in conversation.participants.all():
            participants.append({
                'id': str(participant.user.id),
                'username': participant.user.username,
                'first_name': participant.user.first_name,
                'last_name': participant.user.last_name,
                'profile_picture': participant.user.profile_picture,
            })

        # Get last message
        last_message = conversation.messages.order_by('-created_at').first()
        last_message_data = None
        if last_message:
            last_message_data = {
                'id': str(last_message.id),
                'content': last_message.content,
                'sender': {
                    'username': last_message.sender.username,
                },
                'createdAt': last_message.created_at.isoformat(),
            }

        conversation_data = {
            'id': str(conversation.id),
            'type': conversation.type,
            'name': conversation.name,
            'participants': participants,
            'lastMessage': last_message_data,
            'createdAt': conversation.created_at.isoformat(),
            'updatedAt': conversation.updated_at.isoformat(),
        }

        return Response({
            'success': True,
            'data': conversation_data
        }, status=status.HTTP_200_OK)

    except Http404:
        return Response({
            'success': False,
            'error': 'Conversation not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_messages(request, conversation_id):
    """Get messages for a conversation"""
    try:
        # Verify user has access to this conversation
        conversation = get_object_or_404(
            Conversation.objects.filter(participants__user=request.user),
            id=conversation_id
        )

        # Get messages with pagination
        page = int(request.GET.get('page', 1))
        page_size = 20

        messages = Message.objects.filter(
            conversation=conversation
        ).order_by('-created_at')

        # Simple pagination
        start = (page - 1) * page_size
        end = start + page_size
        paginated_messages = messages[start:end]

        # Serialize messages with encryption support
        message_data = []
        for message in paginated_messages:
            # Prepare message data with encryption fields
            msg_data = {
                'id': str(message.id),
                'conversation_id': str(message.conversation.id),
                'conversationId': str(message.conversation.id),
                'sender': {
                    'id': str(message.sender.id),
                    'username': message.sender.username,
                    'first_name': message.sender.first_name,
                    'last_name': message.sender.last_name,
                    'profile_picture': message.sender.profile_picture,
                },
                'message_type': message.message_type,
                'messageType': message.message_type,
                'created_at': message.created_at.isoformat(),
                'createdAt': message.created_at.isoformat(),
                'updated_at': message.updated_at.isoformat(),
                'updatedAt': message.updated_at.isoformat(),
            }

            # Handle encrypted vs unencrypted messages
            if message.encrypted_content:
                # Encrypted message - include encryption fields, empty content
                msg_data.update({
                    'content': '',  # Empty for security
                    'encryptedContent': message.encrypted_content,
                    'iv': message.iv,
                    'messageNumber': message.message_number,
                    'senderRatchetKey': message.sender_ratchet_key,
                    'previousChainLength': message.previous_chain_length,
                    'messageKeyId': message.message_key_id,
                })
            else:
                # Legacy unencrypted message - include content, no encryption fields
                msg_data.update({
                    'content': message.content,
                    'encryptedContent': None,
                    'iv': None,
                    'messageNumber': 0,
                    'senderRatchetKey': None,
                    'previousChainLength': 0,
                    'messageKeyId': None,
                })

            message_data.append(msg_data)

        # Reverse to show oldest first
        message_data.reverse()

        return Response({
            'success': True,
            'data': {
                'results': message_data,
                'count': len(message_data),
                'next': None if end >= messages.count() else f"?page={page + 1}",
                'previous': None if page <= 1 else f"?page={page - 1}",
            }
        }, status=status.HTTP_200_OK)

    except Http404:
        return Response({
            'success': False,
            'error': 'Conversation not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def send_message(request, conversation_id):
    """Send a message to a conversation"""
    try:
        # Verify user has access to this conversation
        conversation = get_object_or_404(
            Conversation.objects.filter(participants__user=request.user),
            id=conversation_id
        )

        data = request.data
        content = data.get('content', '').strip()
        message_type = data.get('messageType', 'TEXT')

        if not content:
            return Response({
                'success': False,
                'error': 'Message content is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create message
        message = Message.objects.create(
            conversation=conversation,
            sender=request.user,
            content=content,
            message_type=message_type
        )

        # Update conversation timestamp
        conversation.updated_at = message.created_at
        conversation.save()

        # Create message status for sender
        MessageStatus.objects.create(
            message=message,
            user=request.user,
            status='SENT'
        )

        # Serialize message
        message_data = {
            'id': str(message.id),
            'conversation_id': str(message.conversation.id),
            'conversationId': str(message.conversation.id),
            'sender': {
                'id': str(message.sender.id),
                'username': message.sender.username,
                'first_name': message.sender.first_name,
                'last_name': message.sender.last_name,
                'profile_picture': message.sender.profile_picture,
            },
            'content': message.content,
            'message_type': message.message_type,
            'messageType': message.message_type,
            'created_at': message.created_at.isoformat(),
            'createdAt': message.created_at.isoformat(),
            'updated_at': message.updated_at.isoformat(),
            'updatedAt': message.updated_at.isoformat(),
            'status': 'SENT',
        }

        return Response({
            'success': True,
            'data': message_data
        }, status=status.HTTP_201_CREATED)

    except Http404:
        return Response({
            'success': False,
            'error': 'Conversation not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
