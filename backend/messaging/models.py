# backend/messaging/models.py
import uuid
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Conversation(models.Model):
    CONVERSATION_TYPES = [
        ('DIRECT', 'Direct Message'),
        ('GROUP', 'Group Chat'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type = models.CharField(max_length=10, choices=CONVERSATION_TYPES, default='DIRECT')
    name = models.CharField(max_length=100, blank=True, null=True)  # For group chats
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'conversations'
        ordering = ['-updated_at']

    def __str__(self):
        if self.type == 'GROUP':
            return self.name or f"Group {self.id}"
        return f"Direct conversation {self.id}"

class ConversationParticipant(models.Model):
    PARTICIPANT_ROLES = [
        ('ADMIN', 'Admin'),
        ('MEMBER', 'Member'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='participants')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversation_memberships')
    role = models.CharField(max_length=10, choices=PARTICIPANT_ROLES, default='MEMBER')
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'conversation_participants'
        unique_together = ['conversation', 'user']

    def __str__(self):
        return f"{self.user.username} in {self.conversation}"

class Message(models.Model):
    MESSAGE_TYPES = [
        ('TEXT', 'Text Message'),
        ('IMAGE', 'Image'),
        ('FILE', 'File'),
        ('SYSTEM', 'System Message'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')

    # Original content field - kept for backward compatibility, will be empty for encrypted messages
    content = models.TextField(blank=True)

    # Encryption fields
    encrypted_content = models.TextField(blank=True)  # Base64 encoded encrypted content
    message_key_id = models.TextField(null=True, blank=True)  # For key identification
    sender_ratchet_key = models.TextField(null=True, blank=True)  # Base64 encoded
    message_number = models.IntegerField(default=0)
    previous_chain_length = models.IntegerField(default=0)
    iv = models.TextField(null=True, blank=True)  # Base64 encoded initialization vector

    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='TEXT')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'messages'
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.sender.username} in {self.conversation}"

    @property
    def is_encrypted(self):
        """Check if this message is encrypted"""
        return bool(self.encrypted_content)

    def get_content(self):
        """Get the appropriate content field"""
        return self.encrypted_content if self.is_encrypted else self.content


class MessageStatus(models.Model):
    STATUS_TYPES = [
        ('SENT', 'Sent'),
        ('DELIVERED', 'Delivered'),
        ('READ', 'Read'),
        ('FAILED', 'Failed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='statuses')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='message_statuses')
    status = models.CharField(max_length=10, choices=STATUS_TYPES, default='SENT')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'message_statuses'
        unique_together = ['message', 'user']
        ordering = ['created_at']

    def __str__(self):
        return f"{self.user.username} - {self.message.id} - {self.status}"
