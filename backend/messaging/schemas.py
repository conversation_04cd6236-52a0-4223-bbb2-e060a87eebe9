# backend/messaging/schemas.py
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from typing import Optional, List
from datetime import datetime
from enum import Enum
import uuid

class ConversationType(str, Enum):
    DIRECT = "DIRECT"
    GROUP = "GROUP"

class MessageType(str, Enum):
    TEXT = "TEXT"
    IMAGE = "IMAGE"
    FILE = "FILE"
    SYSTEM = "SYSTEM"

class MessageStatusType(str, Enum):
    SENT = "SENT"
    DELIVERED = "DELIVERED"
    READ = "READ"
    FAILED = "FAILED"

class ParticipantRole(str, Enum):
    ADMIN = "ADMIN"
    MEMBER = "MEMBER"

class UserBasic(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID
    username: str
    first_name: str
    last_name: str
    profile_picture: Optional[str] = None

class MessageCreate(BaseModel):
    content: str = Field(..., min_length=1, max_length=4000)
    message_type: MessageType = MessageType.TEXT
    reply_to_id: Optional[uuid.UUID] = None

class MessageResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID
    conversation_id: uuid.UUID
    sender: UserBasic
    content: str
    message_type: MessageType
    created_at: datetime
    updated_at: datetime
    status: Optional[MessageStatusType] = None

class MessageStatusResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID
    message_id: uuid.UUID
    user_id: uuid.UUID
    status: MessageStatusType
    created_at: datetime
    updated_at: datetime

class MessageStatusUpdate(BaseModel):
    message_id: uuid.UUID
    status: MessageStatusType

class ConversationCreate(BaseModel):
    type: ConversationType
    name: Optional[str] = Field(None, max_length=100)
    participant_ids: List[uuid.UUID] = Field(..., min_items=1)

    @model_validator(mode='after')
    def validate_name_for_group(self):
        if self.type == ConversationType.GROUP and not self.name:
            raise ValueError('Group conversations must have a name')
        return self

class ConversationResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID
    type: ConversationType
    name: Optional[str] = None
    participants: List[UserBasic]
    last_message: Optional[MessageResponse] = None
    created_at: datetime
    updated_at: datetime
