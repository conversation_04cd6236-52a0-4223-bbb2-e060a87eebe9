# backend/messaging/urls.py
from django.urls import path
from . import views

urlpatterns = [
    # User-related endpoints
    path('users/search/', views.search_users, name='search-users'),
    path('users/<uuid:user_id>/', views.get_user_profile, name='get-user-profile'),

    # Conversation endpoints
    path('conversations/', views.get_conversations, name='get-conversations'),
    path('conversations/create/', views.create_conversation, name='create-conversation'),
    path('conversations/<uuid:conversation_id>/', views.get_conversation, name='get-conversation'),

    # Message endpoints
    path('conversations/<uuid:conversation_id>/messages/', views.get_messages, name='get-messages'),
    path('conversations/<uuid:conversation_id>/send/', views.send_message, name='send-message'),
]
