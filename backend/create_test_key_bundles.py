#!/usr/bin/env python3
"""
<PERSON>ript to create encryption key bundles for test users
"""
import os
import sys
import django
import base64
import secrets
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives.asymmetric.utils import encode_dss_signature
from cryptography.hazmat.backends import default_backend

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatapp.settings')
django.setup()

from django.contrib.auth import get_user_model
from encryption.models import UserKeyBundle, OneTimePreKey

User = get_user_model()

def generate_key_pair():
    """Generate an EC key pair"""
    private_key = ec.generate_private_key(ec.SECP256R1(), default_backend())
    public_key = private_key.public_key()
    return private_key, public_key

def export_public_key(public_key):
    """Export public key to base64 string"""
    public_bytes = public_key.public_bytes(
        encoding=serialization.Encoding.X962,
        format=serialization.PublicFormat.UncompressedPoint
    )
    return base64.b64encode(public_bytes).decode('utf-8')

def export_private_key(private_key):
    """Export private key to base64 string"""
    private_bytes = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )
    return base64.b64encode(private_bytes).decode('utf-8')

def sign_data(data, private_key):
    """Sign data with private key"""
    data_bytes = data.encode('utf-8') if isinstance(data, str) else data
    signature = private_key.sign(data_bytes, ec.ECDSA(hashes.SHA256()))
    return base64.b64encode(signature).decode('utf-8')

def create_key_bundle_for_user(user):
    """Create a key bundle for a user"""
    print(f"Creating key bundle for user: {user.username}")
    
    # Delete existing key bundle if it exists
    UserKeyBundle.objects.filter(user=user).delete()
    OneTimePreKey.objects.filter(user=user).delete()
    
    # Generate identity key pair
    identity_private, identity_public = generate_key_pair()
    
    # Generate signing key pair (for signing pre-keys)
    signing_private, signing_public = generate_key_pair()
    
    # Generate signed pre-key
    signed_prekey_private, signed_prekey_public = generate_key_pair()
    signed_prekey_id = secrets.randbelow(1000000)
    
    # Export public keys
    identity_public_b64 = export_public_key(identity_public)
    signed_prekey_public_b64 = export_public_key(signed_prekey_public)
    
    # Sign the pre-key with signing key
    signature = sign_data(signed_prekey_public_b64, signing_private)
    
    # Create key bundle
    key_bundle = UserKeyBundle.objects.create(
        user=user,
        identity_public_key=identity_public_b64,
        signed_prekey_id=signed_prekey_id,
        signed_prekey_public=signed_prekey_public_b64,
        signed_prekey_signature=signature
    )
    
    # Generate one-time pre-keys
    one_time_prekeys_created = 0
    for i in range(50):  # Create 50 one-time pre-keys
        otp_private, otp_public = generate_key_pair()
        otp_public_b64 = export_public_key(otp_public)
        
        OneTimePreKey.objects.create(
            user=user,
            key_id=i,
            public_key=otp_public_b64,
            is_used=False
        )
        one_time_prekeys_created += 1
    
    print(f"✅ Created key bundle for {user.username}")
    print(f"   - Identity public key: {identity_public_b64[:50]}...")
    print(f"   - Signed pre-key ID: {signed_prekey_id}")
    print(f"   - One-time pre-keys: {one_time_prekeys_created}")
    
    return key_bundle

def main():
    """Create key bundles for all test users"""
    print("🔐 Creating encryption key bundles for test users...")
    
    # Find test users
    test_users = User.objects.filter(
        email__in=[
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
    )
    
    if not test_users.exists():
        print("❌ No test users found. Please run create_test_users.py first.")
        return
    
    created_count = 0
    for user in test_users:
        try:
            create_key_bundle_for_user(user)
            created_count += 1
        except Exception as e:
            print(f"❌ Failed to create key bundle for {user.username}: {e}")
    
    print(f"\n🎉 Successfully created key bundles for {created_count} test users!")
    
    # Verify key bundles
    print("\n🔍 Verifying key bundles...")
    for user in test_users:
        try:
            key_bundle = UserKeyBundle.objects.get(user=user)
            otp_count = OneTimePreKey.objects.filter(user=user, is_used=False).count()
            print(f"✅ {user.username}: Key bundle exists, {otp_count} unused one-time pre-keys")
        except UserKeyBundle.DoesNotExist:
            print(f"❌ {user.username}: No key bundle found")

if __name__ == '__main__':
    main()
