# backend/encryption/views.py
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.utils import timezone
from pydantic_core import ValidationError
from .models import UserKeyBundle, OneTimePreKey, ConversationSession, MessageKey
from .serializers import (
    UserKeyBundleSerializer, OneTimePreKeySerializer,
    ConversationSessionSerializer, MessageKeySerializer,
    KeyBundleCreateSerializer, KeyBundleResponseSerializer
)
from .schemas import (
    UserKeyBundleCreate, KeyBundleExchangeResponse, BulkOneTimePreKeyUpload,
    ConversationSessionCreate, ConversationSessionUpdate, KeyRotationRequest
)

User = get_user_model()


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def upload_key_bundle(request):
    """Upload user's public key bundle"""
    try:
        # Validate input with Pydantic
        key_bundle_data = UserKeyBundleCreate(**request.data)

        # Delete existing key bundle if it exists
        UserKeyBundle.objects.filter(user=request.user).delete()

        # Create new key bundle
        key_bundle = UserKeyBundle.objects.create(
            user=request.user,
            identity_public_key=key_bundle_data.identity_public_key,
            signed_prekey_id=key_bundle_data.signed_prekey_id,
            signed_prekey_public=key_bundle_data.signed_prekey_public,
            signed_prekey_signature=key_bundle_data.signed_prekey_signature
        )

        # Create one-time pre-keys if provided
        created_prekeys = []
        if key_bundle_data.one_time_prekeys:
            for prekey_data in key_bundle_data.one_time_prekeys:
                # Check if key_id already exists for this user
                if not OneTimePreKey.objects.filter(
                    user=request.user,
                    key_id=prekey_data.key_id
                ).exists():
                    prekey = OneTimePreKey.objects.create(
                        user=request.user,
                        key_id=prekey_data.key_id,
                        public_key=prekey_data.public_key
                    )
                    created_prekeys.append(prekey)

        return Response({
            'message': 'Key bundle uploaded successfully',
            'key_bundle_id': str(key_bundle.id),
            'one_time_prekeys_created': len(created_prekeys)
        }, status=status.HTTP_201_CREATED)

    except ValidationError as e:
        return Response({
            'error': 'Invalid key bundle data',
            'details': e.errors()
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'error': 'Failed to upload key bundle',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_key_bundle(request, user_id):
    """Get another user's public key bundle for key exchange"""
    try:
        # Verify target user exists
        target_user = get_object_or_404(User, id=user_id)

        # Get user's key bundle
        try:
            key_bundle = UserKeyBundle.objects.get(user=target_user)
        except UserKeyBundle.DoesNotExist:
            return Response(
                {'error': 'Key bundle not found for user'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get an unused one-time pre-key
        one_time_prekey = OneTimePreKey.objects.filter(
            user=target_user,
            is_used=False
        ).first()

        # Mark one-time pre-key as used if found
        if one_time_prekey:
            one_time_prekey.is_used = True
            one_time_prekey.used_at = timezone.now()
            one_time_prekey.save()

        # Prepare response data
        response_data = {
            'identity_public_key': key_bundle.identity_public_key,
            'signed_prekey_id': key_bundle.signed_prekey_id,
            'signed_prekey_public': key_bundle.signed_prekey_public,
            'signed_prekey_signature': key_bundle.signed_prekey_signature,
        }

        if one_time_prekey:
            response_data['one_time_prekey'] = {
                'id': str(one_time_prekey.id),
                'key_id': one_time_prekey.key_id,
                'public_key': one_time_prekey.public_key,
                'is_used': one_time_prekey.is_used,
                'created_at': one_time_prekey.created_at.isoformat(),
                'used_at': one_time_prekey.used_at.isoformat() if one_time_prekey.used_at else None
            }

        return Response(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': 'Failed to retrieve key bundle',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def upload_one_time_prekeys(request):
    """Upload batch of one-time pre-keys"""
    try:
        # Validate input with Pydantic
        prekeys_data = BulkOneTimePreKeyUpload(**request.data)

        created_prekeys = []
        for prekey_data in prekeys_data.prekeys:
            # Check if key_id already exists for this user
            if not OneTimePreKey.objects.filter(
                user=request.user,
                key_id=prekey_data.key_id
            ).exists():
                prekey = OneTimePreKey.objects.create(
                    user=request.user,
                    key_id=prekey_data.key_id,
                    public_key=prekey_data.public_key
                )
                created_prekeys.append(prekey)

        return Response({
            'message': f'{len(created_prekeys)} one-time pre-keys uploaded',
            'count': len(created_prekeys),
            'skipped': len(prekeys_data.prekeys) - len(created_prekeys)
        }, status=status.HTTP_201_CREATED)

    except ValidationError as e:
        return Response({
            'error': 'Invalid pre-keys data',
            'details': e.errors()
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'error': 'Failed to upload pre-keys',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_prekey_count(request):
    """Get count of available one-time pre-keys for the current user"""
    try:
        unused_count = OneTimePreKey.objects.filter(
            user=request.user,
            is_used=False
        ).count()

        total_count = OneTimePreKey.objects.filter(
            user=request.user
        ).count()

        return Response({
            'unused_prekeys': unused_count,
            'total_prekeys': total_count,
            'usage_percentage': round((total_count - unused_count) / max(total_count, 1) * 100, 2)
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': 'Failed to get pre-key count',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def rotate_keys(request):
    """Rotate signed pre-key and optionally add new one-time pre-keys"""
    try:
        # Validate input with Pydantic
        rotation_data = KeyRotationRequest(**request.data)

        # Get existing key bundle
        try:
            key_bundle = UserKeyBundle.objects.get(user=request.user)
        except UserKeyBundle.DoesNotExist:
            return Response(
                {'error': 'No existing key bundle found. Please upload a key bundle first.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Update signed pre-key
        key_bundle.signed_prekey_id = rotation_data.new_signed_prekey_id
        key_bundle.signed_prekey_public = rotation_data.new_signed_prekey_public
        key_bundle.signed_prekey_signature = rotation_data.new_signed_prekey_signature
        key_bundle.save()

        # Add new one-time pre-keys if provided
        created_prekeys = []
        if rotation_data.additional_one_time_prekeys:
            for prekey_data in rotation_data.additional_one_time_prekeys:
                if not OneTimePreKey.objects.filter(
                    user=request.user,
                    key_id=prekey_data.key_id
                ).exists():
                    prekey = OneTimePreKey.objects.create(
                        user=request.user,
                        key_id=prekey_data.key_id,
                        public_key=prekey_data.public_key
                    )
                    created_prekeys.append(prekey)

        return Response({
            'message': 'Keys rotated successfully',
            'new_signed_prekey_id': key_bundle.signed_prekey_id,
            'new_one_time_prekeys_added': len(created_prekeys)
        }, status=status.HTTP_200_OK)

    except ValidationError as e:
        return Response({
            'error': 'Invalid rotation data',
            'details': e.errors()
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'error': 'Failed to rotate keys',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_conversation_session(request):
    """Create or update a conversation session"""
    try:
        # Validate input with Pydantic
        session_data = ConversationSessionCreate(**request.data)

        # Verify user has access to the conversation
        from messaging.models import ConversationParticipant
        participant = ConversationParticipant.objects.filter(
            conversation_id=session_data.conversation_id,
            user=request.user
        ).first()

        if not participant:
            return Response(
                {'error': 'Access denied to conversation'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Create or update session
        session, created = ConversationSession.objects.update_or_create(
            conversation_id=session_data.conversation_id,
            participant=request.user,
            defaults={
                'session_state': session_data.session_state,
                'root_key': session_data.root_key,
                'chain_key_send': session_data.chain_key_send,
                'chain_key_receive': session_data.chain_key_receive,
                'message_number_send': session_data.message_number_send,
                'message_number_receive': session_data.message_number_receive,
                'previous_chain_length': session_data.previous_chain_length,
            }
        )

        return Response({
            'message': 'Session created successfully' if created else 'Session updated successfully',
            'session_id': str(session.id),
            'created': created
        }, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)

    except ValidationError as e:
        return Response({
            'error': 'Invalid session data',
            'details': e.errors()
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'error': 'Failed to create session',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_conversation_session(request, conversation_id):
    """Get conversation session for the current user"""
    try:
        # Verify user has access to the conversation
        from messaging.models import ConversationParticipant
        participant = ConversationParticipant.objects.filter(
            conversation_id=conversation_id,
            user=request.user
        ).first()

        if not participant:
            return Response(
                {'error': 'Access denied to conversation'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get session
        try:
            session = ConversationSession.objects.get(
                conversation_id=conversation_id,
                participant=request.user
            )
        except ConversationSession.DoesNotExist:
            return Response(
                {'error': 'Session not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = ConversationSessionSerializer(session)
        return Response(serializer.data, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': 'Failed to get session',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT'])
@permission_classes([permissions.IsAuthenticated])
def update_conversation_session(request, conversation_id):
    """Update conversation session state"""
    try:
        # Validate input with Pydantic
        update_data = ConversationSessionUpdate(**request.data)

        # Get existing session
        try:
            session = ConversationSession.objects.get(
                conversation_id=conversation_id,
                participant=request.user
            )
        except ConversationSession.DoesNotExist:
            return Response(
                {'error': 'Session not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Update session fields
        update_fields = []
        for field, value in update_data.model_dump(exclude_unset=True).items():
            if value is not None:
                setattr(session, field, value)
                update_fields.append(field)

        if update_fields:
            update_fields.append('updated_at')
            session.save(update_fields=update_fields)

        return Response({
            'message': 'Session updated successfully',
            'updated_fields': update_fields[:-1]  # Exclude updated_at
        }, status=status.HTTP_200_OK)

    except ValidationError as e:
        return Response({
            'error': 'Invalid update data',
            'details': e.errors()
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'error': 'Failed to update session',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
