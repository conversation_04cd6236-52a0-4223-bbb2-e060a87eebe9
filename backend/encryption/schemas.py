# backend/encryption/schemas.py
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime
import uuid


class OneTimePreKeyBase(BaseModel):
    key_id: int = Field(..., ge=0)
    public_key: str = Field(..., min_length=1)


class OneTimePreKeyCreate(OneTimePreKeyBase):
    pass


class OneTimePreKeyResponse(OneTimePreKeyBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: uuid.UUID
    is_used: bool
    created_at: datetime
    used_at: Optional[datetime] = None


class UserKeyBundleBase(BaseModel):
    identity_public_key: str = Field(..., min_length=1)
    signed_prekey_id: int = Field(..., ge=0)
    signed_prekey_public: str = Field(..., min_length=1)
    signed_prekey_signature: str = Field(..., min_length=1)


class UserKeyBundleCreate(UserKeyBundleBase):
    one_time_prekeys: Optional[List[OneTimePreKeyCreate]] = Field(default_factory=list)


class UserKeyBundleResponse(UserKeyBundleBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime


class KeyBundleExchangeResponse(UserKeyBundleBase):
    """Response for key bundle exchange including one-time pre-key"""
    one_time_prekey: Optional[OneTimePreKeyResponse] = None


class ConversationSessionBase(BaseModel):
    session_state: Dict[str, Any]
    root_key: str = Field(..., min_length=1)
    chain_key_send: Optional[str] = None
    chain_key_receive: Optional[str] = None
    message_number_send: int = Field(default=0, ge=0)
    message_number_receive: int = Field(default=0, ge=0)
    previous_chain_length: int = Field(default=0, ge=0)


class ConversationSessionCreate(ConversationSessionBase):
    conversation_id: uuid.UUID
    participant_id: uuid.UUID


class ConversationSessionUpdate(BaseModel):
    session_state: Optional[Dict[str, Any]] = None
    root_key: Optional[str] = None
    chain_key_send: Optional[str] = None
    chain_key_receive: Optional[str] = None
    message_number_send: Optional[int] = Field(None, ge=0)
    message_number_receive: Optional[int] = Field(None, ge=0)
    previous_chain_length: Optional[int] = Field(None, ge=0)


class ConversationSessionResponse(ConversationSessionBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: uuid.UUID
    conversation_id: uuid.UUID
    participant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime


class MessageKeyBase(BaseModel):
    message_number: int = Field(..., ge=0)
    message_key: str = Field(..., min_length=1)


class MessageKeyCreate(MessageKeyBase):
    session_id: uuid.UUID


class MessageKeyResponse(MessageKeyBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: uuid.UUID
    session_id: uuid.UUID
    created_at: datetime


class EncryptedMessageData(BaseModel):
    """Schema for encrypted message content"""
    encrypted_content: str = Field(..., min_length=1)
    iv: str = Field(..., min_length=1)
    message_key_id: Optional[str] = None
    sender_ratchet_key: Optional[str] = None
    message_number: int = Field(default=0, ge=0)
    previous_chain_length: int = Field(default=0, ge=0)


class KeyExchangeRequest(BaseModel):
    """Schema for requesting key exchange with another user"""
    target_user_id: uuid.UUID


class SessionInitializationRequest(BaseModel):
    """Schema for initializing a conversation session"""
    conversation_id: uuid.UUID
    remote_key_bundle: KeyBundleExchangeResponse
    initial_session_state: Dict[str, Any]


class BulkOneTimePreKeyUpload(BaseModel):
    """Schema for uploading multiple one-time pre-keys"""
    prekeys: List[OneTimePreKeyCreate] = Field(..., min_items=1, max_items=100)


class KeyRotationRequest(BaseModel):
    """Schema for key rotation requests"""
    new_signed_prekey_id: int = Field(..., ge=0)
    new_signed_prekey_public: str = Field(..., min_length=1)
    new_signed_prekey_signature: str = Field(..., min_length=1)
    additional_one_time_prekeys: Optional[List[OneTimePreKeyCreate]] = Field(default_factory=list)
