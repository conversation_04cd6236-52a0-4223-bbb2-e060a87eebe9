# backend/encryption/serializers.py
from rest_framework import serializers
from .models import UserKeyBundle, OneTimePreKey, ConversationSession, MessageKey


class UserKeyBundleSerializer(serializers.ModelSerializer):
    """Serializer for user key bundles"""
    
    class Meta:
        model = UserKeyBundle
        fields = [
            'id', 'identity_public_key', 'signed_prekey_id', 
            'signed_prekey_public', 'signed_prekey_signature',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_identity_public_key(self, value):
        """Validate that the identity public key is properly formatted"""
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError("Identity public key cannot be empty")
        return value

    def validate_signed_prekey_public(self, value):
        """Validate that the signed pre-key is properly formatted"""
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError("Signed pre-key cannot be empty")
        return value

    def validate_signed_prekey_signature(self, value):
        """Validate that the signature is properly formatted"""
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError("Signature cannot be empty")
        return value


class OneTimePreKeySerializer(serializers.ModelSerializer):
    """Serializer for one-time pre-keys"""
    
    class Meta:
        model = OneTimePreKey
        fields = [
            'id', 'key_id', 'public_key', 'is_used', 
            'created_at', 'used_at'
        ]
        read_only_fields = ['id', 'is_used', 'created_at', 'used_at']

    def validate_public_key(self, value):
        """Validate that the public key is properly formatted"""
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError("Public key cannot be empty")
        return value

    def validate_key_id(self, value):
        """Validate that the key ID is positive"""
        if value < 0:
            raise serializers.ValidationError("Key ID must be non-negative")
        return value


class ConversationSessionSerializer(serializers.ModelSerializer):
    """Serializer for conversation sessions"""
    
    class Meta:
        model = ConversationSession
        fields = [
            'id', 'conversation', 'participant', 'session_state',
            'root_key', 'chain_key_send', 'chain_key_receive',
            'message_number_send', 'message_number_receive',
            'previous_chain_length', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_session_state(self, value):
        """Validate that session state is a valid JSON object"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("Session state must be a JSON object")
        return value


class MessageKeySerializer(serializers.ModelSerializer):
    """Serializer for message keys"""
    
    class Meta:
        model = MessageKey
        fields = [
            'id', 'session', 'message_number', 'message_key', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']

    def validate_message_number(self, value):
        """Validate that message number is non-negative"""
        if value < 0:
            raise serializers.ValidationError("Message number must be non-negative")
        return value

    def validate_message_key(self, value):
        """Validate that the message key is properly formatted"""
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError("Message key cannot be empty")
        return value


class KeyBundleCreateSerializer(serializers.Serializer):
    """Serializer for creating a complete key bundle with one-time pre-keys"""
    identity_public_key = serializers.CharField()
    signed_prekey_id = serializers.IntegerField()
    signed_prekey_public = serializers.CharField()
    signed_prekey_signature = serializers.CharField()
    one_time_prekeys = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True
    )

    def validate_one_time_prekeys(self, value):
        """Validate one-time pre-keys format"""
        for prekey in value:
            if 'key_id' not in prekey or 'public_key' not in prekey:
                raise serializers.ValidationError(
                    "Each one-time pre-key must have 'key_id' and 'public_key'"
                )
            if not isinstance(prekey['key_id'], int) or prekey['key_id'] < 0:
                raise serializers.ValidationError("Key ID must be a non-negative integer")
            if not prekey['public_key'] or len(prekey['public_key'].strip()) == 0:
                raise serializers.ValidationError("Public key cannot be empty")
        return value


class KeyBundleResponseSerializer(serializers.Serializer):
    """Serializer for key bundle responses"""
    identity_public_key = serializers.CharField()
    signed_prekey_id = serializers.IntegerField()
    signed_prekey_public = serializers.CharField()
    signed_prekey_signature = serializers.CharField()
    one_time_prekey = OneTimePreKeySerializer(required=False, allow_null=True)
