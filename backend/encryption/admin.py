# backend/encryption/admin.py
from django.contrib import admin
from .models import UserKeyBundle, OneTimePreKey, ConversationSession, MessageKey


@admin.register(UserKeyBundle)
class UserKeyBundleAdmin(admin.ModelAdmin):
    list_display = ['user', 'signed_prekey_id', 'created_at', 'updated_at']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['id', 'created_at', 'updated_at']

    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('Key Information', {
            'fields': ('identity_public_key', 'signed_prekey_id', 'signed_prekey_public', 'signed_prekey_signature')
        }),
        ('Metadata', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(OneTimePreKey)
class OneTimePreKeyAdmin(admin.ModelAdmin):
    list_display = ['user', 'key_id', 'is_used', 'created_at', 'used_at']
    list_filter = ['is_used', 'created_at', 'used_at']
    search_fields = ['user__username', 'user__email', 'key_id']
    readonly_fields = ['id', 'created_at', 'used_at']

    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('Key Information', {
            'fields': ('key_id', 'public_key', 'is_used')
        }),
        ('Metadata', {
            'fields': ('id', 'created_at', 'used_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ConversationSession)
class ConversationSessionAdmin(admin.ModelAdmin):
    list_display = ['participant', 'conversation', 'message_number_send', 'message_number_receive', 'created_at']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['participant__username', 'conversation__id']
    readonly_fields = ['id', 'created_at', 'updated_at']

    fieldsets = (
        ('Session Information', {
            'fields': ('conversation', 'participant')
        }),
        ('Encryption State', {
            'fields': ('session_state', 'root_key', 'chain_key_send', 'chain_key_receive')
        }),
        ('Message Counters', {
            'fields': ('message_number_send', 'message_number_receive', 'previous_chain_length')
        }),
        ('Metadata', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(MessageKey)
class MessageKeyAdmin(admin.ModelAdmin):
    list_display = ['session', 'message_number', 'created_at']
    list_filter = ['created_at']
    search_fields = ['session__participant__username', 'message_number']
    readonly_fields = ['id', 'created_at']

    fieldsets = (
        ('Key Information', {
            'fields': ('session', 'message_number', 'message_key')
        }),
        ('Metadata', {
            'fields': ('id', 'created_at'),
            'classes': ('collapse',)
        }),
    )
