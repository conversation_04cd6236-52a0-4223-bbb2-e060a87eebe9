# backend/encryption/models.py
import uuid
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class UserKeyBundle(models.Model):
    """Stores user's public keys for key exchange"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='key_bundle')
    identity_public_key = models.TextField()  # Base64 encoded public key
    signed_prekey_id = models.IntegerField()
    signed_prekey_public = models.TextField()  # Base64 encoded
    signed_prekey_signature = models.TextField()  # Base64 encoded
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_key_bundles'

    def __str__(self):
        return f"Key bundle for {self.user.username}"

class OneTimePreKey(models.Model):
    """One-time pre-keys for perfect forward secrecy"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='one_time_prekeys')
    key_id = models.IntegerField()
    public_key = models.TextField()  # Base64 encoded
    is_used = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    used_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'one_time_prekeys'
        unique_together = ['user', 'key_id']

    def __str__(self):
        return f"One-time pre-key {self.key_id} for {self.user.username}"

class ConversationSession(models.Model):
    """Stores session state for each participant in a conversation"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey('messaging.Conversation', on_delete=models.CASCADE, related_name='sessions')
    participant = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversation_sessions')
    session_state = models.JSONField()  # Encrypted session state
    root_key = models.TextField()  # Base64 encoded, encrypted with user's key
    chain_key_send = models.TextField(null=True, blank=True)  # Base64 encoded
    chain_key_receive = models.TextField(null=True, blank=True)  # Base64 encoded
    message_number_send = models.IntegerField(default=0)
    message_number_receive = models.IntegerField(default=0)
    previous_chain_length = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'conversation_sessions'
        unique_together = ['conversation', 'participant']

    def __str__(self):
        return f"Session for {self.participant.username} in conversation {self.conversation.id}"

class MessageKey(models.Model):
    """Stores message keys for delayed decryption"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session = models.ForeignKey(ConversationSession, on_delete=models.CASCADE, related_name='message_keys')
    message_number = models.IntegerField()
    message_key = models.TextField()  # Base64 encoded, encrypted
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'message_keys'
        unique_together = ['session', 'message_number']

    def __str__(self):
        return f"Message key {self.message_number} for session {self.session.id}"
