# Generated by Django 5.2.4 on 2025-08-03 07:10

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('messaging', '0003_message_encrypted_content_message_iv_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ConversationSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_state', models.J<PERSON>NField()),
                ('root_key', models.TextField()),
                ('chain_key_send', models.TextField(blank=True, null=True)),
                ('chain_key_receive', models.TextField(blank=True, null=True)),
                ('message_number_send', models.IntegerField(default=0)),
                ('message_number_receive', models.IntegerField(default=0)),
                ('previous_chain_length', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='messaging.conversation')),
                ('participant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversation_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'conversation_sessions',
                'unique_together': {('conversation', 'participant')},
            },
        ),
        migrations.CreateModel(
            name='UserKeyBundle',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('identity_public_key', models.TextField()),
                ('signed_prekey_id', models.IntegerField()),
                ('signed_prekey_public', models.TextField()),
                ('signed_prekey_signature', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='key_bundle', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'user_key_bundles',
            },
        ),
        migrations.CreateModel(
            name='MessageKey',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('message_number', models.IntegerField()),
                ('message_key', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='message_keys', to='encryption.conversationsession')),
            ],
            options={
                'db_table': 'message_keys',
                'unique_together': {('session', 'message_number')},
            },
        ),
        migrations.CreateModel(
            name='OneTimePreKey',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('key_id', models.IntegerField()),
                ('public_key', models.TextField()),
                ('is_used', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('used_at', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='one_time_prekeys', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'one_time_prekeys',
                'unique_together': {('user', 'key_id')},
            },
        ),
    ]
