# backend/accounts/management/commands/create_test_users.py
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction

User = get_user_model()

class Command(BaseCommand):
    help = 'Create test users for E2E testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing test users before creating new ones',
        )

    def handle(self, *args, **options):
        test_users = [
            {
                'username': 'testuser1',
                'email': '<EMAIL>',
                'password': 'testpass123',
                'first_name': 'Test',
                'last_name': 'User One',
            },
            {
                'username': 'testuser2',
                'email': '<EMAIL>',
                'password': 'testpass123',
                'first_name': 'Test',
                'last_name': 'User Two',
            },
            {
                'username': 'alice',
                'email': '<EMAIL>',
                'password': 'alicepass123',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
            },
            {
                'username': 'bob',
                'email': '<EMAIL>',
                'password': 'bobpass123',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
            },
        ]

        if options['clear']:
            self.stdout.write('Clearing existing test users...')
            User.objects.filter(username__in=[user['username'] for user in test_users]).delete()
            self.stdout.write(self.style.SUCCESS('Existing test users cleared.'))

        created_users = []
        
        with transaction.atomic():
            for user_data in test_users:
                username = user_data['username']
                
                # Check if user already exists
                if User.objects.filter(username=username).exists():
                    self.stdout.write(
                        self.style.WARNING(f'User {username} already exists, skipping...')
                    )
                    continue
                
                # Create user
                user = User.objects.create_user(
                    username=user_data['username'],
                    email=user_data['email'],
                    password=user_data['password'],
                    first_name=user_data['first_name'],
                    last_name=user_data['last_name'],
                )
                
                created_users.append(user)
                self.stdout.write(
                    self.style.SUCCESS(f'Created user: {username} ({user.email})')
                )

        self.stdout.write(
            self.style.SUCCESS(f'\nSuccessfully created {len(created_users)} test users.')
        )
        
        if created_users:
            self.stdout.write('\nTest user credentials:')
            for user_data in test_users:
                if any(u.username == user_data['username'] for u in created_users):
                    self.stdout.write(f"  Username: {user_data['username']}")
                    self.stdout.write(f"  Password: {user_data['password']}")
                    self.stdout.write(f"  Email: {user_data['email']}")
                    self.stdout.write('')
            
            self.stdout.write(
                self.style.SUCCESS(
                    'Test users are ready for E2E testing!\n'
                    'You can now log in with any of these credentials.'
                )
            )
