#!/usr/bin/env python
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatapp.settings')
django.setup()

from users.models import User

def create_test_users():
    # Create test users for E2E testing
    users_data = [
        {
            'email': '<EMAIL>',
            'username': 'testuser1',
            'password': 'TestPassword123!',
            'first_name': 'Test',
            'last_name': 'User1'
        },
        {
            'email': '<EMAIL>',
            'username': 'testuser2',
            'password': 'TestPassword123!',
            'first_name': 'Test',
            'last_name': 'User2'
        },
        {
            'email': '<EMAIL>',
            'username': 'testuser3',
            'password': 'TestPassword123!',
            'first_name': 'Test',
            'last_name': 'User3'
        },
        {
            'email': '<EMAIL>',
            'username': 'admintest',
            'password': 'AdminPassword123!',
            'first_name': 'Admin',
            'last_name': 'Test'
        },
        # Keep original test users for backward compatibility
        {
            'email': '<EMAIL>',
            'username': 'alice',
            'password': 'password123',
            'first_name': 'Alice',
            'last_name': 'Johnson'
        },
        {
            'email': '<EMAIL>',
            'username': 'bob',
            'password': 'password123',
            'first_name': 'Bob',
            'last_name': 'Smith'
        },
        {
            'email': '<EMAIL>',
            'username': 'charlie',
            'password': 'password123',
            'first_name': 'Charlie',
            'last_name': 'Brown'
        }
    ]

    for user_data in users_data:
        user, created = User.objects.get_or_create(
            email=user_data['email'],
            defaults={
                'username': user_data['username'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name']
            }
        )
        if created:
            user.set_password(user_data['password'])
            user.save()
            print(f"Created user: {user.email}")
        else:
            print(f"User already exists: {user.email}")

if __name__ == '__main__':
    create_test_users()
    print("Test users created successfully!")
