#!/usr/bin/env python3
import requests
import time

# Test the fixes for draft conversations and message deduplication
BASE_URL = "http://localhost:8000"

def login_user(email, password):
    """Login and return token and user"""
    login_data = {"email": email, "password": password}
    response = requests.post(f"{BASE_URL}/api/auth/login/", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            return data['data']['tokens']['access'], data['data']['user']
    return None, None

def test_conversation_creation_fix():
    print("🔧 === TESTING DRAFT CONVERSATION FIX ===\n")
    
    # Login as Alice
    print("1. Logging in as Alice...")
    alice_token, alice_user = login_user("<EMAIL>", "password123")
    if not alice_token:
        print("❌ Login failed")
        return
    print("✅ Login successful\n")
    
    headers = {"Authorization": f"Bearer {alice_token}"}
    
    # Check initial conversation count
    print("2. Checking initial conversation count...")
    response = requests.get(f"{BASE_URL}/api/messaging/conversations/", headers=headers)
    initial_count = response.json().get('count', 0) if response.status_code == 200 else 0
    print(f"   Initial conversations: {initial_count}\n")
    
    # Search for Charlie (this should NOT create a conversation yet)
    print("3. Testing user search (should NOT create conversation)...")
    response = requests.get(f"{BASE_URL}/api/messaging/users/search/?q=charlie", headers=headers)
    if response.status_code == 200:
        users = response.json()['data']
        if users:
            charlie = users[0]
            print(f"✅ Found user: {charlie['full_name']} (@{charlie['username']})")
        else:
            print("❌ No users found")
            return
    else:
        print("❌ User search failed")
        return
    
    # Check conversation count again (should be the same)
    print("4. Verifying no conversation created yet...")
    response = requests.get(f"{BASE_URL}/api/messaging/conversations/", headers=headers)
    current_count = response.json().get('count', 0) if response.status_code == 200 else 0
    
    if current_count == initial_count:
        print("✅ No premature conversation creation - fix working!")
    else:
        print(f"❌ Conversation was created prematurely: {current_count} vs {initial_count}")
        return
    
    print("\n🎉 Draft conversation fix verified!")
    print("\n📱 Frontend Testing Instructions:")
    print("1. Open: http://localhost:3002")
    print("2. Login as: <EMAIL> / password123")
    print("3. Click 'New Chat' button")
    print("4. Search for 'charlie' and click 'Chat'")
    print("5. Verify conversation appears in sidebar as 'Start a conversation...'")
    print("6. Send a message - conversation should be created in database")
    print("7. Check for NO duplicate messages in the chat")

def test_message_deduplication():
    print("\n🔧 === TESTING MESSAGE DEDUPLICATION FIX ===\n")
    
    print("This fix prevents duplicate messages during optimistic updates.")
    print("The fix works by:")
    print("1. Storing tempId mapping when message_sent event is received")
    print("2. Using tempId to replace optimistic message when new_message arrives")
    print("3. Preventing duplicate messages with same ID")
    
    print("\n📱 To test message deduplication:")
    print("1. Open two browser windows")
    print("2. Login as different users (alice and charlie)")
    print("3. Start a conversation and send messages")
    print("4. Verify each message appears only ONCE (no duplicates)")
    print("5. Check that optimistic messages are replaced, not duplicated")

if __name__ == "__main__":
    test_conversation_creation_fix()
    test_message_deduplication()
