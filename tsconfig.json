{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "noEmit": true, "types": ["node", "@playwright/test"]}, "include": ["e2e/**/*", "playwright.config.ts"], "exclude": ["node_modules", "e2e/test-results", "e2e/reports"]}