{"config": {"configFile": "/home/<USER>/chatapp/playwright.config.ts", "rootDir": "/home/<USER>/chatapp/e2e/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "e2e/reports/html"}], ["json", {"outputFile": "e2e/reports/results.json"}], ["junit", {"outputFile": "e2e/reports/results.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 6, "webServer": null}, "suites": [{"title": "messaging/encryption-database-issues-reproduction.spec.ts", "file": "messaging/encryption-database-issues-reproduction.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Encryption and Database Issues Reproduction", "file": "messaging/encryption-database-issues-reproduction.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should reproduce encryption session failures and database schema errors", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "timedOut", "duration": 30035, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while setting up \"page\".\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while setting up \"page\".\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded while setting up \"page\".\u001b[39m"}, {"message": "Error: browserContext.newPage: Test timeout of 30000ms exceeded."}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-08T06:43:30.283Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "5bd82ba1e58d3d40d39a-334f2a0fcffc376d36e7", "file": "messaging/encryption-database-issues-reproduction.spec.ts", "line": 4, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-08-08T06:43:29.580Z", "duration": 32026.999999999996, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}