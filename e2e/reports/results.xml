<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="32.026999999999994">
<testsuite name="messaging/encryption-database-issues-reproduction.spec.ts" timestamp="2025-08-08T06:43:29.668Z" hostname="chromium" tests="1" failures="1" skipped="0" time="30.035" errors="0">
<testcase name="Encryption and Database Issues Reproduction › should reproduce encryption session failures and database schema errors" classname="messaging/encryption-database-issues-reproduction.spec.ts" time="30.035">
<failure message="encryption-database-issues-reproduction.spec.ts:4:7 should reproduce encryption session failures and database schema errors" type="FAILURE">
<![CDATA[  [chromium] › messaging/encryption-database-issues-reproduction.spec.ts:4:7 › Encryption and Database Issues Reproduction › should reproduce encryption session failures and database schema errors 

    Test timeout of 30000ms exceeded while setting up "page".

    Error: browserContext.newPage: Test timeout of 30000ms exceeded.
]]>
</failure>
</testcase>
</testsuite>
</testsuites>