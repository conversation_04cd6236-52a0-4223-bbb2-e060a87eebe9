# End-to-End Testing with <PERSON><PERSON>

## Overview

This directory contains comprehensive E2E tests for the Chat Application using <PERSON>wright. The tests cover all user workflows and interactions implemented in Phase 2 of the project.

## Test Structure

```
e2e/
├── README.md                    # This file
├── playwright.config.ts         # Playwright configuration
├── user-stories/               # Detailed user stories for E2E testing
│   ├── authentication.md       # Authentication workflows
│   ├── messaging.md            # Core messaging features
│   ├── real-time.md            # Real-time communication
│   └── error-handling.md       # Error scenarios and edge cases
├── tests/                      # Test files
│   ├── auth/                   # Authentication tests
│   ├── messaging/              # Messaging functionality tests
│   ├── real-time/              # Real-time features tests
│   └── error-handling/         # Error and edge case tests
├── fixtures/                   # Test fixtures and data
├── page-objects/               # Page Object Models
├── utils/                      # Test utilities and helpers
└── reports/                    # Test reports and screenshots
```

## Features Covered

Based on the Phase 2 implementation, the E2E tests cover:

### 1. Authentication System
- User registration with validation
- User login with email/password
- JWT token management
- Session persistence
- Logout functionality
- Protected route access

### 2. Core Messaging
- Conversation creation (direct and group)
- Message sending and receiving
- Message history and pagination
- Conversation list management
- User search and selection

### 3. Real-time Communication
- Live message delivery via Socket.io
- Typing indicators
- User online/offline status
- Connection status management
- Automatic reconnection

### 4. UI Components
- Responsive design testing
- Form validation
- Loading states
- Error handling
- Navigation flows

## Test Categories

### Functional Tests
- User authentication flows
- Message CRUD operations
- Conversation management
- Real-time synchronization

### Integration Tests
- Frontend-Backend API integration
- Socket.io real-time communication
- Database state consistency
- Cross-component interactions

### User Experience Tests
- Navigation and routing
- Form interactions
- Responsive behavior
- Error recovery

### Performance Tests
- Message loading performance
- Real-time latency
- Connection handling
- Memory usage

## Browser Support

Tests are configured to run on:
- Chromium (Chrome/Edge)
- Firefox
- WebKit (Safari)

## Test Data Management

- Test users are created and cleaned up automatically
- Isolated test environments
- Database seeding for consistent test data
- Mock data for offline testing

## Getting Started

### Prerequisites

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Install Playwright Browsers**
   ```bash
   npx playwright install
   npx playwright install-deps  # Install system dependencies
   ```

3. **Start Application Services**

   Before running E2E tests, ensure all services are running:

   **Terminal 1 - Backend (Django)**
   ```bash
   cd backend
   python manage.py runserver 8000
   ```

   **Terminal 2 - Socket Server (Node.js)**
   ```bash
   cd socket-server
   npm run dev
   ```

   **Terminal 3 - Frontend (React)**
   ```bash
   cd frontend
   npm run dev
   ```

### Running Tests

1. **Run All E2E Tests**
   ```bash
   npm run test:e2e
   ```

2. **Run Tests in Headed Mode (with browser UI)**
   ```bash
   npm run test:e2e:headed
   ```

3. **Run Tests in Debug Mode**
   ```bash
   npm run test:e2e:debug
   ```

4. **Run Tests with UI Mode**
   ```bash
   npm run test:e2e:ui
   ```

5. **Run Specific Test File**
   ```bash
   npm run test:e2e -- e2e/tests/auth/login.spec.ts
   ```

6. **Run Tests for Specific Browser**
   ```bash
   npm run test:e2e -- --project=chromium
   npm run test:e2e -- --project=firefox
   npm run test:e2e -- --project=webkit
   ```

### Viewing Test Reports

1. **View HTML Report**
   ```bash
   npm run test:e2e:report
   ```

2. **View Test Results**
   - HTML reports: `e2e/reports/html/`
   - JSON reports: `e2e/reports/results.json`
   - JUnit reports: `e2e/reports/results.xml`

### Test Development

1. **Create New Test File**
   ```typescript
   import { test, expect } from '../../fixtures/test-fixtures';

   test.describe('Feature Name', () => {
     test('should do something', async ({ page }) => {
       // Test implementation
     });
   });
   ```

2. **Use Page Object Models**
   ```typescript
   import { test, expect } from '../../fixtures/test-fixtures';

   test('should login successfully', async ({ loginPage }) => {
     await loginPage.goto();
     await loginPage.login('<EMAIL>', 'password');
     await loginPage.waitForLoginSuccess();
   });
   ```

3. **Multi-User Testing**
   ```typescript
   test('should send message between users', async ({ multiUserPages }) => {
     const { user1, user2 } = multiUserPages;

     await user1.dashboard.sendMessage('Hello!');
     await user2.dashboard.waitForNewMessage();
   });
   ```

### Configuration

The E2E tests are configured in `playwright.config.ts`:

- **Test Directory**: `./e2e/tests`
- **Base URL**: `http://localhost:5173`
- **Browsers**: Chrome, Firefox, Safari, Mobile Chrome, Mobile Safari
- **Reports**: HTML, JSON, JUnit
- **Artifacts**: Screenshots, videos, traces

### Troubleshooting

1. **Services Not Running**
   ```
   Error: connect ECONNREFUSED 127.0.0.1:8000
   ```
   Solution: Ensure all services (Django, Node.js, React) are running

2. **Browser Installation Issues**
   ```bash
   npx playwright install-deps
   sudo apt-get install libnspr4 libnss3 libasound2t64
   ```

3. **Test Timeouts**
   - Increase timeout in `playwright.config.ts`
   - Check network connectivity
   - Verify service performance

4. **Authentication Issues**
   - Verify test users exist in database
   - Check authentication tokens
   - Review auth state files in `e2e/fixtures/`

## CI/CD Integration

Tests are designed to run in CI/CD pipelines with:

### GitHub Actions Example
```yaml
name: E2E Tests
on: [push, pull_request]

jobs:
  e2e:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm install

      - name: Install Playwright
        run: npx playwright install --with-deps

      - name: Start services
        run: |
          # Start backend, socket server, and frontend
          # Use background processes or docker-compose

      - name: Run E2E tests
        run: npm run test:e2e

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: e2e/reports/
```

### Features
- **Headless Execution**: Runs without GUI in CI
- **Parallel Testing**: Multiple browsers simultaneously
- **Artifact Collection**: Screenshots, videos, traces
- **Test Reporting**: Multiple formats for CI integration
- **Retry Logic**: Automatic retry on flaky tests
- **Cross-Browser**: Chrome, Firefox, Safari support
