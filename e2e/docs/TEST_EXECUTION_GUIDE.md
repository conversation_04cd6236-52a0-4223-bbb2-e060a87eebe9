# E2E Test Execution Guide

## Overview

This guide provides comprehensive instructions for executing End-to-End (E2E) tests for the Chat Application using Playwright.

## Quick Start

### Prerequisites

1. **Install Dependencies**
   ```bash
   npm install
   npx playwright install --with-deps
   ```

2. **Start Services**
   ```bash
   # Terminal 1 - Backend
   cd backend && python manage.py runserver 8000
   
   # Terminal 2 - Socket Server
   cd socket-server && npm run dev
   
   # Terminal 3 - Frontend
   cd frontend && npm run dev
   ```

3. **Run Tests**
   ```bash
   npm run test:e2e
   ```

## Test Execution Methods

### 1. NPM Scripts (Recommended)

```bash
# Run all tests
npm run test:e2e

# Run with browser UI (see tests execute)
npm run test:e2e:headed

# Debug mode (step through tests)
npm run test:e2e:debug

# Interactive UI mode
npm run test:e2e:ui

# View test reports
npm run test:e2e:report
```

### 2. Custom Script (Advanced)

```bash
# Basic execution
./e2e/scripts/run-tests.sh

# Run in Firefox with headed mode
./e2e/scripts/run-tests.sh -b firefox -h

# Debug specific tests
./e2e/scripts/run-tests.sh -d -g "login"

# Run all browsers serially
./e2e/scripts/run-tests.sh -b all --serial

# Interactive UI mode
./e2e/scripts/run-tests.sh -u

# Auto-start services
./e2e/scripts/run-tests.sh --start-services
```

### 3. Direct Playwright Commands

```bash
# Run all tests
npx playwright test

# Run specific browser
npx playwright test --project=chromium
npx playwright test --project=firefox
npx playwright test --project=webkit

# Run specific test file
npx playwright test e2e/tests/auth/login.spec.ts

# Run with specific options
npx playwright test --headed --debug --workers=1
```

## Test Categories

### Authentication Tests
```bash
# All authentication tests
npx playwright test e2e/tests/auth/

# Specific auth tests
npx playwright test e2e/tests/auth/login.spec.ts
npx playwright test e2e/tests/auth/register.spec.ts
```

### Real-time Communication Tests
```bash
# All real-time tests
npx playwright test e2e/tests/real-time/

# Specific real-time tests
npx playwright test e2e/tests/real-time/message-delivery.spec.ts
npx playwright test e2e/tests/real-time/typing-indicators.spec.ts
npx playwright test e2e/tests/real-time/connection-management.spec.ts
```

### Error Handling Tests
```bash
# All error handling tests
npx playwright test e2e/tests/error-handling/

# Specific error tests
npx playwright test e2e/tests/error-handling/network-errors.spec.ts
npx playwright test e2e/tests/error-handling/server-errors.spec.ts
npx playwright test e2e/tests/error-handling/validation-errors.spec.ts
```

### Cross-browser Tests
```bash
# All cross-browser tests
npx playwright test e2e/tests/cross-browser/

# Specific compatibility tests
npx playwright test e2e/tests/cross-browser/browser-compatibility.spec.ts
npx playwright test e2e/tests/cross-browser/feature-compatibility.spec.ts
```

## Browser-Specific Execution

### Single Browser
```bash
# Chrome/Chromium
npx playwright test --project=chromium

# Firefox
npx playwright test --project=firefox

# Safari/WebKit
npx playwright test --project=webkit

# Mobile Chrome
npx playwright test --project="Mobile Chrome"

# Mobile Safari
npx playwright test --project="Mobile Safari"
```

### Multiple Browsers
```bash
# All desktop browsers
npx playwright test --project=chromium --project=firefox --project=webkit

# All mobile browsers
npx playwright test --project="Mobile Chrome" --project="Mobile Safari"
```

## Test Filtering

### By Test Name
```bash
# Tests containing "login"
npx playwright test --grep="login"

# Tests containing "message" or "chat"
npx playwright test --grep="message|chat"

# Exclude specific tests
npx playwright test --grep-invert="slow"
```

### By File Pattern
```bash
# All auth tests
npx playwright test e2e/tests/auth/

# Specific file pattern
npx playwright test e2e/tests/**/*login*
```

### By Tags (if implemented)
```bash
# Tests tagged as @smoke
npx playwright test --grep="@smoke"

# Tests tagged as @critical
npx playwright test --grep="@critical"
```

## Execution Modes

### Headed Mode (Visual)
```bash
# See browser windows during test execution
npx playwright test --headed

# Slow motion for better visibility
npx playwright test --headed --slowMo=1000
```

### Debug Mode
```bash
# Step through tests with debugger
npx playwright test --debug

# Debug specific test
npx playwright test e2e/tests/auth/login.spec.ts --debug
```

### UI Mode (Interactive)
```bash
# Interactive test runner
npx playwright test --ui

# Watch mode (re-run on file changes)
npx playwright test --ui --watch
```

## Parallel Execution

### Control Workers
```bash
# Run serially (1 worker)
npx playwright test --workers=1

# Run with specific number of workers
npx playwright test --workers=4

# Fully parallel (default)
npx playwright test --workers=100%
```

### Disable Parallelization
```bash
# Force serial execution
npx playwright test --workers=1 --fullyParallel=false
```

## Test Reporting

### Built-in Reporters
```bash
# HTML report (default)
npx playwright test --reporter=html

# JSON report
npx playwright test --reporter=json

# JUnit report
npx playwright test --reporter=junit

# List reporter (console output)
npx playwright test --reporter=list

# Multiple reporters
npx playwright test --reporter=html,json,junit
```

### View Reports
```bash
# Open HTML report
npx playwright show-report

# Open specific report
npx playwright show-report e2e/reports/html
```

## Environment Configuration

### Test Environment Variables
```bash
# Set base URL
BASE_URL=http://localhost:3000 npx playwright test

# Set test timeout
TIMEOUT=60000 npx playwright test

# Enable debug logging
DEBUG=pw:api npx playwright test
```

### Configuration Files
```bash
# Use specific config file
npx playwright test --config=playwright.ci.config.ts

# Override config options
npx playwright test --timeout=30000 --retries=2
```

## Troubleshooting

### Common Issues

1. **Services Not Running**
   ```bash
   # Check service status
   curl http://localhost:8000  # Backend
   curl http://localhost:7000  # Socket Server
   curl http://localhost:5173  # Frontend
   ```

2. **Browser Installation Issues**
   ```bash
   # Reinstall browsers
   npx playwright install --force
   
   # Install system dependencies
   npx playwright install-deps
   ```

3. **Test Timeouts**
   ```bash
   # Increase timeout
   npx playwright test --timeout=60000
   
   # Increase global timeout
   npx playwright test --globalTimeout=300000
   ```

4. **Flaky Tests**
   ```bash
   # Enable retries
   npx playwright test --retries=3
   
   # Run specific test multiple times
   npx playwright test e2e/tests/auth/login.spec.ts --repeat-each=5
   ```

### Debug Information
```bash
# Enable debug logging
DEBUG=pw:api npx playwright test

# Verbose output
npx playwright test --verbose

# Trace collection
npx playwright test --trace=on
```

### Performance Issues
```bash
# Reduce parallelism
npx playwright test --workers=1

# Disable video recording
npx playwright test --video=off

# Disable screenshots
npx playwright test --screenshot=off
```

## CI/CD Integration

### GitHub Actions
The project includes a comprehensive GitHub Actions workflow at `.github/workflows/e2e-tests.yml` that:

- Runs tests on multiple browsers
- Sets up all required services
- Collects test artifacts
- Generates test reports
- Comments on pull requests with results

### Local CI Simulation
```bash
# Run tests as they would run in CI
CI=true npx playwright test --workers=1 --retries=2

# Generate CI-style reports
npx playwright test --reporter=html,json,junit
```

## Best Practices

### Test Organization
- Group related tests in describe blocks
- Use descriptive test names
- Keep tests independent and isolated
- Use proper setup and teardown

### Performance
- Run tests in parallel when possible
- Use appropriate timeouts
- Clean up resources after tests
- Avoid unnecessary waits

### Debugging
- Use headed mode for visual debugging
- Enable trace collection for failed tests
- Use console.log sparingly
- Leverage Playwright's debugging tools

### Maintenance
- Keep tests up to date with application changes
- Review and update selectors regularly
- Monitor test execution times
- Address flaky tests promptly

## Advanced Usage

### Custom Test Data
```bash
# Run with custom test data
TEST_DATA_FILE=custom-data.json npx playwright test
```

### Environment-Specific Tests
```bash
# Run staging environment tests
ENV=staging npx playwright test

# Run production smoke tests
ENV=production npx playwright test --grep="@smoke"
```

### Performance Testing
```bash
# Run performance-focused tests
npx playwright test --grep="performance"

# Collect performance metrics
COLLECT_METRICS=true npx playwright test
```

## Support and Resources

- **Playwright Documentation**: https://playwright.dev/docs/
- **Project README**: `e2e/README.md`
- **User Stories**: `e2e/user-stories/`
- **Test Examples**: `e2e/tests/`
- **Validation Script**: `./validate-e2e-setup.sh`

For additional help or questions, refer to the project documentation or contact the development team.
