import { chromium, FullConfig } from '@playwright/test';
import { TestDataManager } from './test-data-manager';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for E2E tests...');

  // Wait for services to be ready
  await waitForServices();

  // Initialize test data
  const testDataManager = new TestDataManager();
  await testDataManager.setupTestData();

  // Create authentication state for tests
  await createAuthState();

  console.log('✅ Global setup completed successfully');
}

async function waitForServices() {
  console.log('⏳ Waiting for services to be ready...');
  
  const services = [
    { name: 'Django Backend', url: 'http://localhost:8000/api/auth/test/', timeout: 60000 },
    { name: 'Socket Server', url: 'http://localhost:7000/health', timeout: 60000 },
    { name: 'Frontend', url: 'http://localhost:5173', timeout: 60000 },
  ];

  for (const service of services) {
    await waitForService(service.name, service.url, service.timeout);
  }
}

async function waitForService(name: string, url: string, timeout: number) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      const response = await fetch(url);
      if (response.ok || response.status === 404) { // 404 is ok for some endpoints
        console.log(`✅ ${name} is ready`);
        return;
      }
    } catch (error) {
      // Service not ready yet, continue waiting
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  throw new Error(`❌ ${name} failed to start within ${timeout}ms`);
}

async function createAuthState() {
  console.log('🔐 Creating authentication state...');
  
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Navigate to login page
    await page.goto('http://localhost:5173/login');
    
    // Create test user if needed and login
    const testDataManager = new TestDataManager();
    const testUser = await testDataManager.getTestUser('primary');
    
    // Fill login form
    await page.fill('[data-testid="email-input"]', testUser.email);
    await page.fill('[data-testid="password-input"]', testUser.password);
    await page.click('[data-testid="login-button"]');
    
    // Wait for successful login
    await page.waitForURL('**/dashboard');
    
    // Save authentication state
    await context.storageState({ path: 'e2e/fixtures/auth-state.json' });
    
    console.log('✅ Authentication state created');
  } catch (error) {
    console.error('❌ Failed to create authentication state:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

export default globalSetup;
