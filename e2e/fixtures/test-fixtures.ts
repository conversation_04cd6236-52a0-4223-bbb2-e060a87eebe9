import { test as base, <PERSON><PERSON><PERSON>, <PERSON><PERSON>erContext, Page, expect } from '@playwright/test';
import { LoginPage } from '../page-objects/LoginPage';
import { RegisterPage } from '../page-objects/RegisterPage';
import { DashboardPage } from '../page-objects/DashboardPage';
import { TestDataManager } from '../utils/test-data-manager';

// Define custom fixtures
type TestFixtures = {
  loginPage: LoginPage;
  registerPage: RegisterPage;
  dashboardPage: DashboardPage;
  testDataManager: TestDataManager;
  authenticatedContext: BrowserContext;
  secondUserContext: BrowserContext;
  thirdUserContext: BrowserContext;
  multiUserPages: {
    user1: { page: Page; dashboard: DashboardPage };
    user2: { page: Page; dashboard: DashboardPage };
    user3: { page: Page; dashboard: DashboardPage };
  };
};

// Extend the base test with custom fixtures
export const test = base.extend<TestFixtures>({
  // Page Object Model fixtures
  loginPage: async ({ page }, use) => {
    const loginPage = new LoginPage(page);
    await use(loginPage);
  },

  registerPage: async ({ page }, use) => {
    const registerPage = new RegisterPage(page);
    await use(registerPage);
  },

  dashboardPage: async ({ page }, use) => {
    const dashboardPage = new DashboardPage(page);
    await use(dashboardPage);
  },

  // Test data manager fixture
  testDataManager: async ({}, use) => {
    const testDataManager = new TestDataManager();
    await use(testDataManager);
  },

  // Authenticated context for primary user
  authenticatedContext: async ({ browser }, use) => {
    const context = await browser.newContext();
    
    try {
      // Check if auth state exists, if not create it
      const fs = require('fs');
      const authStatePath = 'e2e/fixtures/auth-state-primary.json';
      
      if (!fs.existsSync(authStatePath)) {
        await TestUtils.authenticateUser(context, 'primary');
      } else {
        // Load existing auth state
        await context.addInitScript(() => {
          // Load auth state from storage
        });
      }
      
      await use(context);
    } finally {
      await context.close();
    }
  },

  // Second user context for multi-user testing
  secondUserContext: async ({ browser }, use) => {
    const context = await browser.newContext();
    
    try {
      await TestUtils.authenticateUser(context, 'secondary');
      await use(context);
    } finally {
      await context.close();
    }
  },

  // Third user context for group testing
  thirdUserContext: async ({ browser }, use) => {
    const context = await browser.newContext();
    
    try {
      await TestUtils.authenticateUser(context, 'tertiary');
      await use(context);
    } finally {
      await context.close();
    }
  },

  // Multi-user pages fixture for real-time testing
  multiUserPages: async ({ browser }, use) => {
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    const context3 = await browser.newContext();

    try {
      // Authenticate all users
      await TestUtils.authenticateUser(context1, 'primary');
      await TestUtils.authenticateUser(context2, 'secondary');
      await TestUtils.authenticateUser(context3, 'tertiary');

      // Create pages for each user
      const page1 = await context1.newPage();
      const page2 = await context2.newPage();
      const page3 = await context3.newPage();

      // Create dashboard instances
      const dashboard1 = new DashboardPage(page1);
      const dashboard2 = new DashboardPage(page2);
      const dashboard3 = new DashboardPage(page3);

      // Navigate all users to dashboard
      await dashboard1.goto();
      await dashboard2.goto();
      await dashboard3.goto();

      // Wait for all connections to be established
      await TestUtils.waitForSocketConnection(page1);
      await TestUtils.waitForSocketConnection(page2);
      await TestUtils.waitForSocketConnection(page3);

      const multiUserPages = {
        user1: { page: page1, dashboard: dashboard1 },
        user2: { page: page2, dashboard: dashboard2 },
        user3: { page: page3, dashboard: dashboard3 }
      };

      await use(multiUserPages);
    } finally {
      await context1.close();
      await context2.close();
      await context3.close();
    }
  }
});

// Export expect from Playwright
export { expect } from '@playwright/test';

// Custom test utilities
export class TestUtils {
  static async createTestUser(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    username?: string;
  }) {
    const testDataManager = new TestDataManager();

    // Ensure username is provided
    const completeUserData = {
      ...userData,
      username: userData.username || `user_${Date.now()}_${Math.random().toString(36).substring(7)}`
    };

    return await testDataManager.createTestUser('custom', completeUserData);
  }

  static async loginUser(page: Page, userKey: string = 'primary') {
    const testDataManager = new TestDataManager();
    const testUser = testDataManager.getTestUser(userKey);
    
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    await loginPage.login(testUser.email, testUser.password);
    await loginPage.waitForLoginSuccess();
  }

  static async registerUser(page: Page, userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    username?: string;
  }) {
    const registerPage = new RegisterPage(page);
    await registerPage.goto();
    await registerPage.register(userData);
    await registerPage.waitForRegistrationSuccess();
  }

  static async setupConversation(
    user1Dashboard: DashboardPage,
    user2Username: string
  ) {
    await user1Dashboard.createDirectConversation(user2Username);
    await user1Dashboard.waitForChatAreaLoad();
  }

  static async sendTestMessage(
    dashboard: DashboardPage,
    message: string = 'Test message'
  ) {
    await dashboard.sendMessage(message);
    await dashboard.waitForMessageSent();
  }

  static async verifyMessageDelivery(
    senderDashboard: DashboardPage,
    receiverDashboard: DashboardPage,
    message: string
  ) {
    // Send message from sender
    await senderDashboard.sendMessage(message);
    
    // Verify message appears for sender
    await senderDashboard.waitForMessageSent();
    const senderLastMessage = await senderDashboard.getLastMessage();
    expect(senderLastMessage).toContain(message);
    
    // Verify message appears for receiver
    await receiverDashboard.waitForNewMessage();
    const receiverLastMessage = await receiverDashboard.getLastMessage();
    expect(receiverLastMessage).toContain(message);
  }

  static async verifyTypingIndicator(
    typerDashboard: DashboardPage,
    observerDashboard: DashboardPage
  ) {
    // Start typing
    await typerDashboard.simulateTyping('Test typing...');
    
    // Verify typing indicator appears for observer
    await observerDashboard.waitForTypingIndicator();
    expect(await observerDashboard.isTypingIndicatorVisible()).toBe(true);
    
    // Stop typing
    await typerDashboard.stopTyping();
    
    // Verify typing indicator disappears
    await observerDashboard.waitForTypingIndicatorToDisappear();
    expect(await observerDashboard.isTypingIndicatorVisible()).toBe(false);
  }

  static async simulateNetworkIssue(page: Page, duration: number = 3000) {
    await TestUtils.simulateNetworkFailure(page);
    await page.waitForTimeout(duration);
    await TestUtils.restoreNetwork(page);
  }

  static async simulateNetworkFailure(page: Page): Promise<void> {
    await page.route('**/*', route => route.abort());
  }

  static async restoreNetwork(page: Page): Promise<void> {
    await page.unroute('**/*');
  }

  static async simulateSlowNetwork(page: Page, delay: number = 1000): Promise<void> {
    await page.route('**/*', async route => {
      await new Promise(resolve => setTimeout(resolve, delay));
      await route.continue();
    });
  }

  static async authenticateUser(context: BrowserContext, userKey: string = 'primary'): Promise<void> {
    const testDataManager = new TestDataManager();
    const testUser = testDataManager.getTestUser(userKey);

    const page = await context.newPage();

    try {
      await page.goto('/login');
      await page.fill('[data-testid="email-input"], input[type="email"]', testUser.email);
      await page.fill('[data-testid="password-input"], input[type="password"]', testUser.password);
      await page.click('[data-testid="login-button"], button[type="submit"]');

      // Wait for successful login
      await page.waitForURL('**/dashboard', { timeout: 10000 });

      // Save authentication state
      await context.storageState({ path: `e2e/fixtures/auth-state-${userKey}.json` });
    } finally {
      await page.close();
    }
  }

  static async waitForSocketConnection(page: Page, timeout: number = 10000): Promise<void> {
    // Wait for socket connection indicator
    const connectionStatus = page.locator('[data-testid="connection-status"], .connection-status');
    await expect(connectionStatus).toContainText(/connected|online/i, { timeout });
  }

  static async verifyErrorHandling(
    page: Page,
    dashboard: DashboardPage,
    expectedError: string
  ) {
    await dashboard.waitForErrorMessage();
    const errorMessage = await dashboard.getErrorMessage();
    expect(errorMessage).toContain(expectedError);
  }

  static async measureResponseTime(operation: () => Promise<void>): Promise<number> {
    const startTime = Date.now();
    await operation();
    return Date.now() - startTime;
  }

  static async verifyAccessibility(page: Page) {
    // Basic accessibility check - verify page has proper headings and labels
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').count();
    expect(headings).toBeGreaterThan(0);

    // Check for form labels
    const forms = await page.locator('form').count();
    if (forms > 0) {
      const labels = await page.locator('label').count();
      const inputs = await page.locator('input, textarea, select').count();
      expect(labels).toBeGreaterThanOrEqual(inputs * 0.5); // At least 50% of inputs should have labels
    }
  }

  static async takeDebugScreenshot(page: Page, testName: string) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `debug-${testName}-${timestamp}.png`;
    await page.screenshot({ path: `e2e/test-results/${filename}`, fullPage: true });
  }

  static generateRandomEmail(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(7);
    return `test.${timestamp}.${random}@example.com`;
  }

  static generateRandomUsername(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(7);
    return `testuser${timestamp}${random}`;
  }

  static generateTestMessage(length: number = 50): string {
    const words = ['hello', 'world', 'test', 'message', 'chat', 'application', 'playwright', 'automation'];
    let message = '';

    while (message.length < length) {
      const word = words[Math.floor(Math.random() * words.length)];
      message += (message ? ' ' : '') + word;
    }

    return message.substring(0, length);
  }

  // Encryption-related utilities
  static async waitForEncryptionInitialization(page: Page, timeout: number = 15000): Promise<void> {
    try {
      await page.waitForTimeout(3000); // Give time for initialization
      await page.waitForFunction(() => {
        return document.querySelector('[data-testid="encryption-status"]') !== null ||
               document.querySelector('.encryption-status') !== null ||
               document.body.classList.contains('encryption-ready') ||
               document.querySelector('[data-testid="dashboard-header"]') !== null; // Fallback
      }, { timeout });
    } catch (error) {
      console.log('Encryption initialization detection timed out, continuing');
    }
  }

  static async verifyEncryptionStatus(page: Page): Promise<boolean> {
    try {
      return await page.evaluate(() => {
        return document.querySelector('[data-testid="encryption-status"]') !== null ||
               document.querySelector('.encryption-status') !== null ||
               document.body.classList.contains('encryption-ready');
      });
    } catch {
      return false;
    }
  }

  static async setupEncryptedConversation(
    user1Dashboard: DashboardPage,
    user2Dashboard: DashboardPage,
    user2Username: string
  ): Promise<void> {
    // Wait for encryption initialization for both users
    await TestUtils.waitForEncryptionInitialization(user1Dashboard.page);
    await TestUtils.waitForEncryptionInitialization(user2Dashboard.page);

    // Create conversation
    await user1Dashboard.createDirectConversation(user2Username);
    await user1Dashboard.waitForChatAreaLoad();

    // Wait for conversation to appear for user2 and select it
    await user2Dashboard.waitForConversationToAppear();
    const conversationCount = await user2Dashboard.getConversationCount();
    if (conversationCount > 0) {
      await user2Dashboard.selectConversation(0);
    }
  }

  static async verifyEncryptedMessageDelivery(
    senderDashboard: DashboardPage,
    receiverDashboard: DashboardPage,
    message: string
  ): Promise<void> {
    // Send encrypted message
    await senderDashboard.sendMessage(message);
    await senderDashboard.waitForMessageSent();

    // Verify message appears for sender
    const senderLastMessage = await senderDashboard.getLastMessage();
    expect(senderLastMessage).toContain(message);

    // Verify message appears decrypted for receiver
    await receiverDashboard.waitForNewMessage();
    const receiverLastMessage = await receiverDashboard.getLastMessage();
    expect(receiverLastMessage).toContain(message);
  }

  static async simulateEncryptionFailure(page: Page): Promise<void> {
    await page.evaluate(() => {
      // Temporarily break encryption by corrupting crypto functions
      if (window.crypto && window.crypto.subtle) {
        const originalGenerateKey = window.crypto.subtle.generateKey;
        window.crypto.subtle.generateKey = () => Promise.reject(new Error('Simulated encryption failure'));

        // Store original function to restore later
        (window as any).__originalGenerateKey = originalGenerateKey;
      }
    });
  }

  static async restoreEncryption(page: Page): Promise<void> {
    await page.evaluate(() => {
      // Restore original encryption functions
      if (window.crypto && window.crypto.subtle && (window as any).__originalGenerateKey) {
        window.crypto.subtle.generateKey = (window as any).__originalGenerateKey;
        delete (window as any).__originalGenerateKey;
      }
    });
  }

  static async verifySocketBasedConversationCreation(
    dashboard: DashboardPage,
    username: string
  ): Promise<void> {
    // Verify socket connection
    await dashboard.waitForSocketConnection();

    // Create conversation via socket events (not REST API)
    await dashboard.createDirectConversation(username);

    // Verify conversation was created without 404 errors
    const hasError = await dashboard.hasErrorMessage();
    expect(hasError).toBe(false);
  }

}
