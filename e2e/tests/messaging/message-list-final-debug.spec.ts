import { test, expect } from '../../fixtures/test-fixtures';

test.describe('MessageList Final Debug', () => {
  test('should debug MessageList rendering states', async ({ page }) => {
    console.log('🔍 Starting MessageList final debug test...');
    
    // Capture console logs
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      if (msg.text().includes('🔍 MessageList') ||
          msg.text().includes('🔍 RTK Query') ||
          msg.text().includes('RTK Query') ||
          msg.text().includes('API')) {
        consoleLogs.push(msg.text());
      }
    });
    
    try {
      // Step 1: Login
      await test.step('Login and setup', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);
        
        expect(page.url()).toContain('/dashboard');
        console.log('✅ User authenticated');
      });

      // Step 2: Create conversation and track MessageList states
      await test.step('Create conversation and track MessageList states', async () => {
        console.log('🔍 Creating conversation...');
        
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page.waitForTimeout(3000);
        
        console.log('📊 MessageList logs after conversation creation:');
        consoleLogs.slice(-10).forEach(log => console.log(`  ${log}`));
      });

      // Step 3: Send message and track MessageList updates
      await test.step('Send message and track MessageList updates', async () => {
        console.log('📤 Sending message...');
        
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        const testMessage = 'MessageList final debug test message 🔍';
        
        await messageInput.fill(testMessage);
        await sendButton.click();
        await page.waitForTimeout(5000);
        
        console.log('📊 MessageList logs after sending message:');
        consoleLogs.slice(-15).forEach(log => console.log(`  ${log}`));
      });

      // Step 4: Check final UI state
      await test.step('Check final UI state', async () => {
        console.log('🔍 Checking final UI state...');
        
        // Check MessageList visibility
        const messageList = page.locator('[data-testid="message-list"]');
        const messageListVisible = await messageList.isVisible();
        console.log(`💬 MessageList visible: ${messageListVisible}`);
        
        // Check for empty state text
        const emptyStateText = page.locator('text="No messages yet"');
        const emptyStateVisible = await emptyStateText.isVisible();
        console.log(`💬 Empty state visible: ${emptyStateVisible}`);
        
        // Check for loading state text
        const loadingStateText = page.locator('text="Loading messages..."');
        const loadingStateVisible = await loadingStateText.isVisible();
        console.log(`💬 Loading state visible: ${loadingStateVisible}`);
        
        // Check for error state text
        const errorStateText = page.locator('text="Failed to load messages"');
        const errorStateVisible = await errorStateText.isVisible();
        console.log(`💬 Error state visible: ${errorStateVisible}`);
        
        // Check for actual messages
        const messages = await page.locator('[data-testid="message"]').count();
        console.log(`💬 Message elements count: ${messages}`);
        
        // Check if our test message is visible
        const testMessage = 'MessageList final debug test message 🔍';
        const testMessageVisible = await page.locator(`text="${testMessage}"`).isVisible();
        console.log(`💬 Test message visible: ${testMessageVisible}`);
        
        console.log('📊 All MessageList logs:');
        consoleLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
      });

      console.log('🎯 MessageList final debug test completed!');

    } catch (error) {
      console.error('❌ Test failed:', error);
      console.log('📊 MessageList logs at failure:');
      consoleLogs.forEach((log, index) => {
        console.log(`  ${index + 1}. ${log}`);
      });
      throw error;
    }
  });
});
