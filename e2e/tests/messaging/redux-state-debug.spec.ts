import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Redux State Debug', () => {
  test('should track Redux state changes during conversation creation and message sending', async ({ page }) => {
    console.log('🔍 Starting Redux state debug test...');
    
    try {
      // Step 1: Login
      await test.step('Login and setup', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);
        
        expect(page.url()).toContain('/dashboard');
        console.log('✅ User authenticated');
      });

      // Step 2: Check initial Redux state
      await test.step('Check initial Redux state', async () => {
        const initialState = await page.evaluate(() => {
          const store = (window as any).__REDUX_STORE__ || (window as any).store;
          if (store) {
            const state = store.getState();
            return {
              selectedConversationId: state.conversations?.selectedConversationId,
              conversations: state.conversations?.conversations?.length || 0,
              draftConversations: state.conversations?.draftConversations?.length || 0,
              messages: Object.keys(state.messages?.messages || {}).length
            };
          }
          return { error: 'Redux store not accessible' };
        });
        
        console.log('🏪 Initial Redux state:', JSON.stringify(initialState, null, 2));
      });

      // Step 3: Create draft conversation and track state
      await test.step('Create draft conversation and track state', async () => {
        console.log('🔍 Creating draft conversation...');
        
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        // Check state before selecting user
        const stateBeforeSelection = await page.evaluate(() => {
          const store = (window as any).__REDUX_STORE__ || (window as any).store;
          if (store) {
            const state = store.getState();
            return {
              selectedConversationId: state.conversations?.selectedConversationId,
              conversations: state.conversations?.conversations?.length || 0,
              draftConversations: state.conversations?.draftConversations?.length || 0
            };
          }
          return { error: 'Redux store not accessible' };
        });
        
        console.log('🏪 State before user selection:', JSON.stringify(stateBeforeSelection, null, 2));
        
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page.waitForTimeout(2000);
        
        // Check state after selecting user (should have draft conversation)
        const stateAfterSelection = await page.evaluate(() => {
          const store = (window as any).__REDUX_STORE__ || (window as any).store;
          if (store) {
            const state = store.getState();
            return {
              selectedConversationId: state.conversations?.selectedConversationId,
              conversations: state.conversations?.conversations?.length || 0,
              draftConversations: state.conversations?.draftConversations?.length || 0,
              draftDetails: state.conversations?.draftConversations?.map(draft => ({
                id: draft.id,
                participants: draft.participants?.map(p => p.username)
              }))
            };
          }
          return { error: 'Redux store not accessible' };
        });
        
        console.log('🏪 State after user selection:', JSON.stringify(stateAfterSelection, null, 2));
      });

      // Step 4: Send message and track state changes
      await test.step('Send message and track state changes', async () => {
        console.log('📤 Sending message and tracking Redux state changes...');
        
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        const testMessage = 'Redux state debug test message 🔍';
        
        // Check state before sending message
        const stateBeforeSending = await page.evaluate(() => {
          const store = (window as any).__REDUX_STORE__ || (window as any).store;
          if (store) {
            const state = store.getState();
            return {
              selectedConversationId: state.conversations?.selectedConversationId,
              conversations: state.conversations?.conversations?.length || 0,
              draftConversations: state.conversations?.draftConversations?.length || 0,
              messages: Object.keys(state.messages?.messages || {}).length,
              conversationDetails: state.conversations?.conversations?.map(conv => ({
                id: conv.id,
                type: conv.type,
                participants: conv.participants?.map(p => p.username)
              }))
            };
          }
          return { error: 'Redux store not accessible' };
        });
        
        console.log('🏪 State before sending message:', JSON.stringify(stateBeforeSending, null, 2));
        
        // Set up Redux action monitoring
        await page.evaluate(() => {
          const store = (window as any).__REDUX_STORE__ || (window as any).store;
          if (store) {
            const originalDispatch = store.dispatch;
            (window as any).reduxActions = [];
            
            store.dispatch = function(action: any) {
              (window as any).reduxActions.push({
                type: action.type,
                payload: action.payload,
                timestamp: new Date().toISOString()
              });
              return originalDispatch.call(this, action);
            };
          }
        });
        
        await messageInput.fill(testMessage);
        await sendButton.click();
        
        // Wait for message processing
        await page.waitForTimeout(3000);
        
        // Check Redux actions that were dispatched
        const reduxActions = await page.evaluate(() => {
          return (window as any).reduxActions || [];
        });
        
        console.log('🔄 Redux actions dispatched during message sending:');
        reduxActions.forEach((action: any, index: number) => {
          console.log(`  ${index + 1}. ${action.type} at ${action.timestamp}`);
          if (action.type.includes('conversation') || action.type.includes('message')) {
            console.log(`     Payload:`, JSON.stringify(action.payload, null, 6));
          }
        });
        
        // Check state after sending message
        const stateAfterSending = await page.evaluate(() => {
          const store = (window as any).__REDUX_STORE__ || (window as any).store;
          if (store) {
            const state = store.getState();
            return {
              selectedConversationId: state.conversations?.selectedConversationId,
              conversations: state.conversations?.conversations?.length || 0,
              draftConversations: state.conversations?.draftConversations?.length || 0,
              messages: Object.keys(state.messages?.messages || {}).length,
              conversationDetails: state.conversations?.conversations?.map(conv => ({
                id: conv.id,
                type: conv.type,
                participants: conv.participants?.map(p => p.username)
              })),
              messageDetails: Object.entries(state.messages?.messages || {}).map(([convId, messages]) => ({
                conversationId: convId,
                messageCount: (messages as any[])?.length || 0,
                lastMessage: (messages as any[])?.slice(-1)[0]?.content
              }))
            };
          }
          return { error: 'Redux store not accessible' };
        });
        
        console.log('🏪 State after sending message:', JSON.stringify(stateAfterSending, null, 2));
      });

      // Step 5: Check UI rendering based on Redux state
      await test.step('Check UI rendering based on Redux state', async () => {
        console.log('🔍 Checking UI rendering based on Redux state...');
        
        // Get current Redux state
        const currentState = await page.evaluate(() => {
          const store = (window as any).__REDUX_STORE__ || (window as any).store;
          if (store) {
            const state = store.getState();
            return {
              selectedConversationId: state.conversations?.selectedConversationId,
              isDraft: state.conversations?.selectedConversationId?.startsWith('draft-')
            };
          }
          return { error: 'Redux store not accessible' };
        });
        
        console.log('🏪 Current Redux state for UI check:', JSON.stringify(currentState, null, 2));
        
        // Check what UI is actually rendered
        const chatArea = page.locator('[data-testid="chat-area"]');
        const chatAreaVisible = await chatArea.isVisible();
        console.log(`💬 Chat area visible: ${chatAreaVisible}`);
        
        if (chatAreaVisible) {
          const chatAreaContent = await chatArea.textContent();
          console.log(`💬 Chat area content: "${chatAreaContent?.substring(0, 200)}..."`);
          
          // Check for "No conversation selected" message
          const noConversationMessage = page.locator('text="No conversation selected"');
          const noConversationVisible = await noConversationMessage.isVisible();
          console.log(`💬 "No conversation selected" visible: ${noConversationVisible}`);
          
          // Check for ChatRoom component
          const messageList = page.locator('[data-testid="message-list"]');
          const messageListVisible = await messageList.isVisible();
          console.log(`💬 Message list visible: ${messageListVisible}`);
          
          const messageInput = page.locator('[data-testid="message-input"]');
          const messageInputVisible = await messageInput.isVisible();
          console.log(`💬 Message input visible: ${messageInputVisible}`);
        }
        
        // Check conversation list state
        const conversationList = page.locator('[data-testid="conversation-list"]');
        const conversationItems = await page.locator('[data-testid="conversation-item"]').count();
        console.log(`📋 Conversation items in list: ${conversationItems}`);
        
        // Check if any conversation is selected (highlighted)
        const selectedConversation = page.locator('.bg-blue-50.border-blue-200');
        const selectedConversationVisible = await selectedConversation.isVisible();
        console.log(`📋 Selected conversation highlighted: ${selectedConversationVisible}`);
        
        if (selectedConversationVisible) {
          const selectedConversationText = await selectedConversation.textContent();
          console.log(`📋 Selected conversation text: "${selectedConversationText?.substring(0, 100)}..."`);
        }
      });

      console.log('🎯 Redux state debug test completed!');

    } catch (error) {
      console.error('❌ Test failed:', error);
      throw error;
    }
  });
});
