import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Transform Fix Test', () => {
  test('should test the RTK Query transform fix', async ({ page }) => {
    console.log('🔍 Starting transform fix test...');
    
    // Capture console logs
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      if (msg.text().includes('🔍 RTK Query transformResponse') || 
          msg.text().includes('🔍 MessageList') ||
          msg.text().includes('🔍 Real conversation messages')) {
        consoleLogs.push(msg.text());
      }
    });
    
    try {
      // Step 1: Login
      await test.step('Login and setup', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);
        
        expect(page.url()).toContain('/dashboard');
        console.log('✅ User authenticated');
      });

      // Step 2: Create conversation and send message
      await test.step('Create conversation and send message', async () => {
        console.log('🔍 Creating conversation...');
        
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page.waitForTimeout(3000);
        
        console.log('📤 Sending message...');
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        const testMessage = 'Transform fix test message 🔧';
        await messageInput.fill(testMessage);
        await sendButton.click();
        await page.waitForTimeout(5000);
        
        console.log('✅ Message sent');
      });

      // Step 3: Check if messages are now visible
      await test.step('Check message visibility', async () => {
        console.log('🔍 Checking message visibility...');
        
        // Check if MessageList is visible
        const messageList = page.locator('[data-testid="message-list"]');
        const messageListVisible = await messageList.isVisible();
        console.log(`💬 MessageList visible: ${messageListVisible}`);
        
        // Check for actual messages
        const messages = await page.locator('[data-testid="message"]').count();
        console.log(`💬 Message elements count: ${messages}`);
        
        // Check if our test message is visible
        const testMessage = 'Transform fix test message 🔧';
        const testMessageVisible = await page.locator(`text="${testMessage}"`).isVisible();
        console.log(`💬 Test message visible: ${testMessageVisible}`);
        
        // Check for empty state
        const emptyStateVisible = await page.locator('text="No messages yet"').isVisible();
        console.log(`💬 Empty state visible: ${emptyStateVisible}`);
        
        console.log('📊 Transform and MessageList logs:');
        consoleLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
        
        // The fix should make messages visible
        if (messages > 0 && !emptyStateVisible) {
          console.log('🎉 SUCCESS: Messages are now visible in MessageList!');
        } else {
          console.log('❌ ISSUE: Messages still not visible in MessageList');
        }
      });

      console.log('🎯 Transform fix test completed!');

    } catch (error) {
      console.error('❌ Test failed:', error);
      console.log('📊 Console logs at failure:');
      consoleLogs.forEach((log, index) => {
        console.log(`  ${index + 1}. ${log}`);
      });
      throw error;
    }
  });
});
