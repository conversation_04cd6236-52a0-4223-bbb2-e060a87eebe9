import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Encryption and Database Issues Reproduction', () => {
  test('should reproduce encryption session failures and database schema errors', async ({ page }) => {
    console.log('🧪 Starting encryption and database issues reproduction test...');
    
    // Track all console logs for analysis
    const allLogs: string[] = [];
    const errorLogs: string[] = [];
    const encryptionLogs: string[] = [];
    const databaseLogs: string[] = [];
    
    page.on('console', msg => {
      const text = msg.text();
      allLogs.push(text);
      
      // Categorize logs
      if (msg.type() === 'error' || text.includes('ERROR') || text.includes('❌')) {
        errorLogs.push(text);
      }
      
      if (text.includes('🔐') || 
          text.includes('encrypt') || 
          text.includes('session') ||
          text.includes('Encryption') ||
          text.includes('SignalProtocol') ||
          text.includes('Session not found')) {
        encryptionLogs.push(text);
      }
      
      if (text.includes('MessageStatusType') ||
          text.includes('Prisma') ||
          text.includes('database') ||
          text.includes('messageStatusService') ||
          text.includes('upsert')) {
        databaseLogs.push(text);
      }
    });
    
    try {
      // Step 1: Login and setup
      await test.step('Login and setup', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        
        await page.waitForURL('**/dashboard', { timeout: 10000 });
        console.log('✅ User authenticated');
        
        // Wait for encryption initialization
        await page.waitForTimeout(5000);
      });

      // Step 2: Create or select conversation to trigger session initialization
      await test.step('Create conversation to trigger encryption session', async () => {
        console.log('🔍 Creating conversation to trigger encryption session...');
        
        // Clear logs to focus on conversation creation
        allLogs.length = 0;
        errorLogs.length = 0;
        encryptionLogs.length = 0;
        databaseLogs.length = 0;
        
        // Try to create a new conversation
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible({ timeout: 5000 });
        
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible({ timeout: 5000 });
        await userButton.click();
        await page.waitForTimeout(3000);
        
        console.log('✅ Conversation creation attempted');
      });

      // Step 3: Attempt to send message to trigger encryption failure
      await test.step('Send message to trigger encryption session failure', async () => {
        console.log('🔍 Attempting to send message to trigger encryption issues...');
        
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        await expect(messageInput).toBeVisible({ timeout: 5000 });
        await expect(sendButton).toBeVisible({ timeout: 5000 });
        
        // Clear logs to focus on message sending
        allLogs.length = 0;
        errorLogs.length = 0;
        encryptionLogs.length = 0;
        databaseLogs.length = 0;
        
        // Send a test message that should trigger encryption
        const testMessage = 'Test message to trigger encryption failure';
        console.log(`📤 Sending message: ${testMessage}`);
        
        await messageInput.fill(testMessage);
        await sendButton.click();
        
        // Wait for message processing and potential errors
        await page.waitForTimeout(8000);
        
        console.log('✅ Message send attempted');
      });

      // Step 4: Analyze encryption session errors
      await test.step('Analyze encryption session errors', async () => {
        console.log('🔍 Analyzing encryption session errors...');
        
        console.log('\n📋 All Console Logs:');
        allLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
        
        console.log('\n🔐 Encryption-related Logs:');
        encryptionLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
        
        console.log('\n❌ Error Logs:');
        errorLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
        
        // Check for specific encryption issues
        const sessionNotFoundError = encryptionLogs.some(log => 
          log.includes('Session not found for conversation')
        );
        
        const encryptionFallback = encryptionLogs.some(log => 
          log.includes('Falling back to unencrypted') ||
          log.includes('Failed to encrypt message')
        );
        
        const sessionInitializationFailed = encryptionLogs.some(log => 
          log.includes('Failed to initialize encryption session')
        );
        
        console.log('\n📊 Encryption Issue Analysis:');
        console.log(`  - "Session not found" error: ${sessionNotFoundError ? '❌ REPRODUCED' : '✅ Not found'}`);
        console.log(`  - Encryption fallback to unencrypted: ${encryptionFallback ? '❌ REPRODUCED' : '✅ Not found'}`);
        console.log(`  - Session initialization failed: ${sessionInitializationFailed ? '❌ REPRODUCED' : '✅ Not found'}`);
        
        // Report findings
        if (sessionNotFoundError) {
          console.log('🎯 ISSUE REPRODUCED: Session not found for conversation error detected');
        }
        if (encryptionFallback) {
          console.log('🎯 ISSUE REPRODUCED: System falling back to unencrypted messages');
        }
      });

      // Step 5: Analyze database schema errors
      await test.step('Analyze database schema errors', async () => {
        console.log('🔍 Analyzing database schema errors...');
        
        console.log('\n🗄️ Database-related Logs:');
        databaseLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
        
        // Check for specific database issues
        const messageStatusTypeError = databaseLogs.some(log => 
          log.includes('type "public.MessageStatusType" does not exist') ||
          log.includes('MessageStatusType') && log.includes('does not exist')
        );
        
        const prismaError = databaseLogs.some(log => 
          log.includes('Prisma') && (log.includes('error') || log.includes('ERROR'))
        );
        
        const upsertError = databaseLogs.some(log => 
          log.includes('upsert') && (log.includes('error') || log.includes('failed'))
        );
        
        console.log('\n📊 Database Issue Analysis:');
        console.log(`  - MessageStatusType enum missing: ${messageStatusTypeError ? '❌ REPRODUCED' : '✅ Not found'}`);
        console.log(`  - Prisma errors: ${prismaError ? '❌ REPRODUCED' : '✅ Not found'}`);
        console.log(`  - Message status upsert errors: ${upsertError ? '❌ REPRODUCED' : '✅ Not found'}`);
        
        // Report findings
        if (messageStatusTypeError) {
          console.log('🎯 ISSUE REPRODUCED: MessageStatusType enum missing from database');
        }
        if (prismaError) {
          console.log('🎯 ISSUE REPRODUCED: Prisma database errors detected');
        }
      });

      // Step 6: Test message status functionality
      await test.step('Test message status functionality', async () => {
        console.log('🔍 Testing message status functionality...');
        
        // Clear logs to focus on status updates
        databaseLogs.length = 0;
        errorLogs.length = 0;
        
        // Wait for potential status updates
        await page.waitForTimeout(5000);
        
        // Check for message status indicators in UI
        const statusIndicators = await page.locator('[data-testid*="status"], .message-status, .status-indicator').count();
        console.log(`📊 Message status indicators found: ${statusIndicators}`);
        
        // Check for delivery/read status
        const deliveredStatus = await page.locator('text=/delivered|sent|read/i').count();
        console.log(`📊 Status text elements found: ${deliveredStatus}`);
        
        // Analyze any new database errors
        if (databaseLogs.length > 0) {
          console.log('\n🗄️ New Database Logs during status check:');
          databaseLogs.forEach((log, index) => {
            console.log(`  ${index + 1}. ${log}`);
          });
        }
      });

      console.log('\n🎯 Test Summary:');
      console.log('This test attempted to reproduce the reported issues:');
      console.log('1. Encryption session "Session not found" errors');
      console.log('2. Database schema MessageStatusType enum errors');
      console.log('3. Message status update functionality failures');
      console.log('\nCheck the logs above for specific error reproductions.');

    } catch (error) {
      console.error('❌ Test execution failed:', error);
      
      // Log all captured information for debugging
      console.log('\n📋 Final Log Summary:');
      console.log(`Total logs captured: ${allLogs.length}`);
      console.log(`Error logs: ${errorLogs.length}`);
      console.log(`Encryption logs: ${encryptionLogs.length}`);
      console.log(`Database logs: ${databaseLogs.length}`);
      
      throw error;
    }
  });
});
