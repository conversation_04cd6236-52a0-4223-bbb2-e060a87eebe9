import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Complete Messaging Flow', () => {
  test('should complete full messaging flow between two users', async ({ browser }) => {
    console.log('🎯 Starting complete messaging flow test...');
    
    // Create two browser contexts for two users
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    try {
      // Step 1: Authenticate both users
      await test.step('Authenticate both users', async () => {
        // User 1 login
        await page1.goto('/login');
        await page1.waitForLoadState('networkidle');
        await page1.fill('input[placeholder*="email"]', '<EMAIL>');
        await page1.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page1.click('button[type="submit"]');
        await page1.waitForTimeout(3000);
        
        // User 2 login
        await page2.goto('/login');
        await page2.waitForLoadState('networkidle');
        await page2.fill('input[placeholder*="email"]', '<EMAIL>');
        await page2.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page2.click('button[type="submit"]');
        await page2.waitForTimeout(3000);
        
        // Verify both users are on dashboard
        expect(page1.url()).toContain('/dashboard');
        expect(page2.url()).toContain('/dashboard');
        console.log('✅ Both users authenticated and on dashboard');
      });

      // Step 2: User1 starts conversation with User2
      await test.step('User1 starts conversation with User2', async () => {
        // Open user search
        await page1.click('[data-testid="new-chat-button"]');
        await page1.waitForTimeout(1000);
        
        // Search for testuser2
        const searchInput = page1.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page1.waitForTimeout(2000);
        
        // Select user from search results
        const userButton = page1.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page1.waitForTimeout(2000);
        
        // Verify message input is visible
        const messageInput = page1.locator('[data-testid="message-input"]');
        await expect(messageInput).toBeVisible();
        console.log('✅ Conversation created and message input available');
      });

      // Step 3: Send message from User1 to User2
      await test.step('Send message from User1 to User2', async () => {
        const messageInput = page1.locator('[data-testid="message-input"]');
        const sendButton = page1.locator('[data-testid="send-button"]');
        
        const testMessage = 'Hello User2! This is a test message from the E2E test 🚀';
        await messageInput.fill(testMessage);
        
        // Verify send button is enabled
        await expect(sendButton).toBeEnabled();
        
        // Send the message
        await sendButton.click();
        await page1.waitForTimeout(2000);
        
        // Verify message appears in User1's chat
        await expect(page1.locator(`text="${testMessage}"`)).toBeVisible();
        
        // Verify input is cleared
        await expect(messageInput).toHaveValue('');
        console.log('✅ Message sent from User1');
      });

      // Step 4: Verify User2 receives the message
      await test.step('Verify User2 receives the message', async () => {
        // Wait for real-time message delivery
        await page2.waitForTimeout(3000);
        
        const testMessage = 'Hello User2! This is a test message from the E2E test 🚀';
        
        // Check if message appears on User2's page
        const messageElement = page2.locator(`text="${testMessage}"`);
        await expect(messageElement).toBeVisible({ timeout: 10000 });
        console.log('✅ Message received by User2');
      });

      // Step 5: Send reply from User2 to User1
      await test.step('Send reply from User2 to User1', async () => {
        // Find message input on User2's page
        const messageInput = page2.locator('[data-testid="message-input"]');
        const sendButton = page2.locator('[data-testid="send-button"]');
        
        // If message input is not visible, User2 might need to open the conversation
        if (!(await messageInput.isVisible())) {
          // Look for conversation in the sidebar and click it
          const conversationItem = page2.locator('[data-testid="conversation-item"]').first();
          if (await conversationItem.isVisible()) {
            await conversationItem.click();
            await page2.waitForTimeout(1000);
          }
        }
        
        await expect(messageInput).toBeVisible();
        
        const replyMessage = 'Hello User1! Reply from User2 - bidirectional messaging works! 🎉';
        await messageInput.fill(replyMessage);
        await sendButton.click();
        await page2.waitForTimeout(2000);
        
        // Verify reply appears in User2's chat
        await expect(page2.locator(`text="${replyMessage}"`)).toBeVisible();
        console.log('✅ Reply sent from User2');
      });

      // Step 6: Verify User1 receives the reply
      await test.step('Verify User1 receives the reply', async () => {
        await page1.waitForTimeout(3000);
        
        const replyMessage = 'Hello User1! Reply from User2 - bidirectional messaging works! 🎉';
        
        // Check if reply appears on User1's page
        const replyElement = page1.locator(`text="${replyMessage}"`);
        await expect(replyElement).toBeVisible({ timeout: 10000 });
        console.log('✅ Reply received by User1');
      });

      // Step 7: Test rapid message exchange
      await test.step('Test rapid message exchange', async () => {
        const messages = [
          { sender: page1, text: 'Quick message 1 from User1' },
          { sender: page2, text: 'Quick reply 1 from User2' },
          { sender: page1, text: 'Quick message 2 from User1' },
          { sender: page2, text: 'Quick reply 2 from User2' }
        ];
        
        for (const { sender, text } of messages) {
          const messageInput = sender.locator('[data-testid="message-input"]');
          const sendButton = sender.locator('[data-testid="send-button"]');
          
          await messageInput.fill(text);
          await sendButton.click();
          await sender.waitForTimeout(1000);
          
          // Verify message appears in sender's chat
          await expect(sender.locator(`text="${text}"`)).toBeVisible();
        }
        
        // Wait for all messages to be delivered
        await page1.waitForTimeout(3000);
        await page2.waitForTimeout(3000);
        
        // Verify all messages appear on both pages
        for (const { text } of messages) {
          await expect(page1.locator(`text="${text}"`)).toBeVisible();
          await expect(page2.locator(`text="${text}"`)).toBeVisible();
        }
        
        console.log('✅ Rapid message exchange successful');
      });

      // Step 8: Verify message persistence
      await test.step('Verify message persistence', async () => {
        // Refresh User1's page
        await page1.reload();
        await page1.waitForLoadState('networkidle');
        await page1.waitForTimeout(3000);
        
        // Verify messages are still visible after refresh
        const testMessage = 'Hello User2! This is a test message from the E2E test 🚀';
        const replyMessage = 'Hello User1! Reply from User2 - bidirectional messaging works! 🎉';
        
        await expect(page1.locator(`text="${testMessage}"`)).toBeVisible({ timeout: 10000 });
        await expect(page1.locator(`text="${replyMessage}"`)).toBeVisible({ timeout: 10000 });
        
        console.log('✅ Message persistence verified');
      });

      console.log('🎉 Complete messaging flow test passed!');
      console.log('📊 Test Summary:');
      console.log('- ✅ User authentication');
      console.log('- ✅ User search and conversation creation');
      console.log('- ✅ Message sending (User1 → User2)');
      console.log('- ✅ Real-time message delivery');
      console.log('- ✅ Bidirectional messaging (User2 → User1)');
      console.log('- ✅ Rapid message exchange');
      console.log('- ✅ Message persistence after page refresh');
      console.log('- ✅ Socket-based real-time communication');
      console.log('- ✅ Optimistic UI updates');

    } finally {
      await context1.close();
      await context2.close();
    }
  });

  test('should handle message sending with network issues', async ({ page }) => {
    await test.step('Test message retry functionality', async () => {
      // Login
      await page.goto('/login');
      await page.fill('input[placeholder*="email"]', '<EMAIL>');
      await page.fill('input[placeholder*="password"]', 'TestPassword123!');
      await page.click('button[type="submit"]');
      await page.waitForTimeout(3000);
      
      // Start conversation
      await page.click('[data-testid="new-chat-button"]');
      const searchInput = page.locator('[data-testid="user-search-input"]');
      await searchInput.fill('testuser2');
      await page.waitForTimeout(2000);
      await page.click('[data-testid="user-action-button"]');
      await page.waitForTimeout(2000);
      
      // Send message
      const messageInput = page.locator('[data-testid="message-input"]');
      await messageInput.fill('Test message for network resilience');
      await page.click('[data-testid="send-button"]');
      
      // Verify optimistic update
      await expect(page.locator('text="Test message for network resilience"')).toBeVisible();
      
      console.log('✅ Message sending with optimistic updates works');
    });
  });
});
