import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Infinite Loop Fix Test', () => {
  test('should send messages without causing infinite loops or UI freezing', async ({ page }) => {
    console.log('🔍 Starting infinite loop fix test...');
    
    // Track console errors and warnings
    const consoleErrors: string[] = [];
    const consoleWarnings: string[] = [];
    
    page.on('console', msg => {
      const text = msg.text();
      if (msg.type() === 'error') {
        consoleErrors.push(text);
      } else if (msg.type() === 'warning') {
        consoleWarnings.push(text);
      }
    });
    
    // Track page crashes or unresponsiveness
    let pageCrashed = false;
    page.on('crash', () => {
      pageCrashed = true;
      console.error('❌ Page crashed!');
    });
    
    try {
      // Step 1: Login
      await test.step('Login and setup', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        
        // Fill login form
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        
        // Wait for redirect with timeout
        await page.waitForURL('**/dashboard', { timeout: 10000 });
        console.log('✅ User authenticated and redirected to dashboard');
      });

      // Step 2: Create conversation
      await test.step('Create conversation', async () => {
        console.log('🔍 Creating conversation...');
        
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible({ timeout: 5000 });
        
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible({ timeout: 5000 });
        await userButton.click();
        await page.waitForTimeout(3000);
        
        console.log('✅ Conversation created');
      });

      // Step 3: Test message sending without infinite loops
      await test.step('Test message sending performance', async () => {
        console.log('🔍 Testing message sending performance...');
        
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        // Verify elements are visible
        await expect(messageInput).toBeVisible({ timeout: 5000 });
        await expect(sendButton).toBeVisible({ timeout: 5000 });
        
        // Test multiple messages in quick succession
        const testMessages = [
          'Test message 1 🚀',
          'Test message 2 ⚡',
          'Test message 3 🎯'
        ];
        
        for (let i = 0; i < testMessages.length; i++) {
          const message = testMessages[i];
          console.log(`📤 Sending message ${i + 1}: ${message}`);
          
          // Record start time
          const startTime = Date.now();
          
          // Type and send message
          await messageInput.fill(message);
          await sendButton.click();
          
          // Verify input is cleared quickly (should happen within 2 seconds)
          await expect(messageInput).toHaveValue('', { timeout: 2000 });
          
          const endTime = Date.now();
          const duration = endTime - startTime;
          
          console.log(`⏱️ Message ${i + 1} sent in ${duration}ms`);
          
          // Check if the operation took too long (potential infinite loop)
          if (duration > 5000) {
            throw new Error(`Message sending took too long: ${duration}ms (potential infinite loop)`);
          }
          
          // Check if page crashed
          if (pageCrashed) {
            throw new Error('Page crashed during message sending');
          }
          
          // Small delay between messages
          await page.waitForTimeout(500);
        }
        
        console.log('✅ All messages sent successfully without performance issues');
      });

      // Step 4: Verify UI responsiveness
      await test.step('Verify UI responsiveness', async () => {
        console.log('🔍 Testing UI responsiveness...');
        
        // Test that we can still interact with the UI
        const messageInput = page.locator('[data-testid="message-input"]');
        
        // Type a message and verify it appears immediately
        const testText = 'Responsiveness test';
        await messageInput.fill(testText);
        await expect(messageInput).toHaveValue(testText, { timeout: 1000 });
        
        // Clear the input
        await messageInput.clear();
        await expect(messageInput).toHaveValue('', { timeout: 1000 });
        
        console.log('✅ UI remains responsive');
      });

      // Step 5: Check for console errors
      await test.step('Check for console errors', async () => {
        console.log('🔍 Checking console output...');
        
        // Filter out expected warnings
        const criticalErrors = consoleErrors.filter(error => 
          !error.includes('Failed to fetch') && // Network errors are expected in test environment
          !error.includes('WebSocket') && // WebSocket errors are expected
          !error.includes('Socket.IO') && // Socket.IO errors are expected
          !error.includes('404') && // 404s are expected for some endpoints
          !error.includes('401') // Auth errors are expected
        );
        
        const criticalWarnings = consoleWarnings.filter(warning =>
          warning.includes('infinite') ||
          warning.includes('loop') ||
          warning.includes('memory') ||
          warning.includes('performance')
        );
        
        console.log(`📊 Console summary:`);
        console.log(`  - Total errors: ${consoleErrors.length}`);
        console.log(`  - Critical errors: ${criticalErrors.length}`);
        console.log(`  - Total warnings: ${consoleWarnings.length}`);
        console.log(`  - Critical warnings: ${criticalWarnings.length}`);
        
        if (criticalErrors.length > 0) {
          console.log('❌ Critical errors found:');
          criticalErrors.forEach((error, index) => {
            console.log(`  ${index + 1}. ${error}`);
          });
        }
        
        if (criticalWarnings.length > 0) {
          console.log('⚠️ Critical warnings found:');
          criticalWarnings.forEach((warning, index) => {
            console.log(`  ${index + 1}. ${warning}`);
          });
        }
        
        // Fail test if critical issues found
        if (criticalErrors.length > 0) {
          throw new Error(`Found ${criticalErrors.length} critical console errors`);
        }
        
        if (criticalWarnings.length > 0) {
          throw new Error(`Found ${criticalWarnings.length} critical console warnings`);
        }
        
        console.log('✅ No critical console issues detected');
      });

      console.log('🎉 Infinite loop fix test completed successfully!');
      console.log('📊 Test Results:');
      console.log('  ✅ Messages sent without infinite loops');
      console.log('  ✅ UI remains responsive');
      console.log('  ✅ No critical console errors');
      console.log('  ✅ Page did not crash');

    } catch (error) {
      console.error('❌ Test failed:', error);
      
      // Log console output for debugging
      if (consoleErrors.length > 0) {
        console.log('🔍 Console errors:');
        consoleErrors.forEach((error, index) => {
          console.log(`  ${index + 1}. ${error}`);
        });
      }
      
      if (consoleWarnings.length > 0) {
        console.log('🔍 Console warnings:');
        consoleWarnings.forEach((warning, index) => {
          console.log(`  ${index + 1}. ${warning}`);
        });
      }
      
      throw error;
    }
  });
});
