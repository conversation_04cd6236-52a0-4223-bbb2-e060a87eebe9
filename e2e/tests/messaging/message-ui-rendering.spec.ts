import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Message UI Rendering', () => {
  test('should validate complete message UI lifecycle and rendering', async ({ browser }) => {
    console.log('🎯 Starting comprehensive message UI rendering test...');
    
    // Create two browser contexts for two users
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    try {
      // Step 1: Authenticate both users
      await test.step('Authenticate both users', async () => {
        // User 1 login
        await page1.goto('/login');
        await page1.waitForLoadState('networkidle');
        await page1.fill('input[placeholder*="email"]', '<EMAIL>');
        await page1.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page1.click('button[type="submit"]');
        await page1.waitForTimeout(3000);
        
        // User 2 login
        await page2.goto('/login');
        await page2.waitForLoadState('networkidle');
        await page2.fill('input[placeholder*="email"]', '<EMAIL>');
        await page2.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page2.click('button[type="submit"]');
        await page2.waitForTimeout(3000);
        
        // Verify both users are on dashboard
        expect(page1.url()).toContain('/dashboard');
        expect(page2.url()).toContain('/dashboard');
        console.log('✅ Both users authenticated and on dashboard');
      });

      // Step 2: User1 creates conversation with User2
      await test.step('User1 creates conversation with User2', async () => {
        // Open user search
        await page1.click('[data-testid="new-chat-button"]');
        await page1.waitForTimeout(1000);
        
        // Search for testuser2
        const searchInput = page1.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page1.waitForTimeout(2000);
        
        // Select user from search results
        const userButton = page1.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page1.waitForTimeout(2000);
        
        // Verify conversation UI is ready
        const messageInput = page1.locator('[data-testid="message-input"]');
        await expect(messageInput).toBeVisible();
        console.log('✅ Conversation created and UI ready');
      });

      // Step 3: Debug initial UI state
      await test.step('Debug initial UI state', async () => {
        console.log('🔍 Debugging initial UI state...');
        
        // Check message list containers
        const messageListSelectors = [
          '[data-testid="message-list"]',
          '[data-testid="messages-container"]',
          '.message-list',
          '.messages-container',
          '.chat-messages'
        ];
        
        for (const selector of messageListSelectors) {
          const element = page1.locator(selector);
          const isVisible = await element.isVisible();
          const count = await element.count();
          console.log(`📋 ${selector}: visible=${isVisible}, count=${count}`);
          
          if (isVisible) {
            const innerHTML = await element.innerHTML();
            console.log(`📋 ${selector} content length: ${innerHTML.length}`);
          }
        }
        
        // Check for any existing messages
        const messageSelectors = [
          '[data-testid="message"]',
          '.message',
          '.chat-message',
          '[data-testid="message-item"]'
        ];
        
        for (const selector of messageSelectors) {
          const count = await page1.locator(selector).count();
          console.log(`💬 ${selector}: ${count} messages found`);
        }
      });

      // Step 4: Send message from User1 and validate UI updates
      await test.step('Send message from User1 and validate UI updates', async () => {
        console.log('📤 Sending message from User1...');
        
        const messageInput = page1.locator('[data-testid="message-input"]');
        const sendButton = page1.locator('[data-testid="send-button"]');
        
        const testMessage = 'Hello User2! This is a UI rendering test message 🎨';
        
        // Set up console monitoring for User1
        const user1ConsoleMessages: any[] = [];
        page1.on('console', msg => {
          if (msg.text().includes('MESSAGE') || msg.text().includes('RTK') || msg.text().includes('CACHE')) {
            user1ConsoleMessages.push({
              type: msg.type(),
              text: msg.text(),
              timestamp: new Date().toISOString()
            });
          }
        });
        
        // Send the message
        await messageInput.fill(testMessage);
        await sendButton.click();
        
        // Wait for optimistic update
        await page1.waitForTimeout(1000);
        
        console.log('🔍 Checking for optimistic UI update...');
        
        // Check if message appears immediately (optimistic update)
        const optimisticMessage = page1.locator(`text="${testMessage}"`);
        const optimisticVisible = await optimisticMessage.isVisible();
        console.log(`📱 Optimistic message visible: ${optimisticVisible}`);
        
        if (!optimisticVisible) {
          // Debug why optimistic update didn't work
          console.log('❌ Optimistic update failed - debugging...');
          
          // Check message containers again
          for (const selector of [
            '[data-testid="message-list"]',
            '[data-testid="messages-container"]',
            '.message-list',
            '.messages-container'
          ]) {
            const element = page1.locator(selector);
            if (await element.isVisible()) {
              const content = await element.textContent();
              console.log(`📋 ${selector} content: "${content}"`);
              
              const innerHTML = await element.innerHTML();
              console.log(`📋 ${selector} HTML length: ${innerHTML.length}`);
              
              if (innerHTML.length > 0 && innerHTML.length < 1000) {
                console.log(`📋 ${selector} HTML: ${innerHTML}`);
              }
            }
          }
          
          // Check for any messages with different selectors
          const allMessages = await page1.locator('*').evaluateAll(elements => {
            return elements
              .filter(el => el.textContent?.includes('Hello User2! This is a UI rendering test message'))
              .map(el => ({
                tagName: el.tagName,
                className: el.className,
                textContent: el.textContent,
                style: el.getAttribute('style'),
                hidden: el.hidden,
                offsetHeight: (el as HTMLElement).offsetHeight,
                offsetWidth: (el as HTMLElement).offsetWidth
              }));
          });
          
          console.log('🔍 Found message elements:', allMessages);
        }
        
        // Wait for real-time confirmation
        await page1.waitForTimeout(3000);
        
        // Check if message appears after real-time confirmation
        const confirmedMessage = page1.locator(`text="${testMessage}"`);
        const confirmedVisible = await confirmedMessage.isVisible();
        console.log(`📱 Confirmed message visible: ${confirmedVisible}`);
        
        // Log console messages for debugging
        console.log(`📊 User1 console messages (${user1ConsoleMessages.length}):`);
        user1ConsoleMessages.slice(-10).forEach((msg, index) => {
          console.log(`  ${index + 1}. [${msg.type}] ${msg.text}`);
        });
        
        // Verify input was cleared
        const inputValue = await messageInput.inputValue();
        console.log(`📝 Input cleared: ${inputValue === ''}`);
      });

      // Step 5: Check if User2 receives the message in UI
      await test.step('Check if User2 receives message in UI', async () => {
        console.log('📥 Checking if User2 receives message in UI...');
        
        // Set up console monitoring for User2
        const user2ConsoleMessages: any[] = [];
        page2.on('console', msg => {
          if (msg.text().includes('MESSAGE') || msg.text().includes('RTK') || msg.text().includes('CACHE')) {
            user2ConsoleMessages.push({
              type: msg.type(),
              text: msg.text(),
              timestamp: new Date().toISOString()
            });
          }
        });
        
        // Wait for real-time message delivery
        await page2.waitForTimeout(5000);
        
        const testMessage = 'Hello User2! This is a UI rendering test message 🎨';
        
        // Check if message appears on User2's page
        const receivedMessage = page2.locator(`text="${testMessage}"`);
        const receivedVisible = await receivedMessage.isVisible();
        console.log(`📱 Message received by User2: ${receivedVisible}`);
        
        if (!receivedVisible) {
          console.log('❌ Message not visible on User2 - debugging...');
          
          // Check if User2 has any conversation open
          const chatArea = page2.locator('[data-testid="chat-area"]');
          const chatAreaVisible = await chatArea.isVisible();
          console.log(`💬 User2 chat area visible: ${chatAreaVisible}`);
          
          if (chatAreaVisible) {
            const chatContent = await chatArea.textContent();
            console.log(`💬 User2 chat content: "${chatContent}"`);
          }
          
          // Check for conversation list
          const conversationList = page2.locator('[data-testid="conversation-list"]');
          const conversationListVisible = await conversationList.isVisible();
          console.log(`📋 User2 conversation list visible: ${conversationListVisible}`);
          
          if (conversationListVisible) {
            const conversations = await page2.locator('[data-testid="conversation-item"]').count();
            console.log(`📋 User2 conversations count: ${conversations}`);
            
            if (conversations > 0) {
              // Click on the first conversation
              await page2.click('[data-testid="conversation-item"]');
              await page2.waitForTimeout(2000);
              
              // Check again for the message
              const messageAfterClick = page2.locator(`text="${testMessage}"`);
              const messageAfterClickVisible = await messageAfterClick.isVisible();
              console.log(`📱 Message visible after clicking conversation: ${messageAfterClickVisible}`);
            }
          }
          
          // Check for any elements containing the message text
          const allMessageElements = await page2.locator('*').evaluateAll(elements => {
            return elements
              .filter(el => el.textContent?.includes('Hello User2! This is a UI rendering test message'))
              .map(el => ({
                tagName: el.tagName,
                className: el.className,
                textContent: el.textContent,
                style: el.getAttribute('style'),
                hidden: el.hidden,
                offsetHeight: (el as HTMLElement).offsetHeight,
                offsetWidth: (el as HTMLElement).offsetWidth
              }));
          });
          
          console.log('🔍 User2 message elements:', allMessageElements);
        }
        
        // Log console messages for debugging
        console.log(`📊 User2 console messages (${user2ConsoleMessages.length}):`);
        user2ConsoleMessages.slice(-10).forEach((msg, index) => {
          console.log(`  ${index + 1}. [${msg.type}] ${msg.text}`);
        });
      });

      // Step 6: Test bidirectional messaging UI
      await test.step('Test bidirectional messaging UI', async () => {
        console.log('🔄 Testing bidirectional messaging UI...');
        
        // Ensure User2 has the conversation open
        const messageInput2 = page2.locator('[data-testid="message-input"]');
        
        if (!(await messageInput2.isVisible())) {
          // Try to open the conversation
          const conversationItem = page2.locator('[data-testid="conversation-item"]').first();
          if (await conversationItem.isVisible()) {
            await conversationItem.click();
            await page2.waitForTimeout(2000);
          }
        }
        
        if (await messageInput2.isVisible()) {
          const replyMessage = 'Hello User1! Reply from User2 - UI test working! 🎉';
          
          await messageInput2.fill(replyMessage);
          await page2.click('[data-testid="send-button"]');
          await page2.waitForTimeout(2000);
          
          // Check if reply appears in User2's UI
          const replyInUser2 = page2.locator(`text="${replyMessage}"`);
          const replyInUser2Visible = await replyInUser2.isVisible();
          console.log(`📱 Reply visible in User2 UI: ${replyInUser2Visible}`);
          
          // Check if reply appears in User1's UI
          await page1.waitForTimeout(3000);
          const replyInUser1 = page1.locator(`text="${replyMessage}"`);
          const replyInUser1Visible = await replyInUser1.isVisible();
          console.log(`📱 Reply visible in User1 UI: ${replyInUser1Visible}`);
        } else {
          console.log('⚠️ User2 message input not available for reply test');
        }
      });

      // Step 7: Test message persistence after page refresh
      await test.step('Test message persistence after page refresh', async () => {
        console.log('🔄 Testing message persistence after page refresh...');
        
        const testMessage = 'Hello User2! This is a UI rendering test message 🎨';
        
        // Refresh User1's page
        await page1.reload();
        await page1.waitForLoadState('networkidle');
        await page1.waitForTimeout(3000);
        
        // Check if messages are still visible after refresh
        const persistentMessage = page1.locator(`text="${testMessage}"`);
        const persistentVisible = await persistentMessage.isVisible();
        console.log(`📱 Message persistent after refresh: ${persistentVisible}`);
        
        if (!persistentVisible) {
          console.log('❌ Message not persistent - checking conversation state...');
          
          // Check if we need to reopen the conversation
          const conversationList = page1.locator('[data-testid="conversation-list"]');
          if (await conversationList.isVisible()) {
            const conversations = await page1.locator('[data-testid="conversation-item"]').count();
            console.log(`📋 Conversations after refresh: ${conversations}`);
            
            if (conversations > 0) {
              await page1.click('[data-testid="conversation-item"]');
              await page1.waitForTimeout(3000);
              
              const messageAfterReopen = page1.locator(`text="${testMessage}"`);
              const messageAfterReopenVisible = await messageAfterReopen.isVisible();
              console.log(`📱 Message visible after reopening conversation: ${messageAfterReopenVisible}`);
            }
          }
        }
      });

      console.log('🎯 Message UI rendering test completed!');

    } finally {
      await context1.close();
      await context2.close();
    }
  });
});
