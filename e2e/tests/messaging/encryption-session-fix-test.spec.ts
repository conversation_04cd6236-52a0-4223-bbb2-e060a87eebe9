import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Encryption Session Fix Test', () => {
  test('should initialize encryption sessions for existing conversations and send encrypted messages', async ({ page }) => {
    console.log('🔍 Starting encryption session fix test...');
    
    // Track encryption-related console logs
    const encryptionLogs: string[] = [];
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('🔐') || 
          text.includes('encrypt') || 
          text.includes('session') ||
          text.includes('Encryption') ||
          text.includes('SEND_MESSAGE')) {
        encryptionLogs.push(text);
      }
    });
    
    try {
      // Step 1: Login
      await test.step('Login and setup', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        
        await page.waitForURL('**/dashboard', { timeout: 10000 });
        console.log('✅ User authenticated');
      });

      // Step 2: Select existing conversation
      await test.step('Select existing conversation', async () => {
        console.log('🔍 Looking for existing conversations...');
        
        // Wait for conversations to load
        await page.waitForTimeout(3000);
        
        // Check if there are any conversations
        const conversationItems = await page.locator('[data-testid="conversation-item"]').count();
        console.log(`📋 Found ${conversationItems} existing conversations`);
        
        if (conversationItems > 0) {
          // Click on the first conversation
          await page.click('[data-testid="conversation-item"]');
          await page.waitForTimeout(2000);
          console.log('✅ Selected existing conversation');
        } else {
          // Create a new conversation first
          console.log('🔍 No existing conversations, creating one...');
          
          await page.click('[data-testid="new-chat-button"]');
          await page.waitForTimeout(1000);
          
          const searchInput = page.locator('[data-testid="user-search-input"]');
          await expect(searchInput).toBeVisible({ timeout: 5000 });
          
          await searchInput.fill('testuser2');
          await page.waitForTimeout(2000);
          
          const userButton = page.locator('[data-testid="user-action-button"]').first();
          await expect(userButton).toBeVisible({ timeout: 5000 });
          await userButton.click();
          await page.waitForTimeout(3000);
          
          console.log('✅ Created new conversation');
        }
      });

      // Step 3: Send message and verify encryption session initialization
      await test.step('Send message with encryption session initialization', async () => {
        console.log('🔍 Testing encryption session initialization...');
        
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        await expect(messageInput).toBeVisible({ timeout: 5000 });
        await expect(sendButton).toBeVisible({ timeout: 5000 });
        
        // Clear any existing logs
        encryptionLogs.length = 0;
        
        // Send a test message
        const testMessage = 'Encryption session test message 🔐';
        console.log(`📤 Sending message: ${testMessage}`);
        
        await messageInput.fill(testMessage);
        await sendButton.click();
        
        // Wait for message processing
        await page.waitForTimeout(5000);
        
        // Verify input is cleared
        await expect(messageInput).toHaveValue('', { timeout: 3000 });
        
        console.log('✅ Message sent successfully');
      });

      // Step 4: Analyze encryption logs
      await test.step('Analyze encryption session behavior', async () => {
        console.log('🔍 Analyzing encryption logs...');
        
        // Log all encryption-related messages
        console.log('🔐 Encryption logs:');
        encryptionLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
        
        // Check for key indicators
        const sessionInitializationAttempted = encryptionLogs.some(log => 
          log.includes('Checking encryption session') || 
          log.includes('Initializing encryption session')
        );
        
        const sessionInitialized = encryptionLogs.some(log => 
          log.includes('Encryption session initialized') ||
          log.includes('Session already exists')
        );
        
        const encryptionAttempted = encryptionLogs.some(log => 
          log.includes('Encrypting message')
        );
        
        const encryptionSucceeded = encryptionLogs.some(log => 
          log.includes('Message encrypted successfully')
        );
        
        const encryptionFailed = encryptionLogs.some(log => 
          log.includes('Failed to encrypt message') ||
          log.includes('Falling back to unencrypted')
        );
        
        console.log('📊 Encryption Analysis:');
        console.log(`  - Session initialization attempted: ${sessionInitializationAttempted}`);
        console.log(`  - Session initialized: ${sessionInitialized}`);
        console.log(`  - Encryption attempted: ${encryptionAttempted}`);
        console.log(`  - Encryption succeeded: ${encryptionSucceeded}`);
        console.log(`  - Encryption failed: ${encryptionFailed}`);
        
        // Determine the outcome
        if (encryptionSucceeded) {
          console.log('🎉 SUCCESS: Message was encrypted successfully!');
        } else if (encryptionFailed) {
          console.log('⚠️ INFO: Message fell back to unencrypted (expected for test environment)');
        } else if (encryptionAttempted) {
          console.log('🔍 INFO: Encryption was attempted but outcome unclear');
        } else {
          console.log('❌ ISSUE: No encryption attempt detected');
        }
        
        // The fix is working if we see session initialization attempts
        if (sessionInitializationAttempted) {
          console.log('✅ FIX WORKING: Encryption session initialization is being attempted for existing conversations');
        } else {
          console.log('❌ FIX NOT WORKING: No session initialization detected');
        }
      });

      // Step 5: Check if message appears in UI
      await test.step('Verify message display', async () => {
        console.log('🔍 Checking message display...');
        
        // Check if the message appears in the UI
        const messageVisible = await page.locator(`text="${'Encryption session test message 🔐'}"`).isVisible();
        console.log(`💬 Test message visible: ${messageVisible}`);
        
        // Check for any messages
        const messageCount = await page.locator('[data-testid="message"]').count();
        console.log(`💬 Total messages visible: ${messageCount}`);
        
        if (messageVisible) {
          console.log('✅ Message displayed successfully in UI');
        } else if (messageCount > 0) {
          console.log('✅ Messages are displaying (test message may be encrypted/processed)');
        } else {
          console.log('⚠️ No messages visible in UI');
        }
      });

      console.log('🎯 Encryption session fix test completed!');

    } catch (error) {
      console.error('❌ Test failed:', error);
      
      // Log encryption logs for debugging
      console.log('🔐 Encryption logs at failure:');
      encryptionLogs.forEach((log, index) => {
        console.log(`  ${index + 1}. ${log}`);
      });
      
      throw error;
    }
  });
});
