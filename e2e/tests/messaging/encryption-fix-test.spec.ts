import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Encryption Fix Test', () => {
  test('should verify encrypted messages are handled correctly', async ({ page }) => {
    console.log('🔍 Starting encryption fix test...');
    
    // Capture console logs for debugging
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      if (msg.text().includes('🔐') || 
          msg.text().includes('encrypt') || 
          msg.text().includes('decrypt') ||
          msg.text().includes('Decryption failed')) {
        consoleLogs.push(msg.text());
      }
    });
    
    try {
      // Step 1: Login
      await test.step('Login and setup', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);
        
        expect(page.url()).toContain('/dashboard');
        console.log('✅ User authenticated');
      });

      // Step 2: Create conversation and send message
      await test.step('Create conversation and send encrypted message', async () => {
        console.log('🔍 Creating conversation...');
        
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page.waitForTimeout(3000);
        
        console.log('📤 Sending encrypted message...');
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        const testMessage = 'Encryption fix test message 🔐';
        await messageInput.fill(testMessage);
        await sendButton.click();
        await page.waitForTimeout(5000);
        
        console.log('✅ Message sent');
      });

      // Step 3: Verify message display and encryption handling
      await test.step('Verify message display and encryption handling', async () => {
        console.log('🔍 Verifying message display...');
        
        // Check if MessageList is visible
        const messageList = page.locator('[data-testid="message-list"]');
        const messageListVisible = await messageList.isVisible();
        console.log(`💬 MessageList visible: ${messageListVisible}`);
        
        // Check for actual messages
        const messages = await page.locator('[data-testid="message"]').count();
        console.log(`💬 Message elements count: ${messages}`);
        
        // Check if our test message is visible (either encrypted or decrypted)
        const testMessage = 'Encryption fix test message 🔐';
        const testMessageVisible = await page.locator(`text="${testMessage}"`).isVisible();
        console.log(`💬 Test message visible: ${testMessageVisible}`);
        
        // Check for decryption failure indicator
        const decryptionFailedVisible = await page.locator('text="[Decryption failed]"').isVisible();
        console.log(`💬 Decryption failed indicator visible: ${decryptionFailedVisible}`);
        
        // Check for empty state
        const emptyStateVisible = await page.locator('text="No messages yet"').isVisible();
        console.log(`💬 Empty state visible: ${emptyStateVisible}`);
        
        // Log encryption-related console messages
        console.log('🔐 Encryption-related logs:');
        consoleLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
        
        // Verify the fix is working
        if (messageListVisible && messages > 0 && !emptyStateVisible) {
          console.log('🎉 SUCCESS: MessageList is displaying messages correctly!');
          
          if (testMessageVisible) {
            console.log('🎉 SUCCESS: Message content is visible (decrypted successfully)!');
          } else if (decryptionFailedVisible) {
            console.log('⚠️ INFO: Message shows decryption failed indicator (encryption fields present but decryption failed)');
          } else {
            console.log('🔍 INFO: Message content not visible as plain text (may be encrypted)');
          }
        } else {
          console.log('❌ ISSUE: MessageList not displaying messages properly');
        }
      });

      // Step 4: Check network requests for encryption fields
      await test.step('Check API response format', async () => {
        console.log('🔍 Checking API response format...');
        
        // Trigger a page refresh to force API call
        await page.reload();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        // Check if conversation list shows messages
        const conversationItems = await page.locator('[data-testid="conversation-item"]').count();
        console.log(`📋 Conversation items: ${conversationItems}`);
        
        if (conversationItems > 0) {
          // Click on the conversation to trigger messages API call
          await page.click('[data-testid="conversation-item"]');
          await page.waitForTimeout(3000);
          
          // Check if messages are displayed after refresh
          const messagesAfterRefresh = await page.locator('[data-testid="message"]').count();
          console.log(`💬 Messages after refresh: ${messagesAfterRefresh}`);
          
          if (messagesAfterRefresh > 0) {
            console.log('🎉 SUCCESS: Messages persist after refresh (API working correctly)!');
          } else {
            console.log('❌ ISSUE: Messages not displayed after refresh');
          }
        }
      });

      console.log('🎯 Encryption fix test completed!');

    } catch (error) {
      console.error('❌ Test failed:', error);
      console.log('🔐 Encryption logs at failure:');
      consoleLogs.forEach((log, index) => {
        console.log(`  ${index + 1}. ${log}`);
      });
      throw error;
    }
  });
});
