import { test, expect } from '@playwright/test';
import { DashboardPage } from '../../page-objects/DashboardPage';
import { TestDataManager } from '../../utils/test-data-manager';

test.describe('Message Deduplication Tests', () => {
  let testDataManager: TestDataManager;

  test.beforeEach(async () => {
    testDataManager = new TestDataManager();
  });

  test('should show messages exactly once without duplicates', async ({ browser }) => {
    // Create two browser contexts for two users
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    const dashboard1 = new DashboardPage(page1);
    const dashboard2 = new DashboardPage(page2);
    
    // Login as user 1
    const testUser1 = testDataManager.getTestUser('primary');
    await page1.goto('/login');
    await page1.fill('[data-testid="email-input"]', testUser1.email);
    await page1.fill('[data-testid="password-input"]', testUser1.password);
    await page1.click('[data-testid="login-button"]');
    await dashboard1.waitForPageLoad();
    
    // Login as user 2
    const testUser2 = testDataManager.getTestUser('secondary');
    await page2.goto('/login');
    await page2.fill('[data-testid="email-input"]', testUser2.email);
    await page2.fill('[data-testid="password-input"]', testUser2.password);
    await page2.click('[data-testid="login-button"]');
    await dashboard2.waitForPageLoad();
    
    // User 1 creates conversation with User 2
    await dashboard1.createDirectConversation(testUser2.username);
    
    // User 2 should see the conversation appear
    await page2.waitForTimeout(2000);
    await dashboard2.selectConversation(0);
    
    const messageText = 'Deduplication test message';
    
    // Get initial message counts
    const initialCount1 = await dashboard1.getMessageCount();
    const initialCount2 = await dashboard2.getMessageCount();
    
    // User 1 sends a message
    await dashboard1.sendMessage(messageText);
    
    // Wait for message to appear on both sides
    await page1.waitForTimeout(2000);
    await page2.waitForTimeout(2000);
    
    // Verify message appears exactly once for sender (User 1)
    const finalCount1 = await dashboard1.getMessageCount();
    expect(finalCount1).toBe(initialCount1 + 1);
    
    // Verify message appears exactly once for receiver (User 2)
    const finalCount2 = await dashboard2.getMessageCount();
    expect(finalCount2).toBe(initialCount2 + 1);
    
    // Verify message content is correct on both sides
    const lastMessage1 = await dashboard1.getLastMessage();
    const lastMessage2 = await dashboard2.getLastMessage();
    
    expect(lastMessage1).toContain(messageText);
    expect(lastMessage2).toContain(messageText);
    
    // Verify no duplicate messages by checking all messages contain unique content
    const allMessages1 = await page1.locator('[data-testid="message"]').allTextContents();
    const allMessages2 = await page2.locator('[data-testid="message"]').allTextContents();
    
    // Count occurrences of our test message
    const occurrences1 = allMessages1.filter(msg => msg.includes(messageText)).length;
    const occurrences2 = allMessages2.filter(msg => msg.includes(messageText)).length;
    
    expect(occurrences1).toBe(1);
    expect(occurrences2).toBe(1);
    
    await context1.close();
    await context2.close();
  });

  test('should maintain correct chronological order of messages', async ({ browser }) => {
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    const dashboard1 = new DashboardPage(page1);
    const dashboard2 = new DashboardPage(page2);
    
    // Login both users
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page1.goto('/login');
    await page1.fill('[data-testid="email-input"]', testUser1.email);
    await page1.fill('[data-testid="password-input"]', testUser1.password);
    await page1.click('[data-testid="login-button"]');
    await dashboard1.waitForPageLoad();
    
    await page2.goto('/login');
    await page2.fill('[data-testid="email-input"]', testUser2.email);
    await page2.fill('[data-testid="password-input"]', testUser2.password);
    await page2.click('[data-testid="login-button"]');
    await dashboard2.waitForPageLoad();
    
    // Create conversation
    await dashboard1.createDirectConversation(testUser2.username);
    await page2.waitForTimeout(2000);
    await dashboard2.selectConversation(0);
    
    // Send messages in sequence with delays
    const messages = [
      { sender: 'user1', text: 'First message', dashboard: dashboard1 },
      { sender: 'user2', text: 'Second message', dashboard: dashboard2 },
      { sender: 'user1', text: 'Third message', dashboard: dashboard1 },
      { sender: 'user2', text: 'Fourth message', dashboard: dashboard2 },
      { sender: 'user1', text: 'Fifth message', dashboard: dashboard1 }
    ];
    
    for (const message of messages) {
      await message.dashboard.sendMessage(message.text);
      await page1.waitForTimeout(1000); // Ensure proper ordering
      await page2.waitForTimeout(1000);
    }
    
    // Wait for all messages to be delivered
    await page1.waitForTimeout(3000);
    await page2.waitForTimeout(3000);
    
    // Verify message order on both sides
    const allMessages1 = await page1.locator('[data-testid="message"]').allTextContents();
    const allMessages2 = await page2.locator('[data-testid="message"]').allTextContents();
    
    // Extract just our test messages (filter out any existing messages)
    const testMessages1 = allMessages1.filter(msg => 
      messages.some(testMsg => msg.includes(testMsg.text))
    );
    const testMessages2 = allMessages2.filter(msg => 
      messages.some(testMsg => msg.includes(testMsg.text))
    );
    
    // Verify all messages are present
    expect(testMessages1.length).toBe(messages.length);
    expect(testMessages2.length).toBe(messages.length);
    
    // Verify order is correct
    for (let i = 0; i < messages.length; i++) {
      expect(testMessages1[i]).toContain(messages[i].text);
      expect(testMessages2[i]).toContain(messages[i].text);
    }
    
    await context1.close();
    await context2.close();
  });

  test('should handle optimistic UI updates correctly without duplicates', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    
    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Create conversation
    await dashboard.createDirectConversation(testUser2.username);
    
    const messageText = 'Optimistic update test';
    const initialCount = await dashboard.getMessageCount();
    
    // Send message
    await dashboard.sendMessage(messageText);
    
    // Verify message appears immediately (optimistic update)
    await expect(async () => {
      const currentCount = await dashboard.getMessageCount();
      expect(currentCount).toBe(initialCount + 1);
    }).toPass({ timeout: 1000 });
    
    // Wait for server confirmation
    await page.waitForTimeout(3000);
    
    // Verify message count hasn't changed (no duplicate)
    const finalCount = await dashboard.getMessageCount();
    expect(finalCount).toBe(initialCount + 1);
    
    // Verify message content is still correct
    const lastMessage = await dashboard.getLastMessage();
    expect(lastMessage).toContain(messageText);
    
    // Verify only one instance of the message exists
    const allMessages = await page.locator('[data-testid="message"]').allTextContents();
    const occurrences = allMessages.filter(msg => msg.includes(messageText)).length;
    expect(occurrences).toBe(1);
  });

  test('should prevent duplicate messages during rapid sending', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    
    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Create conversation
    await dashboard.createDirectConversation(testUser2.username);
    
    const messageText = 'Rapid send test';
    const initialCount = await dashboard.getMessageCount();
    
    // Try to send the same message multiple times rapidly
    await dashboard.messageInput.fill(messageText);
    
    // Click send button multiple times rapidly
    await dashboard.sendButton.click();
    await dashboard.sendButton.click();
    await dashboard.sendButton.click();
    
    // Wait for all operations to complete
    await page.waitForTimeout(5000);
    
    // Verify only one message was sent
    const finalCount = await dashboard.getMessageCount();
    expect(finalCount).toBe(initialCount + 1);
    
    // Verify only one instance of the message exists
    const allMessages = await page.locator('[data-testid="message"]').allTextContents();
    const occurrences = allMessages.filter(msg => msg.includes(messageText)).length;
    expect(occurrences).toBe(1);
  });

  test('should handle message deduplication across browser refresh', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    
    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Create conversation
    await dashboard.createDirectConversation(testUser2.username);
    
    const messageText = 'Refresh test message';
    
    // Send a message
    await dashboard.sendMessage(messageText);
    
    // Wait for message to be sent
    await page.waitForTimeout(3000);
    
    // Get message count before refresh
    const countBeforeRefresh = await dashboard.getMessageCount();
    
    // Refresh the page
    await page.reload();
    await dashboard.waitForPageLoad();
    
    // Navigate back to the conversation
    await dashboard.selectConversation(0);
    
    // Wait for messages to load
    await page.waitForTimeout(2000);
    
    // Verify message count is the same (no duplicates)
    const countAfterRefresh = await dashboard.getMessageCount();
    expect(countAfterRefresh).toBe(countBeforeRefresh);
    
    // Verify message content is still present
    const allMessages = await page.locator('[data-testid="message"]').allTextContents();
    const occurrences = allMessages.filter(msg => msg.includes(messageText)).length;
    expect(occurrences).toBe(1);
  });
});
