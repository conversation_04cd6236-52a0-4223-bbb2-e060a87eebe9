import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Dashboard Redux Debug', () => {
  test('should show Redux state changes in Dashboard console logs', async ({ page }) => {
    console.log('🔍 Starting Dashboard Redux debug test...');
    
    // Capture console logs
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      if (msg.text().includes('🏪 Dashboard Redux State') || msg.text().includes('Auth State')) {
        consoleLogs.push(msg.text());
      }
    });
    
    try {
      // Step 1: Login
      await test.step('Login and check initial state', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);
        
        expect(page.url()).toContain('/dashboard');
        console.log('✅ User authenticated');
        
        // Wait for initial state logs
        await page.waitForTimeout(2000);
        console.log('📊 Initial console logs:');
        consoleLogs.forEach(log => console.log(`  ${log}`));
      });

      // Step 2: Create conversation and track state changes
      await test.step('Create conversation and track state changes', async () => {
        console.log('🔍 Creating conversation...');
        
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        console.log('📊 Console logs after search:');
        consoleLogs.slice(-5).forEach(log => console.log(`  ${log}`));
        
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page.waitForTimeout(3000);
        
        console.log('📊 Console logs after user selection:');
        consoleLogs.slice(-5).forEach(log => console.log(`  ${log}`));
      });

      // Step 3: Send message and track state changes
      await test.step('Send message and track state changes', async () => {
        console.log('📤 Sending message...');
        
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        const testMessage = 'Dashboard Redux debug test message 🔍';
        
        await messageInput.fill(testMessage);
        await sendButton.click();
        await page.waitForTimeout(5000);
        
        console.log('📊 Console logs after sending message:');
        consoleLogs.slice(-10).forEach(log => console.log(`  ${log}`));
      });

      // Step 4: Check final UI state
      await test.step('Check final UI state', async () => {
        console.log('🔍 Checking final UI state...');
        
        // Check if ChatRoom is rendered
        const messageList = page.locator('[data-testid="message-list"]');
        const messageListVisible = await messageList.isVisible();
        console.log(`💬 Message list visible: ${messageListVisible}`);
        
        // Check if message appears
        const testMessage = 'Dashboard Redux debug test message 🔍';
        const messageVisible = await page.locator(`text="${testMessage}"`).isVisible();
        console.log(`💬 Message visible: ${messageVisible}`);
        
        // Check for "No conversation selected" message
        const noConversationMessage = page.locator('text="No conversation selected"');
        const noConversationVisible = await noConversationMessage.isVisible();
        console.log(`💬 "No conversation selected" visible: ${noConversationVisible}`);
        
        console.log('📊 All console logs:');
        consoleLogs.forEach((log, index) => console.log(`  ${index + 1}. ${log}`));
      });

      console.log('🎯 Dashboard Redux debug test completed!');

    } catch (error) {
      console.error('❌ Test failed:', error);
      console.log('📊 Console logs at failure:');
      consoleLogs.forEach((log, index) => console.log(`  ${index + 1}. ${log}`));
      throw error;
    }
  });
});
