import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Debug Message Sending Issue', () => {
  test('should reproduce and debug the message sending failure', async ({ page }) => {
    console.log('🔍 Starting debug test for message sending issue...');
    
    // Step 1: Login with test credentials
    await test.step('Login with test credentials', async () => {
      await page.goto('/login');
      await page.waitForLoadState('networkidle');
      
      console.log('📝 Filling login form...');
      await page.fill('input[placeholder*="email"]', '<EMAIL>');
      await page.fill('input[placeholder*="password"]', 'TestPassword123!');
      await page.click('button[type="submit"]');
      
      // Wait for authentication and redirect
      await page.waitForTimeout(3000);
      
      const currentUrl = page.url();
      console.log(`✅ Login completed. Current URL: ${currentUrl}`);
      
      if (!currentUrl.includes('/dashboard')) {
        throw new Error(`Lo<PERSON> failed - not redirected to dashboard. URL: ${currentUrl}`);
      }
    });

    // Step 2: Wait for Socket.IO and check connection
    await test.step('Check Socket.IO connection', async () => {
      console.log('🔌 Checking Socket.IO connection...');
      
      // Wait for Socket.IO to load
      await page.waitForTimeout(5000);
      
      const socketStatus = await page.evaluate(() => {
        return {
          socketIOAvailable: typeof (window as any).io !== 'undefined',
          socketConnected: (window as any).socketConnected || false,
          hasSocketContext: !!(window as any).socketContext
        };
      });
      
      console.log('🔌 Socket status:', socketStatus);
      
      // Check for connection status in UI
      const connectionIndicator = await page.locator('[data-testid="connection-status"], .connection-status').textContent().catch(() => null);
      console.log('🔌 Connection indicator:', connectionIndicator);
    });

    // Step 3: Open user search
    await test.step('Open user search', async () => {
      console.log('🔍 Opening user search...');
      
      const newChatButton = page.locator('[data-testid="new-chat-button"]');
      await expect(newChatButton).toBeVisible({ timeout: 10000 });
      await newChatButton.click();
      
      // Wait for search modal to open
      await page.waitForTimeout(1000);
      
      const searchModal = page.locator('[data-testid="user-search-input"]');
      await expect(searchModal).toBeVisible({ timeout: 5000 });
      console.log('✅ User search modal opened');
    });

    // Step 4: Search for testuser2
    await test.step('Search for testuser2', async () => {
      console.log('🔍 Searching for testuser2...');
      
      const searchInput = page.locator('[data-testid="user-search-input"]');
      await searchInput.fill('testuser2');
      
      // Wait for search results
      await page.waitForTimeout(2000);
      
      // Check if results appeared
      const searchResults = page.locator('[data-testid="user-search-results"]');
      const resultCount = await searchResults.locator('[data-testid="user-result"]').count();
      console.log(`🔍 Found ${resultCount} search results`);
      
      if (resultCount === 0) {
        // Log the search response for debugging
        const searchError = await page.locator('.error, [data-testid="error"]').textContent().catch(() => null);
        console.log('❌ No search results found. Error:', searchError);
        
        // Check network requests
        const requests = await page.evaluate(() => {
          return (window as any).lastSearchRequest || 'No search request logged';
        });
        console.log('🌐 Search request details:', requests);
      }
    });

    // Step 5: Select user from search results
    await test.step('Select user from search results', async () => {
      console.log('👤 Selecting user from search results...');
      
      const userButton = page.locator('[data-testid="user-action-button"]').first();
      
      if (await userButton.isVisible()) {
        await userButton.click();
        console.log('✅ User selected, conversation should be created');
        
        // Wait for modal to close and conversation to be selected
        await page.waitForTimeout(2000);
        
        // Check if we're now in a conversation
        const chatArea = page.locator('[data-testid="chat-area"]');
        const messageInput = page.locator('[data-testid="message-input"]');
        
        const chatAreaVisible = await chatArea.isVisible();
        const messageInputVisible = await messageInput.isVisible();
        
        console.log(`Chat area visible: ${chatAreaVisible}`);
        console.log(`Message input visible: ${messageInputVisible}`);
        
        if (!messageInputVisible) {
          console.log('❌ Message input not visible after user selection');
          
          // Check what's currently displayed
          const currentContent = await page.locator('[data-testid="chat-area"]').textContent();
          console.log('Current chat area content:', currentContent);
        }
      } else {
        console.log('❌ User action button not found');
      }
    });

    // Step 6: Attempt to send a message
    await test.step('Attempt to send a message', async () => {
      console.log('💬 Attempting to send a message...');
      
      const messageInput = page.locator('[data-testid="message-input"]');
      const sendButton = page.locator('[data-testid="send-button"]');
      
      if (await messageInput.isVisible()) {
        console.log('✅ Message input is visible');
        
        // Set up network monitoring
        const networkRequests: any[] = [];
        page.on('request', request => {
          networkRequests.push({
            url: request.url(),
            method: request.method(),
            headers: request.headers()
          });
        });
        
        // Set up console monitoring
        const consoleMessages: any[] = [];
        page.on('console', msg => {
          consoleMessages.push({
            type: msg.type(),
            text: msg.text()
          });
        });
        
        // Type a test message
        const testMessage = 'Hello testuser2! This is a debug test message 🔍';
        await messageInput.fill(testMessage);
        console.log(`📝 Typed message: "${testMessage}"`);
        
        // Check if send button is enabled
        const sendButtonEnabled = await sendButton.isEnabled();
        console.log(`Send button enabled: ${sendButtonEnabled}`);
        
        if (sendButtonEnabled) {
          console.log('🚀 Clicking send button...');
          await sendButton.click();
          
          // Wait for potential network activity
          await page.waitForTimeout(3000);
          
          // Check what happened
          console.log('📊 Network requests made:', networkRequests.length);
          networkRequests.forEach((req, index) => {
            console.log(`  ${index + 1}. ${req.method} ${req.url}`);
          });
          
          console.log('📊 Console messages:', consoleMessages.length);
          consoleMessages.forEach((msg, index) => {
            console.log(`  ${index + 1}. [${msg.type}] ${msg.text}`);
          });
          
          // Check if message appeared in chat
          const messageAppeared = await page.locator(`text="${testMessage}"`).isVisible();
          console.log(`Message appeared in chat: ${messageAppeared}`);
          
          // Check if input was cleared
          const inputValue = await messageInput.inputValue();
          console.log(`Input cleared after send: ${inputValue === ''}`);
          
          // Check socket events
          const socketEvents = await page.evaluate(() => {
            return (window as any).socketEvents || [];
          });
          console.log('🔌 Socket events:', socketEvents);
          
        } else {
          console.log('❌ Send button is disabled');
          
          // Check why it's disabled
          const inputValue = await messageInput.inputValue();
          const isConnected = await page.evaluate(() => {
            return (window as any).socketConnected || false;
          });
          
          console.log(`Input value: "${inputValue}"`);
          console.log(`Socket connected: ${isConnected}`);
        }
        
      } else {
        console.log('❌ Message input is not visible');
        
        // Check what's in the chat area instead
        const chatContent = await page.locator('[data-testid="chat-area"]').textContent();
        console.log('Chat area content:', chatContent);
      }
    });

    // Step 7: Debug conversation state
    await test.step('Debug conversation state', async () => {
      console.log('🔍 Debugging conversation state...');
      
      const conversationState = await page.evaluate(() => {
        // Access Redux store if available
        const store = (window as any).__REDUX_STORE__ || (window as any).store;
        if (store) {
          const state = store.getState();
          return {
            selectedConversationId: state.conversations?.selectedConversationId,
            conversations: Object.keys(state.conversations?.conversations || {}),
            draftConversations: Object.keys(state.conversations?.draftConversations || {}),
            messages: Object.keys(state.messages?.messages || {})
          };
        }
        return { error: 'Redux store not accessible' };
      });
      
      console.log('🏪 Redux state:', JSON.stringify(conversationState, null, 2));
      
      // Check local storage
      const localStorage = await page.evaluate(() => {
        const keys = Object.keys(window.localStorage);
        const storage: any = {};
        keys.forEach(key => {
          storage[key] = window.localStorage.getItem(key);
        });
        return storage;
      });
      
      console.log('💾 Local storage:', JSON.stringify(localStorage, null, 2));
    });

    console.log('🎯 Debug test completed. Check logs above for detailed analysis.');
  });
});
