import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Complete Encrypted Message Lifecycle', () => {
  test('should test complete message lifecycle: typing → loading → sent → delivered → read (all encrypted)', async ({ browser }) => {
    console.log('🧪 Starting complete encrypted message lifecycle test...');
    
    // Create two browser contexts for two users
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    // Track logs for both users
    const user1Logs: string[] = [];
    const user2Logs: string[] = [];
    const user1EncryptionLogs: string[] = [];
    const user2EncryptionLogs: string[] = [];
    const user1DatabaseLogs: string[] = [];
    const user2DatabaseLogs: string[] = [];
    
    // Setup log tracking for User 1
    page1.on('console', msg => {
      const text = `[USER1] ${msg.text()}`;
      user1Logs.push(text);
      
      if (text.includes('🔐') || text.includes('encrypt') || text.includes('session') || text.includes('SignalProtocol')) {
        user1EncryptionLogs.push(text);
      }
      
      if (text.includes('MessageStatusType') || text.includes('Prisma') || text.includes('messageStatusService')) {
        user1DatabaseLogs.push(text);
      }
    });
    
    // Setup log tracking for User 2
    page2.on('console', msg => {
      const text = `[USER2] ${msg.text()}`;
      user2Logs.push(text);
      
      if (text.includes('🔐') || text.includes('encrypt') || text.includes('session') || text.includes('SignalProtocol')) {
        user2EncryptionLogs.push(text);
      }
      
      if (text.includes('MessageStatusType') || text.includes('Prisma') || text.includes('messageStatusService')) {
        user2DatabaseLogs.push(text);
      }
    });
    
    try {
      // Step 1: Authenticate both users
      await test.step('Authenticate both users', async () => {
        // User 1 login
        await page1.goto('/login');
        await page1.waitForLoadState('networkidle');
        await page1.fill('input[placeholder*="email"]', '<EMAIL>');
        await page1.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page1.click('button[type="submit"]');
        await page1.waitForURL('**/dashboard', { timeout: 10000 });
        
        // User 2 login
        await page2.goto('/login');
        await page2.waitForLoadState('networkidle');
        await page2.fill('input[placeholder*="email"]', '<EMAIL>');
        await page2.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page2.click('button[type="submit"]');
        await page2.waitForURL('**/dashboard', { timeout: 10000 });
        
        console.log('✅ Both users authenticated');
        
        // Wait for encryption initialization
        await page1.waitForTimeout(5000);
        await page2.waitForTimeout(5000);
      });

      // Step 2: User1 creates conversation with User2
      await test.step('User1 creates conversation with User2', async () => {
        console.log('🔍 User1 creating conversation with User2...');
        
        await page1.click('[data-testid="new-chat-button"]');
        await page1.waitForTimeout(1000);
        
        const searchInput = page1.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible({ timeout: 5000 });
        
        await searchInput.fill('testuser2');
        await page1.waitForTimeout(2000);
        
        const userButton = page1.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible({ timeout: 5000 });
        await userButton.click();
        await page1.waitForTimeout(3000);
        
        console.log('✅ Conversation created');
      });

      // Step 3: Test typing indicators
      await test.step('Test typing indicators', async () => {
        console.log('🔍 Testing typing indicators...');
        
        const messageInput1 = page1.locator('[data-testid="message-input"]');
        await expect(messageInput1).toBeVisible({ timeout: 5000 });
        
        // Start typing on User1's side
        await messageInput1.focus();
        await messageInput1.type('Hello', { delay: 100 });
        
        // Wait for typing indicator to potentially appear on User2's side
        await page2.waitForTimeout(2000);
        
        // Check for typing indicator on User2's page
        const typingIndicator = page2.locator('[data-testid*="typing"], .typing-indicator, text=/typing/i');
        const isTypingVisible = await typingIndicator.isVisible();
        
        console.log(`📝 Typing indicator visible on User2: ${isTypingVisible}`);
        
        // Clear the input to stop typing
        await messageInput1.clear();
        await page1.waitForTimeout(1000);
      });

      // Step 4: Send encrypted message and track lifecycle
      await test.step('Send encrypted message and track complete lifecycle', async () => {
        console.log('🔍 Testing complete encrypted message lifecycle...');
        
        const messageInput1 = page1.locator('[data-testid="message-input"]');
        const sendButton1 = page1.locator('[data-testid="send-button"]');
        
        // Clear logs to focus on message lifecycle
        user1Logs.length = 0;
        user2Logs.length = 0;
        user1EncryptionLogs.length = 0;
        user2EncryptionLogs.length = 0;
        user1DatabaseLogs.length = 0;
        user2DatabaseLogs.length = 0;
        
        const testMessage = 'Complete lifecycle test message 🔐🚀';
        console.log(`📤 Sending message: ${testMessage}`);
        
        // Send the message
        await messageInput1.fill(testMessage);
        await sendButton1.click();
        
        // Wait for message processing
        await page1.waitForTimeout(3000);
        
        // Check if message appears on sender's side
        const messageOnSender = await page1.locator(`text="${testMessage}"`).isVisible();
        console.log(`📱 Message visible on sender (User1): ${messageOnSender}`);
        
        // Wait for real-time delivery to receiver
        await page2.waitForTimeout(5000);
        
        // Check if message appears on receiver's side
        const messageOnReceiver = await page2.locator(`text="${testMessage}"`).isVisible();
        console.log(`📱 Message visible on receiver (User2): ${messageOnReceiver}`);
      });

      // Step 5: Test message status updates (delivered/read)
      await test.step('Test message status updates', async () => {
        console.log('🔍 Testing message status updates...');
        
        // Look for status indicators on sender's side
        const statusIndicators1 = await page1.locator('[data-testid*="status"], .message-status, .status-indicator').count();
        console.log(`📊 Status indicators on User1: ${statusIndicators1}`);
        
        // Look for delivered/sent/read text
        const statusText1 = await page1.locator('text=/delivered|sent|read/i').count();
        console.log(`📊 Status text elements on User1: ${statusText1}`);
        
        // Simulate User2 reading the message (if visible)
        const messageOnUser2 = page2.locator(`text="${'Complete lifecycle test message 🔐🚀'}"`);
        if (await messageOnUser2.isVisible()) {
          await messageOnUser2.click();
          await page2.waitForTimeout(2000);
          console.log('📖 User2 clicked on message (simulating read)');
        }
        
        // Wait for potential status updates
        await page1.waitForTimeout(3000);
        
        // Check for updated status indicators
        const updatedStatusIndicators1 = await page1.locator('[data-testid*="status"], .message-status, .status-indicator').count();
        console.log(`📊 Updated status indicators on User1: ${updatedStatusIndicators1}`);
      });

      // Step 6: Analyze encryption and database logs
      await test.step('Analyze encryption and database behavior', async () => {
        console.log('🔍 Analyzing encryption and database behavior...');
        
        console.log('\n🔐 User1 Encryption Logs:');
        user1EncryptionLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
        
        console.log('\n🔐 User2 Encryption Logs:');
        user2EncryptionLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
        
        console.log('\n🗄️ User1 Database Logs:');
        user1DatabaseLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
        
        console.log('\n🗄️ User2 Database Logs:');
        user2DatabaseLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
        
        // Analyze encryption success/failure
        const encryptionSucceeded = user1EncryptionLogs.some(log => 
          log.includes('Message encrypted successfully')
        );
        
        const encryptionFailed = user1EncryptionLogs.some(log => 
          log.includes('Failed to encrypt message') ||
          log.includes('Falling back to unencrypted')
        );
        
        const sessionNotFound = user1EncryptionLogs.some(log => 
          log.includes('Session not found for conversation')
        );
        
        // Analyze database issues
        const databaseErrors = [...user1DatabaseLogs, ...user2DatabaseLogs].some(log => 
          log.includes('MessageStatusType') && log.includes('does not exist')
        );
        
        console.log('\n📊 Lifecycle Analysis:');
        console.log(`  - Encryption succeeded: ${encryptionSucceeded ? '✅' : '❌'}`);
        console.log(`  - Encryption failed: ${encryptionFailed ? '❌ ISSUE' : '✅'}`);
        console.log(`  - Session not found error: ${sessionNotFound ? '❌ ISSUE' : '✅'}`);
        console.log(`  - Database schema errors: ${databaseErrors ? '❌ ISSUE' : '✅'}`);
        
        // Report specific issues found
        if (sessionNotFound) {
          console.log('🎯 ISSUE CONFIRMED: Session not found for conversation');
        }
        if (encryptionFailed) {
          console.log('🎯 ISSUE CONFIRMED: Encryption failure with fallback to unencrypted');
        }
        if (databaseErrors) {
          console.log('🎯 ISSUE CONFIRMED: Database schema errors with MessageStatusType');
        }
      });

      // Step 7: Test message deduplication
      await test.step('Test message deduplication', async () => {
        console.log('🔍 Testing message deduplication...');
        
        // Count messages before
        const messagesBefore = await page1.locator('[data-testid="message"]').count();
        console.log(`📊 Messages before: ${messagesBefore}`);
        
        // Send another message quickly
        const messageInput1 = page1.locator('[data-testid="message-input"]');
        const sendButton1 = page1.locator('[data-testid="send-button"]');
        
        await messageInput1.fill('Deduplication test message');
        await sendButton1.click();
        await page1.waitForTimeout(1000);
        
        // Count messages after
        const messagesAfter = await page1.locator('[data-testid="message"]').count();
        console.log(`📊 Messages after: ${messagesAfter}`);
        
        const expectedIncrease = messagesAfter - messagesBefore;
        console.log(`📊 Message count increase: ${expectedIncrease} (should be 1)`);
      });

      console.log('\n🎯 Complete Encrypted Message Lifecycle Test Summary:');
      console.log('✅ User authentication');
      console.log('✅ Conversation creation');
      console.log('✅ Typing indicators');
      console.log('✅ Message encryption attempt');
      console.log('✅ Real-time delivery');
      console.log('✅ Message status updates');
      console.log('✅ Message deduplication');
      console.log('\nCheck logs above for specific encryption and database issues.');

    } finally {
      await context1.close();
      await context2.close();
    }
  });
});
