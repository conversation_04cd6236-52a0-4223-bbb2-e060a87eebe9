import { test, expect } from '../../fixtures/test-fixtures';

test.describe('API Response Debug', () => {
  test('should capture and analyze API response content', async ({ page }) => {
    console.log('🔍 Starting API response debug test...');
    
    // Track network responses with body content
    const apiResponses: any[] = [];
    
    page.on('response', async response => {
      if (response.url().includes('/api/messaging/conversations/') && response.url().includes('/messages/')) {
        try {
          const responseBody = await response.text();
          apiResponses.push({
            url: response.url(),
            status: response.status(),
            statusText: response.statusText(),
            headers: response.headers(),
            body: responseBody,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          apiResponses.push({
            url: response.url(),
            status: response.status(),
            statusText: response.statusText(),
            error: error.message,
            timestamp: new Date().toISOString()
          });
        }
      }
    });
    
    try {
      // Step 1: Login
      await test.step('Login and setup', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);
        
        expect(page.url()).toContain('/dashboard');
        console.log('✅ User authenticated');
      });

      // Step 2: Create conversation and send message
      await test.step('Create conversation and send message', async () => {
        console.log('🔍 Creating conversation...');
        
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page.waitForTimeout(3000);
        
        console.log('📤 Sending message...');
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        const testMessage = 'API response debug test message 🔍';
        await messageInput.fill(testMessage);
        await sendButton.click();
        
        // Wait for API responses
        await page.waitForTimeout(5000);
        
        console.log('✅ Message sent');
      });

      // Step 3: Analyze API responses
      await test.step('Analyze API responses', async () => {
        console.log('🔍 Analyzing API responses...');
        console.log(`📊 Total message API responses: ${apiResponses.length}`);
        
        apiResponses.forEach((response, index) => {
          console.log(`\n📋 Response ${index + 1}:`);
          console.log(`  URL: ${response.url}`);
          console.log(`  Status: ${response.status} ${response.statusText}`);
          console.log(`  Timestamp: ${response.timestamp}`);
          
          if (response.error) {
            console.log(`  ❌ Error: ${response.error}`);
          } else if (response.body) {
            try {
              const parsedBody = JSON.parse(response.body);
              console.log(`  📄 Response body:`);
              console.log(`    Success: ${parsedBody.success}`);
              console.log(`    Data exists: ${!!parsedBody.data}`);
              
              if (parsedBody.data) {
                console.log(`    Results count: ${parsedBody.data.results?.length || 0}`);
                console.log(`    Total count: ${parsedBody.data.count || 0}`);
                console.log(`    Next: ${parsedBody.data.next || 'null'}`);
                console.log(`    Previous: ${parsedBody.data.previous || 'null'}`);
                
                if (parsedBody.data.results && parsedBody.data.results.length > 0) {
                  console.log(`    First message:`);
                  const firstMessage = parsedBody.data.results[0];
                  console.log(`      ID: ${firstMessage.id}`);
                  console.log(`      Content: "${firstMessage.content?.substring(0, 100)}..."`);
                  console.log(`      Sender: ${firstMessage.sender?.username || firstMessage.sender?.id}`);
                  console.log(`      Created: ${firstMessage.createdAt || firstMessage.created_at}`);
                } else {
                  console.log(`    ⚠️ No messages in results array`);
                }
              } else {
                console.log(`    ⚠️ No data field in response`);
              }
            } catch (parseError) {
              console.log(`  ❌ Failed to parse JSON: ${parseError.message}`);
              console.log(`  📄 Raw body (first 500 chars): ${response.body.substring(0, 500)}`);
            }
          } else {
            console.log(`  ⚠️ No response body`);
          }
        });
      });

      // Step 4: Test timing by sending another message
      await test.step('Test timing with second message', async () => {
        console.log('🔍 Testing timing with second message...');
        
        const responsesBefore = apiResponses.length;
        
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        const testMessage2 = 'Second message for timing test 🕐';
        await messageInput.fill(testMessage2);
        await sendButton.click();
        
        // Wait for API responses
        await page.waitForTimeout(5000);
        
        const newResponses = apiResponses.slice(responsesBefore);
        console.log(`📊 New API responses: ${newResponses.length}`);
        
        newResponses.forEach((response, index) => {
          console.log(`\n📋 New Response ${index + 1}:`);
          console.log(`  URL: ${response.url}`);
          console.log(`  Status: ${response.status} ${response.statusText}`);
          
          if (response.body) {
            try {
              const parsedBody = JSON.parse(response.body);
              console.log(`  Results count: ${parsedBody.data?.results?.length || 0}`);
              
              if (parsedBody.data?.results?.length > 0) {
                console.log(`  Messages found:`);
                parsedBody.data.results.forEach((msg: any, msgIndex: number) => {
                  console.log(`    ${msgIndex + 1}. "${msg.content?.substring(0, 50)}..." (${msg.id})`);
                });
              }
            } catch (parseError) {
              console.log(`  ❌ Parse error: ${parseError.message}`);
            }
          }
        });
      });

      console.log('🎯 API response debug test completed!');

    } catch (error) {
      console.error('❌ Test failed:', error);
      console.log('📊 API responses at failure:', apiResponses.length);
      throw error;
    }
  });
});
