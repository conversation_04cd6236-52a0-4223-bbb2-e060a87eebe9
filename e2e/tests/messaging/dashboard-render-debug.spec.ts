import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Dashboard Render Debug', () => {
  test('should debug Dashboard conditional rendering logic', async ({ page }) => {
    console.log('🔍 Starting Dashboard render debug test...');
    
    // Capture console logs
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      if (msg.text().includes('🔍 Dashboard render check') || 
          msg.text().includes('✅ Rendering ChatRoom') || 
          msg.text().includes('❌ Rendering "No conversation selected"') ||
          msg.text().includes('🏪 Dashboard Redux State')) {
        consoleLogs.push(msg.text());
      }
    });
    
    try {
      // Step 1: Login
      await test.step('Login and check initial rendering', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);
        
        expect(page.url()).toContain('/dashboard');
        console.log('✅ User authenticated');
        
        // Wait for initial render logs
        await page.waitForTimeout(2000);
      });

      // Step 2: Create conversation and track rendering
      await test.step('Create conversation and track rendering', async () => {
        console.log('🔍 Creating conversation...');
        
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page.waitForTimeout(3000);
        
        console.log('📊 Logs after user selection:');
        consoleLogs.slice(-10).forEach(log => console.log(`  ${log}`));
      });

      // Step 3: Send message and track rendering changes
      await test.step('Send message and track rendering changes', async () => {
        console.log('📤 Sending message...');
        
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        const testMessage = 'Dashboard render debug test message 🔍';
        
        await messageInput.fill(testMessage);
        await sendButton.click();
        await page.waitForTimeout(5000);
        
        console.log('📊 Logs after sending message:');
        consoleLogs.slice(-15).forEach(log => console.log(`  ${log}`));
      });

      // Step 4: Check final UI state
      await test.step('Check final UI state', async () => {
        console.log('🔍 Checking final UI state...');
        
        // Check what's actually rendered
        const chatRoom = page.locator('[data-testid="message-list"]');
        const chatRoomVisible = await chatRoom.isVisible();
        console.log(`💬 ChatRoom (MessageList) visible: ${chatRoomVisible}`);
        
        const noConversationMessage = page.locator('text="No conversation selected"');
        const noConversationVisible = await noConversationMessage.isVisible();
        console.log(`💬 "No conversation selected" visible: ${noConversationVisible}`);
        
        const messageInput = page.locator('[data-testid="message-input"]');
        const messageInputVisible = await messageInput.isVisible();
        console.log(`💬 Message input visible: ${messageInputVisible}`);
        
        // Check if message appears anywhere
        const testMessage = 'Dashboard render debug test message 🔍';
        const messageVisible = await page.locator(`text="${testMessage}"`).isVisible();
        console.log(`💬 Message visible anywhere: ${messageVisible}`);
        
        console.log('📊 All console logs:');
        consoleLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log}`);
        });
      });

      console.log('🎯 Dashboard render debug test completed!');

    } catch (error) {
      console.error('❌ Test failed:', error);
      console.log('📊 Console logs at failure:');
      consoleLogs.forEach((log, index) => {
        console.log(`  ${index + 1}. ${log}`);
      });
      throw error;
    }
  });
});
