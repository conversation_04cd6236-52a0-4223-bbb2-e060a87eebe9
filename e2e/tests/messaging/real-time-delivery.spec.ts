import { test, expect } from '@playwright/test';
import { DashboardPage } from '../../page-objects/DashboardPage';
import { TestDataManager } from '../../utils/test-data-manager';

test.describe('Real-time Message Delivery Tests', () => {
  let testDataManager: TestDataManager;

  test.beforeEach(async () => {
    testDataManager = new TestDataManager();
  });

  test('should deliver messages in real-time between two users', async ({ browser }) => {
    // Create two browser contexts for two users
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    const dashboard1 = new DashboardPage(page1);
    const dashboard2 = new DashboardPage(page2);
    
    // Login both users
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page1.goto('/login');
    await page1.fill('[data-testid="email-input"]', testUser1.email);
    await page1.fill('[data-testid="password-input"]', testUser1.password);
    await page1.click('[data-testid="login-button"]');
    await dashboard1.waitForPageLoad();
    
    await page2.goto('/login');
    await page2.fill('[data-testid="email-input"]', testUser2.email);
    await page2.fill('[data-testid="password-input"]', testUser2.password);
    await page2.click('[data-testid="login-button"]');
    await dashboard2.waitForPageLoad();
    
    // User 1 creates conversation with User 2
    await dashboard1.createDirectConversation(testUser2.username);
    
    // User 2 should see the conversation appear
    await expect(async () => {
      const conversationCount = await dashboard2.getConversationCount();
      expect(conversationCount).toBeGreaterThan(0);
    }).toPass({ timeout: 10000 });
    
    await dashboard2.selectConversation(0);
    
    // Get initial message counts
    const initialCount1 = await dashboard1.getMessageCount();
    const initialCount2 = await dashboard2.getMessageCount();
    
    const messageText = 'Real-time delivery test';
    
    // User 1 sends a message
    await dashboard1.sendMessage(messageText);
    
    // Verify message appears for sender immediately (optimistic update)
    await expect(async () => {
      const count = await dashboard1.getMessageCount();
      expect(count).toBe(initialCount1 + 1);
    }).toPass({ timeout: 2000 });
    
    // Verify message appears for receiver in real-time
    await expect(async () => {
      const count = await dashboard2.getMessageCount();
      expect(count).toBe(initialCount2 + 1);
    }).toPass({ timeout: 5000 });
    
    // Verify message content is correct on both sides
    const lastMessage1 = await dashboard1.getLastMessage();
    const lastMessage2 = await dashboard2.getLastMessage();
    
    expect(lastMessage1).toContain(messageText);
    expect(lastMessage2).toContain(messageText);
    
    await context1.close();
    await context2.close();
  });

  test('should handle bidirectional messaging correctly', async ({ browser }) => {
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    const dashboard1 = new DashboardPage(page1);
    const dashboard2 = new DashboardPage(page2);
    
    // Login both users
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page1.goto('/login');
    await page1.fill('[data-testid="email-input"]', testUser1.email);
    await page1.fill('[data-testid="password-input"]', testUser1.password);
    await page1.click('[data-testid="login-button"]');
    await dashboard1.waitForPageLoad();
    
    await page2.goto('/login');
    await page2.fill('[data-testid="email-input"]', testUser2.email);
    await page2.fill('[data-testid="password-input"]', testUser2.password);
    await page2.click('[data-testid="login-button"]');
    await dashboard2.waitForPageLoad();
    
    // Create conversation
    await dashboard1.createDirectConversation(testUser2.username);
    await page2.waitForTimeout(2000);
    await dashboard2.selectConversation(0);
    
    // Exchange messages
    await dashboard1.sendMessage('Hello from User 1');
    await page1.waitForTimeout(1000);
    await page2.waitForTimeout(1000);
    
    await dashboard2.sendMessage('Hello from User 2');
    await page1.waitForTimeout(1000);
    await page2.waitForTimeout(1000);
    
    await dashboard1.sendMessage('How are you?');
    await page1.waitForTimeout(1000);
    await page2.waitForTimeout(1000);
    
    await dashboard2.sendMessage('I am fine, thanks!');
    await page1.waitForTimeout(1000);
    await page2.waitForTimeout(1000);
    
    // Verify both users see all 4 messages
    const count1 = await dashboard1.getMessageCount();
    const count2 = await dashboard2.getMessageCount();
    
    expect(count1).toBeGreaterThanOrEqual(4);
    expect(count2).toBeGreaterThanOrEqual(4);
    
    // Verify message content on both sides
    const messages1 = await page1.locator('[data-testid="message"]').allTextContents();
    const messages2 = await page2.locator('[data-testid="message"]').allTextContents();
    
    expect(messages1.some(msg => msg.includes('Hello from User 1'))).toBe(true);
    expect(messages1.some(msg => msg.includes('Hello from User 2'))).toBe(true);
    expect(messages1.some(msg => msg.includes('How are you?'))).toBe(true);
    expect(messages1.some(msg => msg.includes('I am fine, thanks!'))).toBe(true);
    
    expect(messages2.some(msg => msg.includes('Hello from User 1'))).toBe(true);
    expect(messages2.some(msg => msg.includes('Hello from User 2'))).toBe(true);
    expect(messages2.some(msg => msg.includes('How are you?'))).toBe(true);
    expect(messages2.some(msg => msg.includes('I am fine, thanks!'))).toBe(true);
    
    await context1.close();
    await context2.close();
  });

  test('should maintain socket connection status during messaging', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    
    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Verify connection status shows connected
    await expect(dashboard.connectionStatus).toContainText(/connected/i);
    
    // Create conversation
    await dashboard.createDirectConversation(testUser2.username);
    
    // Verify connection is still active
    await expect(dashboard.connectionStatus).toContainText(/connected/i);
    
    // Send a message
    await dashboard.sendMessage('Connection test message');
    
    // Verify connection remains stable during message sending
    await expect(dashboard.connectionStatus).toContainText(/connected/i);
    
    // Verify message input is enabled (indicates good connection)
    await expect(dashboard.messageInput).toBeEnabled();
    await expect(dashboard.sendButton).toBeEnabled();
  });

  test('should handle reconnection scenarios gracefully', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    
    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Create conversation
    await dashboard.createDirectConversation(testUser2.username);
    
    // Send a message while connected
    await dashboard.sendMessage('Message before disconnect');
    await page.waitForTimeout(2000);
    
    // Simulate network disconnection
    await page.route('**/socket.io/**', route => route.abort());
    
    // Wait for disconnection to be detected
    await page.waitForTimeout(3000);
    
    // Verify connection status shows disconnected/reconnecting
    await expect(dashboard.connectionStatus).toContainText(/reconnecting|connecting|disconnected/i);
    
    // Verify message input is disabled during disconnection
    await expect(dashboard.messageInput).toBeDisabled();
    
    // Restore connection
    await page.unroute('**/socket.io/**');
    
    // Wait for reconnection
    await expect(dashboard.connectionStatus).toContainText(/connected/i, { timeout: 10000 });
    
    // Verify message input is re-enabled
    await expect(dashboard.messageInput).toBeEnabled();
    
    // Send a message after reconnection
    await dashboard.sendMessage('Message after reconnect');
    
    // Verify message is sent successfully
    const lastMessage = await dashboard.getLastMessage();
    expect(lastMessage).toContain('Message after reconnect');
  });

  test('should handle message delivery across page refresh', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    
    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Create conversation
    await dashboard.createDirectConversation(testUser2.username);
    
    // Send messages before refresh
    await dashboard.sendMessage('Message 1 before refresh');
    await page.waitForTimeout(1000);
    await dashboard.sendMessage('Message 2 before refresh');
    await page.waitForTimeout(2000);
    
    // Get message count before refresh
    const countBeforeRefresh = await dashboard.getMessageCount();
    
    // Refresh the page
    await page.reload();
    await dashboard.waitForPageLoad();
    
    // Navigate back to the conversation
    await dashboard.selectConversation(0);
    await page.waitForTimeout(2000);
    
    // Verify messages are still there
    const countAfterRefresh = await dashboard.getMessageCount();
    expect(countAfterRefresh).toBe(countBeforeRefresh);
    
    // Send a message after refresh
    await dashboard.sendMessage('Message after refresh');
    
    // Verify new message appears
    await expect(async () => {
      const currentCount = await dashboard.getMessageCount();
      expect(currentCount).toBe(countBeforeRefresh + 1);
    }).toPass({ timeout: 5000 });
    
    // Verify all messages are present
    const allMessages = await page.locator('[data-testid="message"]').allTextContents();
    expect(allMessages.some(msg => msg.includes('Message 1 before refresh'))).toBe(true);
    expect(allMessages.some(msg => msg.includes('Message 2 before refresh'))).toBe(true);
    expect(allMessages.some(msg => msg.includes('Message after refresh'))).toBe(true);
  });

  test('should show read receipts when messages are viewed', async ({ browser }) => {
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    const dashboard1 = new DashboardPage(page1);
    const dashboard2 = new DashboardPage(page2);
    
    // Login both users
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page1.goto('/login');
    await page1.fill('[data-testid="email-input"]', testUser1.email);
    await page1.fill('[data-testid="password-input"]', testUser1.password);
    await page1.click('[data-testid="login-button"]');
    await dashboard1.waitForPageLoad();
    
    await page2.goto('/login');
    await page2.fill('[data-testid="email-input"]', testUser2.email);
    await page2.fill('[data-testid="password-input"]', testUser2.password);
    await page2.click('[data-testid="login-button"]');
    await dashboard2.waitForPageLoad();
    
    // Create conversation
    await dashboard1.createDirectConversation(testUser2.username);
    await page2.waitForTimeout(2000);
    await dashboard2.selectConversation(0);
    
    // User 1 sends a message
    await dashboard1.sendMessage('Read receipt test message');
    
    // Wait for message to be delivered to User 2
    await page2.waitForTimeout(3000);
    
    // User 2 views the message (should trigger read receipt)
    await page2.locator('[data-testid="message"]').last().scrollIntoViewIfNeeded();
    
    // Wait for read receipt to be processed
    await page1.waitForTimeout(3000);
    
    // Verify read receipt appears for User 1 (sender)
    const messageElement = page1.locator('[data-testid="message"]').last();
    const statusIndicator = messageElement.locator('[data-testid="message-status"], .message-status');
    
    // Should show read status (green double check or similar)
    await expect(statusIndicator).toBeVisible();
    
    // Check for read status indicators (green color, double check, etc.)
    const hasReadIndicator = await Promise.race([
      statusIndicator.locator('.text-green-500').isVisible(),
      statusIndicator.locator('[class*="green"]').isVisible(),
      statusIndicator.locator('svg').isVisible()
    ]);
    
    expect(hasReadIndicator).toBe(true);
    
    await context1.close();
    await context2.close();
  });
});
