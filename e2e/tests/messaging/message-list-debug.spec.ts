import { test, expect } from '../../fixtures/test-fixtures';

test.describe('MessageList Debug', () => {
  test('should debug MessageList component rendering and data flow', async ({ page }) => {
    console.log('🔍 Starting MessageList debug test...');
    
    // Capture console logs
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      if (msg.text().includes('MessageList') || msg.text().includes('messages') || msg.text().includes('🏪')) {
        consoleLogs.push(msg.text());
      }
    });
    
    try {
      // Step 1: Login
      await test.step('Login and setup', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);
        
        expect(page.url()).toContain('/dashboard');
        console.log('✅ User authenticated');
      });

      // Step 2: Add debugging to MessageList component
      await test.step('Add debugging to MessageList component', async () => {
        // Inject debugging code into the page
        await page.evaluate(() => {
          // Override console.log to capture MessageList logs
          const originalLog = console.log;
          (window as any).messageListLogs = [];
          
          console.log = function(...args: any[]) {
            const message = args.join(' ');
            if (message.includes('MessageList') || message.includes('messages') || message.includes('🔍')) {
              (window as any).messageListLogs.push(message);
            }
            return originalLog.apply(console, args);
          };
        });
        
        console.log('✅ Debugging injected');
      });

      // Step 3: Create conversation and track MessageList state
      await test.step('Create conversation and track MessageList state', async () => {
        console.log('🔍 Creating conversation...');
        
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page.waitForTimeout(3000);
        
        // Check if MessageList is rendered
        const messageList = page.locator('[data-testid="message-list"]');
        const messageListVisible = await messageList.isVisible();
        console.log(`💬 MessageList visible after conversation creation: ${messageListVisible}`);
        
        if (messageListVisible) {
          // Get MessageList properties
          const messageListProps = await page.evaluate(() => {
            const messageListElement = document.querySelector('[data-testid="message-list"]');
            if (messageListElement) {
              return {
                className: messageListElement.className,
                innerHTML: messageListElement.innerHTML.substring(0, 500),
                childElementCount: messageListElement.childElementCount,
                textContent: messageListElement.textContent?.substring(0, 200)
              };
            }
            return null;
          });
          
          console.log('💬 MessageList properties:', JSON.stringify(messageListProps, null, 2));
        }
      });

      // Step 4: Send message and track MessageList updates
      await test.step('Send message and track MessageList updates', async () => {
        console.log('📤 Sending message...');
        
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        const testMessage = 'MessageList debug test message 🔍';
        
        // Check MessageList state before sending
        const messagesBefore = await page.locator('[data-testid="message"]').count();
        console.log(`💬 Messages in MessageList before sending: ${messagesBefore}`);
        
        await messageInput.fill(testMessage);
        await sendButton.click();
        
        // Check MessageList state immediately after sending (optimistic update)
        await page.waitForTimeout(1000);
        const messagesAfterOptimistic = await page.locator('[data-testid="message"]').count();
        console.log(`💬 Messages in MessageList after optimistic update: ${messagesAfterOptimistic}`);
        
        // Wait for real-time confirmation
        await page.waitForTimeout(5000);
        const messagesAfterConfirmation = await page.locator('[data-testid="message"]').count();
        console.log(`💬 Messages in MessageList after confirmation: ${messagesAfterConfirmation}`);
        
        // Check if the specific message is visible
        const specificMessage = page.locator(`text="${testMessage}"`);
        const specificMessageVisible = await specificMessage.isVisible();
        console.log(`💬 Specific message visible: ${specificMessageVisible}`);
        
        if (specificMessageVisible) {
          // Check if it's inside the MessageList
          const messageInList = await page.locator('[data-testid="message-list"]').locator(`text="${testMessage}"`).isVisible();
          console.log(`💬 Message inside MessageList: ${messageInList}`);
          
          // Get the parent element of the message
          const messageParent = await page.evaluate((msg) => {
            const elements = Array.from(document.querySelectorAll('*')).filter(el => 
              el.textContent?.includes(msg)
            );
            return elements.map(el => ({
              tagName: el.tagName,
              className: el.className,
              id: el.id,
              testId: el.getAttribute('data-testid'),
              parentTestId: el.parentElement?.getAttribute('data-testid')
            }));
          }, testMessage);
          
          console.log('💬 Message parent elements:', JSON.stringify(messageParent, null, 2));
        }
        
        // Get MessageList final state
        const messageList = page.locator('[data-testid="message-list"]');
        if (await messageList.isVisible()) {
          const finalState = await page.evaluate(() => {
            const messageListElement = document.querySelector('[data-testid="message-list"]');
            if (messageListElement) {
              const messages = messageListElement.querySelectorAll('[data-testid="message"]');
              return {
                messageCount: messages.length,
                hasEmptyState: messageListElement.textContent?.includes('No messages yet'),
                hasLoadingState: messageListElement.textContent?.includes('Loading messages'),
                hasErrorState: messageListElement.textContent?.includes('Failed to load'),
                scrollHeight: messageListElement.scrollHeight,
                clientHeight: messageListElement.clientHeight,
                scrollTop: messageListElement.scrollTop
              };
            }
            return null;
          });
          
          console.log('💬 MessageList final state:', JSON.stringify(finalState, null, 2));
        }
        
        // Get any captured logs from the page
        const pageMessageListLogs = await page.evaluate(() => {
          return (window as any).messageListLogs || [];
        });
        
        console.log('📊 Page MessageList logs:');
        pageMessageListLogs.forEach((log: string, index: number) => {
          console.log(`  ${index + 1}. ${log}`);
        });
      });

      // Step 5: Check RTK Query state
      await test.step('Check RTK Query state', async () => {
        console.log('🔍 Checking RTK Query state...');
        
        const rtkQueryState = await page.evaluate(() => {
          // Try to access RTK Query cache
          const store = (window as any).__REDUX_STORE__ || (window as any).store;
          if (store) {
            const state = store.getState();
            const apiState = state.api;
            
            if (apiState && apiState.queries) {
              const queries = Object.keys(apiState.queries);
              const messageQueries = queries.filter(key => key.includes('getMessages'));
              
              return {
                totalQueries: queries.length,
                messageQueries: messageQueries.length,
                messageQueryDetails: messageQueries.map(key => ({
                  key,
                  status: apiState.queries[key]?.status,
                  data: apiState.queries[key]?.data ? 'has data' : 'no data',
                  error: apiState.queries[key]?.error ? 'has error' : 'no error'
                }))
              };
            }
          }
          return { error: 'RTK Query state not accessible' };
        });
        
        console.log('🔍 RTK Query state:', JSON.stringify(rtkQueryState, null, 2));
      });

      console.log('🎯 MessageList debug test completed!');
      
      console.log('📊 All captured console logs:');
      consoleLogs.forEach((log, index) => {
        console.log(`  ${index + 1}. ${log}`);
      });

    } catch (error) {
      console.error('❌ Test failed:', error);
      console.log('📊 Console logs at failure:');
      consoleLogs.forEach((log, index) => {
        console.log(`  ${index + 1}. ${log}`);
      });
      throw error;
    }
  });
});
