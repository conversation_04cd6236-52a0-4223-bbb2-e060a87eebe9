import { test, expect } from '../../fixtures/test-fixtures';

test.describe('UI State Transitions', () => {
  test('should track UI state transitions during message lifecycle', async ({ page }) => {
    console.log('🔍 Starting UI state transition tracking test...');
    
    try {
      // Step 1: Login
      await test.step('Login and setup', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);
        
        expect(page.url()).toContain('/dashboard');
        console.log('✅ User authenticated');
      });

      // Step 2: Create conversation and track UI states
      await test.step('Create conversation and track UI states', async () => {
        console.log('🔍 Opening user search...');
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        console.log('🔍 Searching for user...');
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        console.log('🔍 Selecting user...');
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page.waitForTimeout(2000);
        
        console.log('✅ Conversation created');
      });

      // Step 3: Track UI state before sending message
      await test.step('Track UI state before sending message', async () => {
        console.log('🔍 Checking UI state before sending message...');
        
        // Check for message list container
        const messageList = page.locator('[data-testid="message-list"]');
        const messageListVisible = await messageList.isVisible();
        console.log(`📋 Message list visible: ${messageListVisible}`);
        
        if (messageListVisible) {
          const messageListContent = await messageList.textContent();
          console.log(`📋 Message list content: "${messageListContent}"`);
          
          // Check for empty state
          const emptyState = page.locator('text="No messages yet"');
          const emptyStateVisible = await emptyState.isVisible();
          console.log(`📋 Empty state visible: ${emptyStateVisible}`);
          
          // Check for loading state
          const loadingState = page.locator('text="Loading messages..."');
          const loadingStateVisible = await loadingState.isVisible();
          console.log(`📋 Loading state visible: ${loadingStateVisible}`);
        }
        
        // Check for existing messages
        const existingMessages = await page.locator('[data-testid="message"]').count();
        console.log(`💬 Existing messages count: ${existingMessages}`);
        
        // Check message input
        const messageInput = page.locator('[data-testid="message-input"]');
        const messageInputVisible = await messageInput.isVisible();
        console.log(`📝 Message input visible: ${messageInputVisible}`);
      });

      // Step 4: Send message and track UI transitions
      await test.step('Send message and track UI transitions', async () => {
        console.log('📤 Sending message and tracking UI transitions...');
        
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        const testMessage = 'UI transition test message 🔄';
        
        // Fill message
        await messageInput.fill(testMessage);
        console.log('📝 Message typed');
        
        // Track UI state immediately before sending
        const messagesBefore = await page.locator('[data-testid="message"]').count();
        console.log(`💬 Messages before sending: ${messagesBefore}`);
        
        // Send message
        await sendButton.click();
        console.log('🚀 Send button clicked');
        
        // Track UI state immediately after sending (optimistic update)
        await page.waitForTimeout(500);
        const messagesAfterOptimistic = await page.locator('[data-testid="message"]').count();
        console.log(`💬 Messages after optimistic update: ${messagesAfterOptimistic}`);
        
        // Check if the specific message appears
        const specificMessage = page.locator(`text="${testMessage}"`);
        const specificMessageVisible = await specificMessage.isVisible();
        console.log(`💬 Specific message visible: ${specificMessageVisible}`);
        
        if (specificMessageVisible) {
          // Get the message element details
          const messageElement = await specificMessage.locator('..').first();
          const messageClasses = await messageElement.getAttribute('class');
          console.log(`💬 Message element classes: ${messageClasses}`);
          
          // Check if it's in the message list
          const messageInList = await page.locator('[data-testid="message-list"]').locator(`text="${testMessage}"`).isVisible();
          console.log(`💬 Message in message list: ${messageInList}`);
        }
        
        // Wait for real-time confirmation
        await page.waitForTimeout(3000);
        const messagesAfterConfirmation = await page.locator('[data-testid="message"]').count();
        console.log(`💬 Messages after confirmation: ${messagesAfterConfirmation}`);
        
        // Check message list state after sending
        const messageList = page.locator('[data-testid="message-list"]');
        if (await messageList.isVisible()) {
          const messageListHTML = await messageList.innerHTML();
          console.log(`📋 Message list HTML length: ${messageListHTML.length}`);
          
          // Check for any error states
          const errorState = page.locator('text="Failed to load messages"');
          const errorStateVisible = await errorState.isVisible();
          console.log(`❌ Error state visible: ${errorStateVisible}`);
        }
        
        // Verify input was cleared
        const inputValue = await messageInput.inputValue();
        console.log(`📝 Input cleared: ${inputValue === ''}`);
      });

      // Step 5: Analyze final UI state
      await test.step('Analyze final UI state', async () => {
        console.log('🔍 Analyzing final UI state...');
        
        // Get all message elements and their details
        const messageElements = page.locator('[data-testid="message"]');
        const messageCount = await messageElements.count();
        console.log(`💬 Total messages in UI: ${messageCount}`);
        
        for (let i = 0; i < messageCount; i++) {
          const message = messageElements.nth(i);
          const messageText = await message.textContent();
          const messageId = await message.getAttribute('data-message-id');
          const senderId = await message.getAttribute('data-sender-id');
          console.log(`💬 Message ${i + 1}: ID=${messageId}, Sender=${senderId}, Text="${messageText?.substring(0, 50)}..."`);
        }
        
        // Check message list container final state
        const messageList = page.locator('[data-testid="message-list"]');
        const messageListVisible = await messageList.isVisible();
        console.log(`📋 Final message list visible: ${messageListVisible}`);
        
        if (messageListVisible) {
          const messageListClasses = await messageList.getAttribute('class');
          console.log(`📋 Message list classes: ${messageListClasses}`);
          
          // Check scroll position
          const scrollTop = await messageList.evaluate(el => el.scrollTop);
          const scrollHeight = await messageList.evaluate(el => el.scrollHeight);
          const clientHeight = await messageList.evaluate(el => el.clientHeight);
          console.log(`📋 Scroll: top=${scrollTop}, height=${scrollHeight}, client=${clientHeight}`);
        }
        
        // Check for any hidden elements containing our message
        const testMessage = 'UI transition test message 🔄';
        const allElementsWithMessage = await page.locator('*').evaluateAll(elements => {
          return elements
            .filter(el => el.textContent?.includes('UI transition test message'))
            .map(el => ({
              tagName: el.tagName,
              className: el.className,
              textContent: el.textContent?.substring(0, 100),
              style: el.getAttribute('style'),
              hidden: el.hidden,
              offsetHeight: (el as HTMLElement).offsetHeight,
              offsetWidth: (el as HTMLElement).offsetWidth,
              display: window.getComputedStyle(el).display,
              visibility: window.getComputedStyle(el).visibility,
              opacity: window.getComputedStyle(el).opacity
            }));
        });
        
        console.log(`🔍 All elements containing message (${allElementsWithMessage.length}):`);
        allElementsWithMessage.forEach((el, index) => {
          console.log(`  ${index + 1}. ${el.tagName}.${el.className}: display=${el.display}, visibility=${el.visibility}, opacity=${el.opacity}, size=${el.offsetWidth}x${el.offsetHeight}`);
        });
      });

      // Step 6: Test message persistence
      await test.step('Test message persistence', async () => {
        console.log('🔄 Testing message persistence...');
        
        const testMessage = 'UI transition test message 🔄';
        
        // Check message is visible before refresh
        const messageBeforeRefresh = page.locator(`text="${testMessage}"`);
        const visibleBeforeRefresh = await messageBeforeRefresh.isVisible();
        console.log(`💬 Message visible before refresh: ${visibleBeforeRefresh}`);
        
        // Refresh page
        await page.reload();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        // Check if we need to reopen conversation
        const conversationList = page.locator('[data-testid="conversation-list"]');
        const conversationListVisible = await conversationList.isVisible();
        console.log(`📋 Conversation list visible after refresh: ${conversationListVisible}`);
        
        if (conversationListVisible) {
          const conversations = await page.locator('[data-testid="conversation-item"]').count();
          console.log(`📋 Conversations after refresh: ${conversations}`);
          
          if (conversations > 0) {
            console.log('🔄 Reopening conversation...');
            await page.click('[data-testid="conversation-item"]');
            await page.waitForTimeout(3000);
          }
        }
        
        // Check message persistence
        const messageAfterRefresh = page.locator(`text="${testMessage}"`);
        const visibleAfterRefresh = await messageAfterRefresh.isVisible();
        console.log(`💬 Message visible after refresh: ${visibleAfterRefresh}`);
        
        // Check message list state after refresh
        const messageList = page.locator('[data-testid="message-list"]');
        const messageListVisibleAfterRefresh = await messageList.isVisible();
        console.log(`📋 Message list visible after refresh: ${messageListVisibleAfterRefresh}`);
        
        if (messageListVisibleAfterRefresh) {
          const messagesAfterRefresh = await page.locator('[data-testid="message"]').count();
          console.log(`💬 Messages count after refresh: ${messagesAfterRefresh}`);
        }
      });

      console.log('🎯 UI state transition tracking completed!');

    } catch (error) {
      console.error('❌ Test failed:', error);
      throw error;
    }
  });
});
