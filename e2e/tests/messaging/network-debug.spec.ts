import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Network Debug', () => {
  test('should monitor network requests during message sending', async ({ page }) => {
    console.log('🔍 Starting network debug test...');
    
    // Track network requests
    const networkRequests: any[] = [];
    page.on('request', request => {
      networkRequests.push({
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
        timestamp: new Date().toISOString()
      });
    });
    
    page.on('response', response => {
      const request = networkRequests.find(req => req.url === response.url());
      if (request) {
        request.status = response.status();
        request.statusText = response.statusText();
        request.responseHeaders = response.headers();
      }
    });
    
    try {
      // Step 1: Login
      await test.step('Login and monitor requests', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);
        
        expect(page.url()).toContain('/dashboard');
        console.log('✅ User authenticated');
        
        // Log auth-related requests
        const authRequests = networkRequests.filter(req => 
          req.url.includes('/auth/') || req.url.includes('/login') || req.url.includes('/token')
        );
        console.log('🔐 Auth requests:', authRequests.length);
        authRequests.forEach(req => {
          console.log(`  ${req.method} ${req.url} - ${req.status || 'pending'}`);
        });
      });

      // Step 2: Create conversation and monitor requests
      await test.step('Create conversation and monitor requests', async () => {
        console.log('🔍 Creating conversation...');
        
        const requestsBefore = networkRequests.length;
        
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page.waitForTimeout(3000);
        
        const requestsAfter = networkRequests.length;
        console.log(`📊 Requests during conversation creation: ${requestsAfter - requestsBefore}`);
        
        // Log conversation-related requests
        const conversationRequests = networkRequests.slice(requestsBefore).filter(req => 
          req.url.includes('/conversations/') || req.url.includes('/users/') || req.url.includes('/search')
        );
        console.log('💬 Conversation requests:', conversationRequests.length);
        conversationRequests.forEach(req => {
          console.log(`  ${req.method} ${req.url} - ${req.status || 'pending'}`);
        });
      });

      // Step 3: Send message and monitor requests
      await test.step('Send message and monitor requests', async () => {
        console.log('📤 Sending message...');
        
        const requestsBefore = networkRequests.length;
        
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        const testMessage = 'Network debug test message 🔍';
        await messageInput.fill(testMessage);
        await sendButton.click();
        
        // Wait for requests to complete
        await page.waitForTimeout(5000);
        
        const requestsAfter = networkRequests.length;
        console.log(`📊 Requests during message sending: ${requestsAfter - requestsBefore}`);
        
        // Log message-related requests
        const messageRequests = networkRequests.slice(requestsBefore).filter(req => 
          req.url.includes('/messages/') || req.url.includes('/send') || req.url.includes('/api/messages')
        );
        console.log('💬 Message requests:', messageRequests.length);
        messageRequests.forEach(req => {
          console.log(`  ${req.method} ${req.url} - ${req.status || 'pending'}`);
          if (req.status && req.status >= 400) {
            console.log(`    ❌ Error response: ${req.statusText}`);
          }
        });
        
        // Log ALL requests during message sending for debugging
        const allMessageRequests = networkRequests.slice(requestsBefore);
        console.log('📊 ALL requests during message sending:');
        allMessageRequests.forEach((req, index) => {
          console.log(`  ${index + 1}. ${req.method} ${req.url} - ${req.status || 'pending'}`);
        });
      });

      // Step 4: Wait for any delayed requests
      await test.step('Wait for delayed requests', async () => {
        console.log('⏳ Waiting for delayed requests...');
        
        const requestsBefore = networkRequests.length;
        await page.waitForTimeout(5000);
        const requestsAfter = networkRequests.length;
        
        if (requestsAfter > requestsBefore) {
          console.log(`📊 Delayed requests: ${requestsAfter - requestsBefore}`);
          const delayedRequests = networkRequests.slice(requestsBefore);
          delayedRequests.forEach(req => {
            console.log(`  ${req.method} ${req.url} - ${req.status || 'pending'}`);
          });
        } else {
          console.log('📊 No delayed requests');
        }
      });

      // Step 5: Summary
      await test.step('Network summary', async () => {
        console.log('📊 Network Summary:');
        console.log(`  Total requests: ${networkRequests.length}`);
        
        const apiRequests = networkRequests.filter(req => req.url.includes('/api/'));
        console.log(`  API requests: ${apiRequests.length}`);
        
        const messageApiRequests = networkRequests.filter(req => 
          req.url.includes('/api/messages') || req.url.includes('messages')
        );
        console.log(`  Message API requests: ${messageApiRequests.length}`);
        
        const errorRequests = networkRequests.filter(req => req.status && req.status >= 400);
        console.log(`  Error requests: ${errorRequests.length}`);
        
        if (errorRequests.length > 0) {
          console.log('❌ Error requests:');
          errorRequests.forEach(req => {
            console.log(`  ${req.method} ${req.url} - ${req.status} ${req.statusText}`);
          });
        }
        
        // Check for specific message API patterns
        const getMessagesRequests = networkRequests.filter(req => 
          req.url.includes('/api/messages/') && req.method === 'GET'
        );
        console.log(`  GET /api/messages/ requests: ${getMessagesRequests.length}`);
        
        const postMessagesRequests = networkRequests.filter(req => 
          req.url.includes('/api/messages/') && req.method === 'POST'
        );
        console.log(`  POST /api/messages/ requests: ${postMessagesRequests.length}`);
      });

      console.log('🎯 Network debug test completed!');

    } catch (error) {
      console.error('❌ Test failed:', error);
      console.log('📊 Network requests at failure:', networkRequests.length);
      throw error;
    }
  });
});
