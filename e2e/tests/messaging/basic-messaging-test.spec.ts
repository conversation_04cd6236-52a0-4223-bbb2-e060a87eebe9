import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Basic Messaging Test', () => {
  test('should load the application and verify basic functionality', async ({ page }) => {
    console.log('🧪 Starting basic messaging test...');
    
    // Step 1: Navigate to the application
    await test.step('Navigate to application', async () => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      console.log('✅ Application loaded');
    });

    // Step 2: Check if login page is accessible
    await test.step('Check login page', async () => {
      // Try to navigate to login
      await page.goto('/login');
      await page.waitForLoadState('networkidle');
      
      // Check if login form elements are present
      const emailInput = page.locator('input[type="email"], [data-testid="email-input"]');
      const passwordInput = page.locator('input[type="password"], [data-testid="password-input"]');
      const loginButton = page.locator('button[type="submit"], [data-testid="login-button"]');
      
      await expect(emailInput).toBeVisible({ timeout: 10000 });
      await expect(passwordInput).toBeVisible({ timeout: 10000 });
      await expect(loginButton).toBeVisible({ timeout: 10000 });
      
      console.log('✅ Login form elements found');
    });

    // Step 3: Try to login with test credentials
    await test.step('Attempt login', async () => {
      // Fill login form
      await page.fill('input[type="email"], [data-testid="email-input"]', '<EMAIL>');
      await page.fill('input[type="password"], [data-testid="password-input"]', 'testpass123');
      
      // Submit form
      await page.click('button[type="submit"], [data-testid="login-button"]');
      
      // Wait for navigation or error
      await page.waitForTimeout(3000);
      
      // Check if we're redirected to dashboard or if there's an error
      const currentUrl = page.url();
      const hasError = await page.locator('.error, [data-testid="error-message"]').isVisible();
      
      console.log(`Current URL: ${currentUrl}`);
      console.log(`Has error: ${hasError}`);
      
      // If we're on dashboard, that's success
      if (currentUrl.includes('/dashboard') || currentUrl.includes('/chat')) {
        console.log('✅ Login successful - redirected to dashboard');
      } else if (hasError) {
        const errorText = await page.locator('.error, [data-testid="error-message"]').textContent();
        console.log(`❌ Login failed with error: ${errorText}`);
      } else {
        console.log('⚠️ Login result unclear - checking page content');
        
        // Check if we have dashboard elements
        const hasDashboard = await page.locator('[data-testid="dashboard"], .dashboard, .chat-container').isVisible();
        if (hasDashboard) {
          console.log('✅ Dashboard elements found - login likely successful');
        }
      }
    });

    // Step 4: Check for socket connection (if on dashboard)
    await test.step('Check socket connection', async () => {
      const currentUrl = page.url();
      if (currentUrl.includes('/dashboard') || currentUrl.includes('/chat')) {
        // Wait a bit for socket to connect
        await page.waitForTimeout(2000);
        
        // Check for connection indicators
        const connectionStatus = await page.evaluate(() => {
          // Look for connection status indicators
          const statusElement = document.querySelector('[data-testid="connection-status"], .connection-status');
          if (statusElement) {
            return statusElement.textContent;
          }
          
          // Check if there are no disconnection errors
          const errorElement = document.querySelector('.connection-error, .disconnected');
          return errorElement ? 'disconnected' : 'unknown';
        });
        
        console.log(`Connection status: ${connectionStatus}`);
      }
    });

    // Step 5: Check for encryption initialization (if applicable)
    await test.step('Check encryption status', async () => {
      const currentUrl = page.url();
      if (currentUrl.includes('/dashboard') || currentUrl.includes('/chat')) {
        // Wait for potential encryption initialization
        await page.waitForTimeout(3000);
        
        const encryptionStatus = await page.evaluate(() => {
          // Look for encryption status indicators
          const statusElement = document.querySelector('[data-testid="encryption-status"], .encryption-status');
          if (statusElement) {
            return statusElement.textContent;
          }
          
          // Check if encryption context seems to be working
          return 'unknown';
        });
        
        console.log(`Encryption status: ${encryptionStatus}`);
      }
    });

    console.log('🎉 Basic messaging test completed!');
  });

  test('should handle navigation between pages', async ({ page }) => {
    await test.step('Test page navigation', async () => {
      // Start at home
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // Navigate to login
      await page.goto('/login');
      await page.waitForLoadState('networkidle');
      
      // Check if login page loaded
      const hasLoginForm = await page.locator('input[type="email"], [data-testid="email-input"]').isVisible();
      expect(hasLoginForm).toBe(true);
      
      console.log('✅ Navigation between pages working');
    });
  });

  test('should verify API endpoints are accessible', async ({ page }) => {
    await test.step('Check API accessibility', async () => {
      // Test if we can reach the backend API
      const response = await page.request.get('/api/auth/status/').catch(() => null);
      
      if (response) {
        console.log(`API response status: ${response.status()}`);
        if (response.status() === 200 || response.status() === 401) {
          console.log('✅ Backend API is accessible');
        } else {
          console.log(`⚠️ Backend API returned status: ${response.status()}`);
        }
      } else {
        console.log('❌ Backend API not accessible');
      }
    });
  });

  test('should verify socket server is accessible', async ({ page }) => {
    await test.step('Check socket server', async () => {
      // Navigate to a page that would initialize socket connection
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // Check if socket.io script is loaded
      const hasSocketIO = await page.evaluate(() => {
        return typeof window !== 'undefined' && 
               (window as any).io !== undefined;
      });
      
      console.log(`Socket.IO available: ${hasSocketIO}`);
      
      if (hasSocketIO) {
        console.log('✅ Socket.IO library loaded');
      } else {
        console.log('⚠️ Socket.IO library not detected');
      }
    });
  });
});
