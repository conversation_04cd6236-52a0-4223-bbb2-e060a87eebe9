import { test, expect } from '../../fixtures/test-fixtures';

test.describe('API Debug', () => {
  test('should test the messages API directly', async ({ page }) => {
    console.log('🔍 Starting API debug test...');
    
    try {
      // Step 1: Login and get auth token
      await test.step('Login and get auth token', async () => {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        await page.fill('input[placeholder*="email"]', '<EMAIL>');
        await page.fill('input[placeholder*="password"]', 'TestPassword123!');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);
        
        expect(page.url()).toContain('/dashboard');
        console.log('✅ User authenticated');
      });

      // Step 2: Create conversation and send message
      await test.step('Create conversation and send message', async () => {
        console.log('🔍 Creating conversation...');
        
        await page.click('[data-testid="new-chat-button"]');
        await page.waitForTimeout(1000);
        
        const searchInput = page.locator('[data-testid="user-search-input"]');
        await expect(searchInput).toBeVisible();
        await searchInput.fill('testuser2');
        await page.waitForTimeout(2000);
        
        const userButton = page.locator('[data-testid="user-action-button"]').first();
        await expect(userButton).toBeVisible();
        await userButton.click();
        await page.waitForTimeout(3000);
        
        console.log('📤 Sending message...');
        const messageInput = page.locator('[data-testid="message-input"]');
        const sendButton = page.locator('[data-testid="send-button"]');
        
        const testMessage = 'API debug test message 🔍';
        await messageInput.fill(testMessage);
        await sendButton.click();
        await page.waitForTimeout(3000);
        
        console.log('✅ Message sent');
      });

      // Step 3: Test API directly
      await test.step('Test messages API directly', async () => {
        console.log('🔍 Testing messages API directly...');
        
        // Get the conversation ID from Redux state
        const conversationId = await page.evaluate(() => {
          const store = (window as any).__REDUX_STORE__ || (window as any).store;
          if (store) {
            const state = store.getState();
            return state.conversations?.selectedConversationId;
          }
          return null;
        });
        
        console.log('🔍 Conversation ID:', conversationId);
        
        if (conversationId && !conversationId.startsWith('draft-')) {
          // Test the API endpoint directly
          const apiResponse = await page.evaluate(async (convId) => {
            try {
              const response = await fetch(`/api/messages/?conversation_id=${convId}`, {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json',
                  // Include any auth headers that might be needed
                },
                credentials: 'include'
              });
              
              const data = await response.json();
              
              return {
                status: response.status,
                ok: response.ok,
                data: data,
                headers: Object.fromEntries(response.headers.entries())
              };
            } catch (error) {
              return {
                error: error.message,
                stack: error.stack
              };
            }
          }, conversationId);
          
          console.log('🔍 API Response:', JSON.stringify(apiResponse, null, 2));
          
          // Also test with authentication
          const authApiResponse = await page.evaluate(async (convId) => {
            try {
              // Get auth token from localStorage or cookies
              const token = localStorage.getItem('authToken') || 
                           sessionStorage.getItem('authToken') ||
                           document.cookie.split(';').find(c => c.trim().startsWith('authToken='))?.split('=')[1];
              
              const headers = {
                'Content-Type': 'application/json',
              };
              
              if (token) {
                headers['Authorization'] = `Bearer ${token}`;
              }
              
              const response = await fetch(`/api/messages/?conversation_id=${convId}`, {
                method: 'GET',
                headers,
                credentials: 'include'
              });
              
              const data = await response.json();
              
              return {
                status: response.status,
                ok: response.ok,
                data: data,
                token: token ? 'present' : 'missing',
                headers: Object.fromEntries(response.headers.entries())
              };
            } catch (error) {
              return {
                error: error.message,
                stack: error.stack
              };
            }
          }, conversationId);
          
          console.log('🔍 Auth API Response:', JSON.stringify(authApiResponse, null, 2));
        } else {
          console.log('❌ No real conversation ID found');
        }
      });

      // Step 4: Check network requests
      await test.step('Check network requests', async () => {
        console.log('🔍 Checking network requests...');
        
        // Wait a bit more for any pending requests
        await page.waitForTimeout(2000);
        
        // Get all network requests
        const requests = await page.evaluate(() => {
          return (window as any).networkRequests || [];
        });
        
        console.log('🔍 Network requests count:', requests.length);
        
        // Filter for messages API requests
        const messageRequests = requests.filter((req: any) => 
          req.url.includes('/api/messages/') || req.url.includes('messages')
        );
        
        console.log('🔍 Message API requests:', messageRequests.length);
        messageRequests.forEach((req: any, index: number) => {
          console.log(`  ${index + 1}. ${req.method} ${req.url} - ${req.status}`);
        });
      });

      console.log('🎯 API debug test completed!');

    } catch (error) {
      console.error('❌ Test failed:', error);
      throw error;
    }
  });
});
