import { test, expect } from '../../fixtures/test-fixtures';

test.describe('User Setup and Messaging Flow', () => {
  test('should register test users and test encrypted messaging', async ({ browser }) => {
    console.log('🧪 Starting user setup and messaging test...');
    
    // Create two browser contexts for two users
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    try {
      // Step 1: Register or login first user
      await test.step('Setup User 1', async () => {
        await page1.goto('/register');
        await page1.waitForLoadState('networkidle');
        
        // Try to register user1 using placeholder-based selectors
        const emailInput = page1.locator('input[placeholder*="email"]');
        const usernameInput = page1.locator('input[placeholder*="username"]');
        const firstNameInput = page1.locator('input[placeholder*="First name"]');
        const lastNameInput = page1.locator('input[placeholder*="Last name"]');
        const passwordInput = page1.locator('input[placeholder*="password"]');
        const confirmPasswordInput = page1.locator('input[placeholder*="Confirm"]');
        const submitButton = page1.locator('button[type="submit"]');

        if (await emailInput.isVisible()) {
          await emailInput.fill('<EMAIL>');
          await usernameInput.fill('testuser1');
          await firstNameInput.fill('Test');
          await lastNameInput.fill('User1');
          await passwordInput.fill('testpass123');
          await confirmPasswordInput.fill('testpass123');
          
          await submitButton.click();
          await page1.waitForTimeout(3000);
          
          // Check if registration was successful or if user already exists
          const currentUrl = page1.url();
          if (currentUrl.includes('/dashboard')) {
            console.log('✅ User1 registered and logged in successfully');
          } else {
            // Try to login instead
            await page1.goto('/login');
            await page1.waitForLoadState('networkidle');
            await page1.fill('input[placeholder*="email"]', '<EMAIL>');
            await page1.fill('input[placeholder*="password"]', 'testpass123');
            await page1.click('button[type="submit"]');
            await page1.waitForTimeout(3000);
            console.log('✅ User1 logged in');
          }
        }
      });

      // Step 2: Register or login second user
      await test.step('Setup User 2', async () => {
        await page2.goto('/register');
        await page2.waitForLoadState('networkidle');
        
        const emailInput = page2.locator('input[placeholder*="email"]');
        const usernameInput = page2.locator('input[placeholder*="username"]');
        const firstNameInput = page2.locator('input[placeholder*="First name"]');
        const lastNameInput = page2.locator('input[placeholder*="Last name"]');
        const passwordInput = page2.locator('input[placeholder*="password"]');
        const confirmPasswordInput = page2.locator('input[placeholder*="Confirm"]');
        const submitButton = page2.locator('button[type="submit"]');

        if (await emailInput.isVisible()) {
          await emailInput.fill('<EMAIL>');
          await usernameInput.fill('testuser2');
          await firstNameInput.fill('Test');
          await lastNameInput.fill('User2');
          await passwordInput.fill('testpass123');
          await confirmPasswordInput.fill('testpass123');
          
          await submitButton.click();
          await page2.waitForTimeout(3000);
          
          const currentUrl = page2.url();
          if (currentUrl.includes('/dashboard')) {
            console.log('✅ User2 registered and logged in successfully');
          } else {
            // Try to login instead
            await page2.goto('/login');
            await page2.waitForLoadState('networkidle');
            await page2.fill('input[placeholder*="email"]', '<EMAIL>');
            await page2.fill('input[placeholder*="password"]', 'testpass123');
            await page2.click('button[type="submit"]');
            await page2.waitForTimeout(3000);
            console.log('✅ User2 logged in');
          }
        }
      });

      // Step 3: Wait for both users to be on dashboard and check socket connections
      await test.step('Verify dashboard and socket connections', async () => {
        // Wait for both users to be on dashboard
        await page1.waitForTimeout(2000);
        await page2.waitForTimeout(2000);
        
        // Check if Socket.IO is available for both users
        const user1SocketAvailable = await page1.evaluate(() => {
          return typeof (window as any).io !== 'undefined';
        });
        
        const user2SocketAvailable = await page2.evaluate(() => {
          return typeof (window as any).io !== 'undefined';
        });
        
        console.log(`User1 Socket.IO available: ${user1SocketAvailable}`);
        console.log(`User2 Socket.IO available: ${user2SocketAvailable}`);
        
        // Wait for potential socket connections
        await page1.waitForTimeout(3000);
        await page2.waitForTimeout(3000);
      });

      // Step 4: Test user search functionality
      await test.step('Test user search', async () => {
        // Look for user search functionality on page1
        const searchInput = page1.locator('input[placeholder*="search"], input[placeholder*="user"], [data-testid="user-search"]');
        
        if (await searchInput.isVisible()) {
          await searchInput.fill('testuser2');
          await page1.waitForTimeout(2000);
          
          // Look for search results
          const searchResults = page1.locator('[data-testid="search-results"], .search-results, .user-list');
          if (await searchResults.isVisible()) {
            console.log('✅ User search functionality found');
            
            // Try to click on the first result
            const firstResult = searchResults.locator('button, .user-item').first();
            if (await firstResult.isVisible()) {
              await firstResult.click();
              await page1.waitForTimeout(2000);
              console.log('✅ User search result clicked');
            }
          }
        } else {
          console.log('⚠️ User search input not found');
        }
      });

      // Step 5: Test message sending
      await test.step('Test message sending', async () => {
        // Look for message input on page1
        const messageInput = page1.locator('input[placeholder*="message"], textarea[placeholder*="message"], [data-testid="message-input"]');
        
        if (await messageInput.isVisible()) {
          const testMessage = 'Hello from User1! This is a test message 🚀';
          await messageInput.fill(testMessage);
          
          // Look for send button
          const sendButton = page1.locator('button[type="submit"], [data-testid="send-button"], button:has-text("Send")');
          if (await sendButton.isVisible()) {
            await sendButton.click();
            await page1.waitForTimeout(2000);
            console.log('✅ Message sent from User1');
            
            // Check if message appears in chat
            const messageText = page1.locator(`text="${testMessage}"`);
            if (await messageText.isVisible()) {
              console.log('✅ Message appears in User1 chat');
            }
          }
        } else {
          console.log('⚠️ Message input not found');
        }
      });

      // Step 6: Check if message is received by User2
      await test.step('Check message reception', async () => {
        await page2.waitForTimeout(3000);
        
        // Look for the test message on page2
        const testMessage = 'Hello from User1! This is a test message 🚀';
        const messageText = page2.locator(`text="${testMessage}"`);
        
        if (await messageText.isVisible()) {
          console.log('✅ Message received by User2');
        } else {
          console.log('⚠️ Message not found on User2 - checking for any messages');
          
          // Check if there are any messages at all
          const anyMessage = page2.locator('.message, [data-testid="message"]');
          const messageCount = await anyMessage.count();
          console.log(`User2 has ${messageCount} messages visible`);
        }
      });

      // Step 7: Test bidirectional messaging
      await test.step('Test reply from User2', async () => {
        const messageInput = page2.locator('input[placeholder*="message"], textarea[placeholder*="message"], [data-testid="message-input"]');
        
        if (await messageInput.isVisible()) {
          const replyMessage = 'Hello back from User2! 👋';
          await messageInput.fill(replyMessage);
          
          const sendButton = page2.locator('button[type="submit"], [data-testid="send-button"], button:has-text("Send")');
          if (await sendButton.isVisible()) {
            await sendButton.click();
            await page2.waitForTimeout(2000);
            console.log('✅ Reply sent from User2');
            
            // Check if User1 receives the reply
            await page1.waitForTimeout(3000);
            const replyText = page1.locator(`text="${replyMessage}"`);
            if (await replyText.isVisible()) {
              console.log('✅ Reply received by User1');
            } else {
              console.log('⚠️ Reply not received by User1');
            }
          }
        }
      });

      console.log('🎉 User setup and messaging test completed!');

    } finally {
      await context1.close();
      await context2.close();
    }
  });
});
