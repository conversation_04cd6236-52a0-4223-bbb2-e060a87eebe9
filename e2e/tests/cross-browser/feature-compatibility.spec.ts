import { test, expect } from '@playwright/test';
import { TestUtils } from '../../fixtures/test-fixtures';
import { DashboardPage } from '../../page-objects/DashboardPage';
import { LoginPage } from '../../page-objects/LoginPage';
import { RegisterPage } from '../../page-objects/RegisterPage';

test.describe('Feature Compatibility Across Browsers', () => {
  test('should handle real-time messaging consistently across browsers', async ({ browser }) => {
    // Create two browser contexts (simulating different browsers)
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    try {
      // Authenticate both contexts
      await TestUtils.authenticateUser(context1, 'primary');
      await TestUtils.authenticateUser(context2, 'secondary');
      
      const page1 = await context1.newPage();
      const page2 = await context2.newPage();
      
      const dashboard1 = new DashboardPage(page1);
      const dashboard2 = new DashboardPage(page2);
      
      await dashboard1.goto();
      await dashboard2.goto();
      
      // Wait for connections
      await dashboard1.waitForConnectionEstablished();
      await dashboard2.waitForConnectionEstablished();
      
      // Set up conversation
      await TestUtils.setupConversation(dashboard1, 'testuser2');
      
      // Test real-time messaging
      await dashboard1.sendMessage('Cross-browser test message');
      await dashboard2.waitForNewMessage();
      
      const receivedMessage = await dashboard2.getLastMessage();
      expect(receivedMessage).toContain('Cross-browser test message');
      
    } finally {
      await context1.close();
      await context2.close();
    }
  });

  test('should handle typing indicators consistently across browsers', async ({ browser }) => {
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    try {
      await TestUtils.authenticateUser(context1, 'primary');
      await TestUtils.authenticateUser(context2, 'secondary');
      
      const page1 = await context1.newPage();
      const page2 = await context2.newPage();
      
      const dashboard1 = new DashboardPage(page1);
      const dashboard2 = new DashboardPage(page2);
      
      await dashboard1.goto();
      await dashboard2.goto();
      
      await dashboard1.waitForConnectionEstablished();
      await dashboard2.waitForConnectionEstablished();
      
      await TestUtils.setupConversation(dashboard1, 'testuser2');
      
      // Test typing indicators
      await dashboard1.simulateTyping('Typing test...');
      await dashboard2.waitForTypingIndicator();
      
      expect(await dashboard2.isTypingIndicatorVisible()).toBe(true);
      
      await dashboard1.stopTyping();
      await dashboard2.waitForTypingIndicatorToDisappear();
      
      expect(await dashboard2.isTypingIndicatorVisible()).toBe(false);
      
    } finally {
      await context1.close();
      await context2.close();
    }
  });

  test('should handle authentication consistently across browsers', async ({ browser }) => {
    const browsers = ['chromium', 'firefox', 'webkit'];
    const results: Record<string, boolean> = {};
    
    for (const browserName of browsers) {
      try {
        const context = await browser.newContext();
        const page = await context.newPage();
        
        const loginPage = new LoginPage(page);
        
        await loginPage.goto();
        
        // Test login form
        await loginPage.fillEmail('<EMAIL>');
        await loginPage.fillPassword('password123');
        
        // Verify form state
        const emailValue = await loginPage.getEmailValue();
        const passwordValue = await loginPage.getPasswordValue();
        
        results[browserName] = 
          emailValue === '<EMAIL>' && 
          passwordValue === 'password123';
        
        await context.close();
      } catch (error) {
        results[browserName] = false;
      }
    }
    
    // Verify all browsers handle authentication consistently
    for (const browserName of browsers) {
      expect(results[browserName]).toBe(true);
    }
  });

  test('should handle form validation consistently across browsers', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      const registerPage = new RegisterPage(page);
      
      await registerPage.goto();
      
      // Test email validation
      await registerPage.fillEmail('invalid-email');
      
      const isEmailValid = await page.locator('input[type="email"]').evaluate((el: HTMLInputElement) => {
        return el.validity.valid;
      });
      
      expect(isEmailValid).toBe(false);
      
      // Test valid email
      await registerPage.fillEmail('<EMAIL>');
      
      const isEmailValidNow = await page.locator('input[type="email"]').evaluate((el: HTMLInputElement) => {
        return el.validity.valid;
      });
      
      expect(isEmailValidNow).toBe(true);
      
    } finally {
      await context.close();
    }
  });

  test('should handle WebSocket connections consistently across browsers', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      await page.goto('/login');
      
      // Test WebSocket support and functionality
      const webSocketTest = await page.evaluate(async () => {
        return new Promise((resolve) => {
          try {
            const ws = new WebSocket('ws://localhost:7000');
            
            ws.onopen = () => {
              ws.close();
              resolve({ supported: true, canConnect: true });
            };
            
            ws.onerror = () => {
              resolve({ supported: true, canConnect: false });
            };
            
            // Timeout after 5 seconds
            setTimeout(() => {
              ws.close();
              resolve({ supported: true, canConnect: false });
            }, 5000);
            
          } catch (error) {
            resolve({ supported: false, canConnect: false });
          }
        });
      });
      
      expect(webSocketTest.supported).toBe(true);
      
    } finally {
      await context.close();
    }
  });

  test('should handle local storage consistently across browsers', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      await page.goto('/login');
      
      // Test localStorage functionality
      const storageTest = await page.evaluate(() => {
        try {
          // Test setting and getting values
          localStorage.setItem('test-key', 'test-value');
          const retrieved = localStorage.getItem('test-key');
          
          // Test JSON storage
          const testObj = { key: 'value', number: 123 };
          localStorage.setItem('test-object', JSON.stringify(testObj));
          const retrievedObj = JSON.parse(localStorage.getItem('test-object') || '{}');
          
          // Test removal
          localStorage.removeItem('test-key');
          const afterRemoval = localStorage.getItem('test-key');
          
          return {
            basicStorage: retrieved === 'test-value',
            jsonStorage: retrievedObj.key === 'value' && retrievedObj.number === 123,
            removal: afterRemoval === null,
            supported: true
          };
        } catch (error) {
          return { supported: false, error: error instanceof Error ? error.message : String(error) };
        }
      });
      
      expect(storageTest.supported).toBe(true);
      expect(storageTest.basicStorage).toBe(true);
      expect(storageTest.jsonStorage).toBe(true);
      expect(storageTest.removal).toBe(true);
      
    } finally {
      await context.close();
    }
  });

  test('should handle CSS features consistently across browsers', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      await page.goto('/login');
      
      // Test CSS Grid support
      const cssSupport = await page.evaluate(() => {
        const testElement = document.createElement('div');
        document.body.appendChild(testElement);
        
        try {
          // Test CSS Grid
          testElement.style.display = 'grid';
          const gridSupported = getComputedStyle(testElement).display === 'grid';
          
          // Test Flexbox
          testElement.style.display = 'flex';
          const flexSupported = getComputedStyle(testElement).display === 'flex';
          
          // Test CSS Variables
          testElement.style.setProperty('--test-var', 'red');
          const variableSupported = testElement.style.getPropertyValue('--test-var') === 'red';
          
          document.body.removeChild(testElement);
          
          return {
            grid: gridSupported,
            flex: flexSupported,
            variables: variableSupported
          };
        } catch (error) {
          document.body.removeChild(testElement);
          return { error: error instanceof Error ? error.message : String(error) };
        }
      });
      
      expect(cssSupport.grid).toBe(true);
      expect(cssSupport.flex).toBe(true);
      expect(cssSupport.variables).toBe(true);
      
    } finally {
      await context.close();
    }
  });

  test('should handle JavaScript APIs consistently across browsers', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      await page.goto('/login');
      
      // Test modern JavaScript APIs
      const apiSupport = await page.evaluate(() => {
        return {
          fetch: typeof fetch !== 'undefined',
          promise: typeof Promise !== 'undefined',
          asyncAwait: (async () => true) instanceof Promise,
          destructuring: (() => {
            try {
              const { length } = 'test';
              return length === 4;
            } catch {
              return false;
            }
          })(),
          arrowFunctions: (() => {
            try {
              const arrow = () => true;
              return arrow();
            } catch {
              return false;
            }
          })(),
          templateLiterals: (() => {
            try {
              const test = 'world';
              return `hello ${test}` === 'hello world';
            } catch {
              return false;
            }
          })(),
          classes: (() => {
            try {
              class TestClass {}
              return new TestClass() instanceof TestClass;
            } catch {
              return false;
            }
          })()
        };
      });
      
      expect(apiSupport.fetch).toBe(true);
      expect(apiSupport.promise).toBe(true);
      expect(apiSupport.asyncAwait).toBe(true);
      expect(apiSupport.destructuring).toBe(true);
      expect(apiSupport.arrowFunctions).toBe(true);
      expect(apiSupport.templateLiterals).toBe(true);
      expect(apiSupport.classes).toBe(true);
      
    } finally {
      await context.close();
    }
  });

  test('should handle responsive design consistently across browsers', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      await page.goto('/login');
      
      const viewports = [
        { width: 1920, height: 1080, name: 'desktop' },
        { width: 768, height: 1024, name: 'tablet' },
        { width: 375, height: 667, name: 'mobile' }
      ];
      
      for (const viewport of viewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        
        // Verify elements are visible and properly sized
        const emailInput = page.locator('input[type="email"]');
        await expect(emailInput).toBeVisible();
        
        const boundingBox = await emailInput.boundingBox();
        expect(boundingBox).not.toBeNull();
        expect(boundingBox!.width).toBeGreaterThan(0);
        expect(boundingBox!.height).toBeGreaterThan(0);
        
        // Verify element fits within viewport
        expect(boundingBox!.x + boundingBox!.width).toBeLessThanOrEqual(viewport.width);
        expect(boundingBox!.y + boundingBox!.height).toBeLessThanOrEqual(viewport.height);
      }
      
    } finally {
      await context.close();
    }
  });

  test('should handle error scenarios consistently across browsers', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      // Test network error handling
      await page.route('**/api/**', route => route.abort());
      
      await page.goto('/login');
      
      const loginPage = new LoginPage(page);
      
      await loginPage.fillEmail('<EMAIL>');
      await loginPage.fillPassword('password123');
      await loginPage.clickLogin();
      
      // Should handle network error gracefully
      await page.waitForTimeout(2000);
      
      // Verify error handling (implementation dependent)
      const hasError = await page.locator('.error, [data-testid="error-message"]').isVisible();
      
      // Either shows error or handles gracefully
      // The important thing is that the browser doesn't crash
      expect(typeof hasError).toBe('boolean');
      
    } finally {
      await context.close();
    }
  });

  test('should handle performance consistently across browsers', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      const startTime = Date.now();
      await page.goto('/login');
      const loadTime = Date.now() - startTime;
      
      // Page should load within reasonable time (10 seconds)
      expect(loadTime).toBeLessThan(10000);
      
      // Test JavaScript execution performance
      const jsPerformance = await page.evaluate(() => {
        const start = performance.now();
        
        // Perform some operations
        const arr = Array.from({ length: 1000 }, (_, i) => i);
        const doubled = arr.map(x => x * 2);
        const sum = doubled.reduce((a, b) => a + b, 0);
        
        const end = performance.now();
        return {
          duration: end - start,
          result: sum
        };
      });
      
      // JavaScript operations should complete quickly
      expect(jsPerformance.duration).toBeLessThan(100);
      expect(jsPerformance.result).toBe(999000); // Expected sum
      
    } finally {
      await context.close();
    }
  });
});
