import { test, expect, devices } from '@playwright/test';
import { TestUtils } from '../../fixtures/test-fixtures';

// Test across different browsers
const browsers = ['chromium', 'firefox', 'webkit'];

test.describe('Cross-Browser Compatibility', () => {
  browsers.forEach(browserName => {
    test.describe(`${browserName} Browser Tests`, () => {
      test(`should load application correctly in ${browserName}`, async ({ page, browserName: currentBrowser }) => {
        test.skip(currentBrowser !== browserName, `Skipping ${browserName} test in ${currentBrowser}`);
        
        await page.goto('/login');
        
        // Verify basic page elements load
        await expect(page.locator('input[type="email"]')).toBeVisible();
        await expect(page.locator('input[type="password"]')).toBeVisible();
        await expect(page.locator('button[type="submit"]')).toBeVisible();
        
        // Verify no console errors
        const consoleErrors: string[] = [];
        page.on('console', msg => {
          if (msg.type() === 'error') {
            consoleErrors.push(msg.text());
          }
        });
        
        await page.waitForTimeout(2000);
        
        // Filter out known acceptable errors
        const criticalErrors = consoleErrors.filter(error => 
          !error.includes('favicon') && 
          !error.includes('404') &&
          !error.includes('WebSocket') &&
          !error.includes('net::ERR_')
        );
        
        expect(criticalErrors).toHaveLength(0);
      });

      test(`should handle authentication flow in ${browserName}`, async ({ page, browserName: currentBrowser }) => {
        test.skip(currentBrowser !== browserName, `Skipping ${browserName} test in ${currentBrowser}`);
        
        await page.goto('/login');
        
        // Test login form functionality
        await page.fill('input[type="email"]', '<EMAIL>');
        await page.fill('input[type="password"]', 'password123');
        
        // Verify form interaction works
        const emailValue = await page.inputValue('input[type="email"]');
        const passwordValue = await page.inputValue('input[type="password"]');
        
        expect(emailValue).toBe('<EMAIL>');
        expect(passwordValue).toBe('password123');
        
        // Test form submission (without actually submitting)
        const submitButton = page.locator('button[type="submit"]');
        expect(await submitButton.isEnabled()).toBe(true);
      });

      test(`should handle CSS rendering correctly in ${browserName}`, async ({ page, browserName: currentBrowser }) => {
        test.skip(currentBrowser !== browserName, `Skipping ${browserName} test in ${currentBrowser}`);
        
        await page.goto('/login');
        
        // Check if CSS is loaded and applied
        const emailInput = page.locator('input[type="email"]');
        
        // Verify element is styled (has computed styles)
        const computedStyle = await emailInput.evaluate(el => {
          const style = window.getComputedStyle(el);
          return {
            display: style.display,
            visibility: style.visibility,
            opacity: style.opacity
          };
        });
        
        expect(computedStyle.display).not.toBe('none');
        expect(computedStyle.visibility).not.toBe('hidden');
        expect(parseFloat(computedStyle.opacity)).toBeGreaterThan(0);
      });

      test(`should handle JavaScript events correctly in ${browserName}`, async ({ page, browserName: currentBrowser }) => {
        test.skip(currentBrowser !== browserName, `Skipping ${browserName} test in ${currentBrowser}`);
        
        await page.goto('/login');
        
        // Test click events
        const emailInput = page.locator('input[type="email"]');
        await emailInput.click();
        
        // Verify focus event
        const isFocused = await emailInput.evaluate(el => el === document.activeElement);
        expect(isFocused).toBe(true);
        
        // Test keyboard events
        await emailInput.type('<EMAIL>');
        const value = await emailInput.inputValue();
        expect(value).toBe('<EMAIL>');
        
        // Test form validation events
        await emailInput.clear();
        await emailInput.blur();
        
        // Check if validation triggers (implementation dependent)
        await page.waitForTimeout(500);
      });

      test(`should handle responsive design in ${browserName}`, async ({ page, browserName: currentBrowser }) => {
        test.skip(currentBrowser !== browserName, `Skipping ${browserName} test in ${currentBrowser}`);
        
        await page.goto('/login');
        
        // Test desktop view
        await page.setViewportSize({ width: 1920, height: 1080 });
        await expect(page.locator('input[type="email"]')).toBeVisible();
        
        // Test tablet view
        await page.setViewportSize({ width: 768, height: 1024 });
        await expect(page.locator('input[type="email"]')).toBeVisible();
        
        // Test mobile view
        await page.setViewportSize({ width: 375, height: 667 });
        await expect(page.locator('input[type="email"]')).toBeVisible();
        
        // Verify layout adapts to different screen sizes
        const emailInput = page.locator('input[type="email"]');
        const boundingBox = await emailInput.boundingBox();
        
        expect(boundingBox).not.toBeNull();
        expect(boundingBox!.width).toBeGreaterThan(0);
        expect(boundingBox!.height).toBeGreaterThan(0);
      });

      test(`should handle local storage in ${browserName}`, async ({ page, browserName: currentBrowser }) => {
        test.skip(currentBrowser !== browserName, `Skipping ${browserName} test in ${currentBrowser}`);
        
        await page.goto('/login');
        
        // Test localStorage functionality
        await page.evaluate(() => {
          localStorage.setItem('test-key', 'test-value');
        });
        
        const storedValue = await page.evaluate(() => {
          return localStorage.getItem('test-key');
        });
        
        expect(storedValue).toBe('test-value');
        
        // Test sessionStorage functionality
        await page.evaluate(() => {
          sessionStorage.setItem('session-key', 'session-value');
        });
        
        const sessionValue = await page.evaluate(() => {
          return sessionStorage.getItem('session-key');
        });
        
        expect(sessionValue).toBe('session-value');
      });

      test(`should handle WebSocket connections in ${browserName}`, async ({ page, browserName: currentBrowser }) => {
        test.skip(currentBrowser !== browserName, `Skipping ${browserName} test in ${currentBrowser}`);
        
        await page.goto('/login');
        
        // Test WebSocket support
        const webSocketSupported = await page.evaluate(() => {
          return typeof WebSocket !== 'undefined';
        });
        
        expect(webSocketSupported).toBe(true);
        
        // Test WebSocket creation (without actually connecting)
        const canCreateWebSocket = await page.evaluate(() => {
          try {
            const ws = new WebSocket('ws://localhost:7000');
            ws.close();
            return true;
          } catch (error) {
            return false;
          }
        });
        
        expect(canCreateWebSocket).toBe(true);
      });

      test(`should handle modern JavaScript features in ${browserName}`, async ({ page, browserName: currentBrowser }) => {
        test.skip(currentBrowser !== browserName, `Skipping ${browserName} test in ${currentBrowser}`);
        
        await page.goto('/login');
        
        // Test ES6+ features support
        const modernFeaturesSupport = await page.evaluate(() => {
          try {
            // Test arrow functions
            const arrow = () => 'arrow';
            
            // Test template literals
            const template = `template ${arrow()}`;
            
            // Test destructuring
            const { length } = 'test';
            
            // Test async/await
            const asyncTest = async () => 'async';
            
            // Test Promises
            const promise = Promise.resolve('promise');
            
            // Test Map/Set
            const map = new Map();
            const set = new Set();
            
            return {
              arrow: arrow() === 'arrow',
              template: template === 'template arrow',
              destructuring: length === 4,
              async: typeof asyncTest === 'function',
              promise: promise instanceof Promise,
              map: map instanceof Map,
              set: set instanceof Set
            };
          } catch (error) {
            return { error: error.message };
          }
        });
        
        expect(modernFeaturesSupport.arrow).toBe(true);
        expect(modernFeaturesSupport.template).toBe(true);
        expect(modernFeaturesSupport.destructuring).toBe(true);
        expect(modernFeaturesSupport.async).toBe(true);
        expect(modernFeaturesSupport.promise).toBe(true);
        expect(modernFeaturesSupport.map).toBe(true);
        expect(modernFeaturesSupport.set).toBe(true);
      });

      test(`should handle form validation consistently in ${browserName}`, async ({ page, browserName: currentBrowser }) => {
        test.skip(currentBrowser !== browserName, `Skipping ${browserName} test in ${currentBrowser}`);
        
        await page.goto('/register');
        
        // Test HTML5 validation
        const emailInput = page.locator('input[type="email"]');
        
        // Test invalid email
        await emailInput.fill('invalid-email');
        
        const isValid = await emailInput.evaluate((el: HTMLInputElement) => {
          return el.validity.valid;
        });
        
        expect(isValid).toBe(false);
        
        // Test valid email
        await emailInput.fill('<EMAIL>');
        
        const isValidNow = await emailInput.evaluate((el: HTMLInputElement) => {
          return el.validity.valid;
        });
        
        expect(isValidNow).toBe(true);
      });

      test(`should handle file operations in ${browserName}`, async ({ page, browserName: currentBrowser }) => {
        test.skip(currentBrowser !== browserName, `Skipping ${browserName} test in ${currentBrowser}`);
        
        await page.goto('/login');
        
        // Test File API support
        const fileApiSupport = await page.evaluate(() => {
          return {
            File: typeof File !== 'undefined',
            FileReader: typeof FileReader !== 'undefined',
            Blob: typeof Blob !== 'undefined',
            FormData: typeof FormData !== 'undefined'
          };
        });
        
        expect(fileApiSupport.File).toBe(true);
        expect(fileApiSupport.FileReader).toBe(true);
        expect(fileApiSupport.Blob).toBe(true);
        expect(fileApiSupport.FormData).toBe(true);
      });

      test(`should handle date and time operations in ${browserName}`, async ({ page, browserName: currentBrowser }) => {
        test.skip(currentBrowser !== browserName, `Skipping ${browserName} test in ${currentBrowser}`);
        
        await page.goto('/login');
        
        // Test Date operations
        const dateOperations = await page.evaluate(() => {
          const now = new Date();
          const iso = now.toISOString();
          const locale = now.toLocaleString();
          
          return {
            now: now instanceof Date,
            iso: typeof iso === 'string' && iso.includes('T'),
            locale: typeof locale === 'string' && locale.length > 0,
            timestamp: typeof now.getTime() === 'number'
          };
        });
        
        expect(dateOperations.now).toBe(true);
        expect(dateOperations.iso).toBe(true);
        expect(dateOperations.locale).toBe(true);
        expect(dateOperations.timestamp).toBe(true);
      });
    });
  });

  test('should work consistently across all browsers', async ({ browser }) => {
    // This test runs once and creates contexts for all browsers
    const testResults: Record<string, boolean> = {};
    
    // Test basic functionality in each browser
    for (const browserName of browsers) {
      try {
        const context = await browser.newContext();
        const page = await context.newPage();
        
        await page.goto('/login');
        
        // Basic functionality test
        await page.fill('input[type="email"]', '<EMAIL>');
        await page.fill('input[type="password"]', 'password123');
        
        const emailValue = await page.inputValue('input[type="email"]');
        const passwordValue = await page.inputValue('input[type="password"]');
        
        testResults[browserName] = 
          emailValue === '<EMAIL>' && 
          passwordValue === 'password123';
        
        await context.close();
      } catch (error) {
        testResults[browserName] = false;
      }
    }
    
    // Verify all browsers passed
    for (const browserName of browsers) {
      expect(testResults[browserName]).toBe(true);
    }
  });
});

test.describe('Mobile Browser Compatibility', () => {
  const mobileDevices = [
    { name: 'iPhone 12', device: devices['iPhone 12'] },
    { name: 'Pixel 5', device: devices['Pixel 5'] },
    { name: 'iPad', device: devices['iPad Pro'] }
  ];

  mobileDevices.forEach(({ name, device }) => {
    test(`should work on ${name}`, async ({ browser }) => {
      const context = await browser.newContext({
        ...device
      });
      
      const page = await context.newPage();
      
      try {
        await page.goto('/login');
        
        // Test touch interactions
        await page.tap('input[type="email"]');
        await page.fill('input[type="email"]', '<EMAIL>');
        
        await page.tap('input[type="password"]');
        await page.fill('input[type="password"]', 'password123');
        
        // Verify mobile layout
        const viewport = page.viewportSize();
        expect(viewport).not.toBeNull();
        
        if (viewport) {
          expect(viewport.width).toBeLessThanOrEqual(device.viewport.width);
          expect(viewport.height).toBeLessThanOrEqual(device.viewport.height);
        }
        
        // Verify form elements are accessible on mobile
        await expect(page.locator('input[type="email"]')).toBeVisible();
        await expect(page.locator('input[type="password"]')).toBeVisible();
        await expect(page.locator('button[type="submit"]')).toBeVisible();
        
      } finally {
        await context.close();
      }
    });
  });
});
