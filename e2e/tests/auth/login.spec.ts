import { test, expect } from '../../fixtures/test-fixtures';
import { TestUtils } from '../../fixtures/test-fixtures';

test.describe('User Login', () => {
  test.beforeEach(async ({ page }) => {
    // Ensure we start from a clean state
    await page.goto('/login');
  });

  test('should successfully login with valid credentials', async ({ loginPage, testDataManager, page }) => {
    const testUser = testDataManager.getTestUser('primary');
    
    await loginPage.login(testUser.email, testUser.password);
    await loginPage.waitForLoginSuccess();
    
    // Verify redirect to dashboard
    expect(page.url()).toContain('/dashboard');
  });

  test('should show error for invalid email', async ({ loginPage, page }) => {
    // Capture console logs
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      consoleLogs.push(`${msg.type()}: ${msg.text()}`);
      console.log(`Browser console [${msg.type()}]:`, msg.text());
    });

    await loginPage.login('<EMAIL>', 'password123');

    // Wait a bit for any async operations
    await page.waitForTimeout(2000);

    // Log all captured console messages
    console.log('All console logs:', consoleLogs);

    await loginPage.waitForLoginError();

    const errorMessage = await loginPage.getErrorMessage();
    expect(errorMessage).toContain('Invalid credentials');
  });

  test('should show error for invalid password', async ({ loginPage, testDataManager }) => {
    const testUser = testDataManager.getTestUser('primary');
    
    await loginPage.login(testUser.email, 'wrongpassword');
    await loginPage.waitForLoginError();
    
    const errorMessage = await loginPage.getErrorMessage();
    expect(errorMessage).toContain('Invalid credentials');
  });

  test('should validate required fields', async ({ loginPage }) => {
    // Try to login with empty fields
    await loginPage.clickLogin();
    
    // Check for validation errors
    await loginPage.expectEmailValidationError();
    await loginPage.expectPasswordValidationError();
  });

  test('should validate email format', async ({ loginPage }) => {
    await loginPage.fillEmail('invalid-email');
    await loginPage.fillPassword('password123');
    await loginPage.clickLogin();
    
    await loginPage.expectEmailValidationError();
  });

  test('should toggle password visibility', async ({ loginPage }) => {
    await loginPage.fillPassword('testpassword');
    
    // Initially password should be masked
    expect(await loginPage.isPasswordVisible()).toBe(false);
    
    // Toggle visibility
    await loginPage.togglePasswordVisibility();
    expect(await loginPage.isPasswordVisible()).toBe(true);
    
    // Toggle back
    await loginPage.togglePasswordVisibility();
    expect(await loginPage.isPasswordVisible()).toBe(false);
  });

  test('should navigate to register page', async ({ loginPage, page }) => {
    await loginPage.clickRegisterLink();
    
    await page.waitForURL('**/register');
    expect(page.url()).toContain('/register');
  });

  test('should handle loading states', async ({ loginPage, testDataManager, page }) => {
    const testUser = testDataManager.getTestUser('primary');
    
    // Simulate slow network
    await page.route('**/api/auth/login/', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });
    
    await loginPage.fillEmail(testUser.email);
    await loginPage.fillPassword(testUser.password);
    await loginPage.clickLogin();
    
    // Check loading state
    expect(await loginPage.isLoading()).toBe(true);
    
    // Wait for loading to finish
    await loginPage.waitForLoadingToFinish();
    expect(await loginPage.isLoading()).toBe(false);
  });

  test('should prevent XSS attacks', async ({ loginPage }) => {
    await loginPage.testXSSPrevention();
    
    // Verify no script execution occurred
    const logs = await page.evaluate(() => window.console);
    // Add specific XSS prevention checks based on your implementation
  });

  test('should handle network errors gracefully', async ({ loginPage, testDataManager, page }) => {
    const testUser = testDataManager.getTestUser('primary');
    
    // Simulate network failure
    await page.route('**/api/auth/login/', route => route.abort());
    
    await loginPage.login(testUser.email, testUser.password);
    await loginPage.waitForLoginError();
    
    const errorMessage = await loginPage.getErrorMessage();
    expect(errorMessage).toContain('network');
  });

  test('should handle server errors', async ({ loginPage, testDataManager, page }) => {
    const testUser = testDataManager.getTestUser('primary');
    
    // Simulate server error
    await page.route('**/api/auth/login/', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      });
    });
    
    await loginPage.login(testUser.email, testUser.password);
    await loginPage.waitForLoginError();
    
    const errorMessage = await loginPage.getErrorMessage();
    expect(errorMessage).toContain('server error');
  });

  test('should maintain form state during errors', async ({ loginPage, testDataManager }) => {
    const testUser = testDataManager.getTestUser('primary');
    
    await loginPage.fillEmail(testUser.email);
    await loginPage.fillPassword('wrongpassword');
    await loginPage.clickLogin();
    
    await loginPage.waitForLoginError();
    
    // Verify form values are preserved
    expect(await loginPage.getEmailValue()).toBe(testUser.email);
    expect(await loginPage.getPasswordValue()).toBe('wrongpassword');
  });

  test('should be accessible', async ({ loginPage }) => {
    await loginPage.checkAccessibility();
  });

  test('should work with keyboard navigation', async ({ loginPage, testDataManager, page }) => {
    const testUser = testDataManager.getTestUser('primary');
    
    // Navigate using Tab key
    await page.keyboard.press('Tab'); // Focus email
    await page.keyboard.type(testUser.email);
    
    await page.keyboard.press('Tab'); // Focus password
    await page.keyboard.type(testUser.password);
    
    await page.keyboard.press('Tab'); // Focus login button
    await page.keyboard.press('Enter'); // Submit form
    
    await loginPage.waitForLoginSuccess();
    expect(page.url()).toContain('/dashboard');
  });

  test('should handle case-insensitive email login', async ({ loginPage, testDataManager, page }) => {
    const testUser = testDataManager.getTestUser('primary');
    const uppercaseEmail = testUser.email.toUpperCase();
    
    await loginPage.login(uppercaseEmail, testUser.password);
    await loginPage.waitForLoginSuccess();
    
    expect(page.url()).toContain('/dashboard');
  });

  test('should clear form when needed', async ({ loginPage }) => {
    await loginPage.fillEmail('<EMAIL>');
    await loginPage.fillPassword('password123');
    
    await loginPage.clearForm();
    
    expect(await loginPage.getEmailValue()).toBe('');
    expect(await loginPage.getPasswordValue()).toBe('');
  });

  test('should handle multiple failed login attempts', async ({ loginPage, testDataManager }) => {
    const testUser = testDataManager.getTestUser('primary');
    
    // Attempt multiple failed logins
    for (let i = 0; i < 3; i++) {
      await loginPage.login(testUser.email, 'wrongpassword');
      await loginPage.waitForLoginError();
      await loginPage.clearForm();
    }
    
    // Verify account is not locked (or handle according to your implementation)
    await loginPage.login(testUser.email, testUser.password);
    await loginPage.waitForLoginSuccess();
  });
});

test.describe('Login Security', () => {
  test('should prevent password masking bypass', async ({ loginPage }) => {
    await loginPage.checkPasswordMasking();
  });

  test('should handle CSRF protection', async ({ loginPage, testDataManager, page }) => {
    const testUser = testDataManager.getTestUser('primary');
    
    // Remove CSRF token if present
    await page.evaluate(() => {
      const csrfInputs = document.querySelectorAll('input[name*="csrf"]');
      csrfInputs.forEach(input => input.remove());
    });
    
    await loginPage.login(testUser.email, testUser.password);
    
    // Should either succeed (if CSRF not required) or show appropriate error
    // Adjust based on your CSRF implementation
  });

  test('should sanitize input data', async ({ loginPage, page }) => {
    const maliciousInput = '<script>alert("xss")</script>';
    
    await loginPage.fillEmail(maliciousInput);
    await loginPage.fillPassword(maliciousInput);
    
    // Verify no script execution
    const alertDialogs: string[] = [];
    page.on('dialog', dialog => {
      alertDialogs.push(dialog.message());
      dialog.dismiss();
    });

    await loginPage.clickLogin();
    await page.waitForTimeout(1000);

    expect(alertDialogs).toHaveLength(0);
  });
});
