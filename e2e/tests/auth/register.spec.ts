import { test, expect } from '../../fixtures/test-fixtures';
import { TestUtils } from '../../fixtures/test-fixtures';

test.describe('User Registration', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/register');
  });

  test('should successfully register with valid data', async ({ registerPage, page }) => {
    const userData = {
      firstName: 'Test',
      lastName: 'User',
      email: TestUtils.generateRandomEmail(),
      password: 'TestPassword123!',
      username: TestUtils.generateRandomUsername()
    };
    
    await registerPage.register(userData);
    await registerPage.waitForRegistrationSuccess();
    
    // Verify redirect to dashboard
    expect(page.url()).toContain('/dashboard');
  });

  test('should validate required fields', async ({ registerPage }) => {
    await registerPage.clickRegister();
    
    // Check for validation errors on required fields
    await registerPage.expectFieldValidationError('firstName');
    await registerPage.expectFieldValidationError('lastName');
    await registerPage.expectFieldValidationError('email');
    await registerPage.expectFieldValidationError('password');
  });

  test('should validate email format', async ({ registerPage }) => {
    await registerPage.testEmailValidation();
  });

  test('should validate password strength', async ({ registerPage }) => {
    await registerPage.testPasswordValidation();
  });

  test('should validate password confirmation', async ({ registerPage }) => {
    await registerPage.testPasswordConfirmation();
  });

  test('should test all required field validation', async ({ registerPage }) => {
    await registerPage.testRequiredFields();
  });

  test('should handle duplicate email registration', async ({ registerPage, testDataManager }) => {
    const existingUser = testDataManager.getTestUser('primary');
    
    const userData = {
      firstName: 'New',
      lastName: 'User',
      email: existingUser.email, // Use existing email
      password: 'NewPassword123!',
      username: TestUtils.generateRandomUsername()
    };
    
    await registerPage.register(userData);
    await registerPage.waitForRegistrationError();
    
    const errorMessage = await registerPage.getErrorMessage();
    expect(errorMessage).toContain('email');
    expect(errorMessage).toContain('already exists');
  });

  test('should toggle password visibility', async ({ registerPage }) => {
    await registerPage.fillPassword('testpassword');
    
    // Initially password should be masked
    expect(await registerPage.isPasswordVisible()).toBe(false);
    
    // Toggle visibility
    await registerPage.togglePasswordVisibility();
    expect(await registerPage.isPasswordVisible()).toBe(true);
    
    // Toggle back
    await registerPage.togglePasswordVisibility();
    expect(await registerPage.isPasswordVisible()).toBe(false);
  });

  test('should navigate to login page', async ({ registerPage, page }) => {
    await registerPage.clickLoginLink();
    
    await page.waitForURL('**/login');
    expect(page.url()).toContain('/login');
  });

  test('should handle loading states', async ({ registerPage, page }) => {
    // Simulate slow network
    await page.route('**/api/auth/register/', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });
    
    const userData = {
      firstName: 'Test',
      lastName: 'User',
      email: TestUtils.generateRandomEmail(),
      password: 'TestPassword123!'
    };
    
    await registerPage.register(userData);
    
    // Check loading state
    expect(await registerPage.isLoading()).toBe(true);
    
    // Wait for loading to finish
    await registerPage.waitForLoadingToFinish();
    expect(await registerPage.isLoading()).toBe(false);
  });

  test('should prevent XSS attacks', async ({ registerPage }) => {
    await registerPage.testXSSPrevention();
  });

  test('should handle network errors gracefully', async ({ registerPage, page }) => {
    // Simulate network failure
    await page.route('**/api/auth/register/', route => route.abort());
    
    const userData = {
      firstName: 'Test',
      lastName: 'User',
      email: TestUtils.generateRandomEmail(),
      password: 'TestPassword123!'
    };
    
    await registerPage.register(userData);
    await registerPage.waitForRegistrationError();
    
    const errorMessage = await registerPage.getErrorMessage();
    expect(errorMessage).toContain('network');
  });

  test('should handle server errors', async ({ registerPage, page }) => {
    // Simulate server error
    await page.route('**/api/auth/register/', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      });
    });
    
    const userData = {
      firstName: 'Test',
      lastName: 'User',
      email: TestUtils.generateRandomEmail(),
      password: 'TestPassword123!'
    };
    
    await registerPage.register(userData);
    await registerPage.waitForRegistrationError();
    
    const errorMessage = await registerPage.getErrorMessage();
    expect(errorMessage).toContain('server error');
  });

  test('should maintain form state during errors', async ({ registerPage }) => {
    const userData = {
      firstName: 'Test',
      lastName: 'User',
      email: 'invalid-email',
      password: 'TestPassword123!'
    };
    
    await registerPage.register(userData);
    await registerPage.waitForRegistrationError();
    
    // Verify form values are preserved
    const formValues = await registerPage.getFormValues();
    expect(formValues.firstName).toBe(userData.firstName);
    expect(formValues.lastName).toBe(userData.lastName);
    expect(formValues.email).toBe(userData.email);
  });

  test('should be accessible', async ({ registerPage }) => {
    await registerPage.checkAccessibility();
  });

  test('should work with keyboard navigation', async ({ registerPage, page }) => {
    const userData = {
      firstName: 'Test',
      lastName: 'User',
      email: TestUtils.generateRandomEmail(),
      password: 'TestPassword123!'
    };
    
    // Navigate using Tab key
    await page.keyboard.press('Tab'); // Focus first name
    await page.keyboard.type(userData.firstName);
    
    await page.keyboard.press('Tab'); // Focus last name
    await page.keyboard.type(userData.lastName);
    
    await page.keyboard.press('Tab'); // Focus email
    await page.keyboard.type(userData.email);
    
    await page.keyboard.press('Tab'); // Focus password
    await page.keyboard.type(userData.password);
    
    await page.keyboard.press('Tab'); // Focus confirm password
    await page.keyboard.type(userData.password);
    
    await page.keyboard.press('Tab'); // Focus register button
    await page.keyboard.press('Enter'); // Submit form
    
    await registerPage.waitForRegistrationSuccess();
    expect(page.url()).toContain('/dashboard');
  });

  test('should handle special characters in names', async ({ registerPage, page }) => {
    const userData = {
      firstName: "José-María",
      lastName: "O'Connor-Smith",
      email: TestUtils.generateRandomEmail(),
      password: 'TestPassword123!'
    };
    
    await registerPage.register(userData);
    await registerPage.waitForRegistrationSuccess();
    
    expect(page.url()).toContain('/dashboard');
  });

  test('should handle unicode characters', async ({ registerPage, page }) => {
    const userData = {
      firstName: '测试',
      lastName: 'пользователь',
      email: TestUtils.generateRandomEmail(),
      password: 'TestPassword123!'
    };
    
    await registerPage.register(userData);
    await registerPage.waitForRegistrationSuccess();
    
    expect(page.url()).toContain('/dashboard');
  });

  test('should clear form when needed', async ({ registerPage }) => {
    await registerPage.fillFirstName('Test');
    await registerPage.fillLastName('User');
    await registerPage.fillEmail('<EMAIL>');
    await registerPage.fillPassword('password123');
    
    await registerPage.clearForm();
    
    const formValues = await registerPage.getFormValues();
    expect(formValues.firstName).toBe('');
    expect(formValues.lastName).toBe('');
    expect(formValues.email).toBe('');
    expect(formValues.password).toBe('');
  });

  test('should validate minimum password length', async ({ registerPage }) => {
    const userData = {
      firstName: 'Test',
      lastName: 'User',
      email: TestUtils.generateRandomEmail(),
      password: '123' // Too short
    };
    
    await registerPage.register(userData);
    await registerPage.expectFieldValidationError('password');
  });

  test('should validate maximum field lengths', async ({ registerPage }) => {
    const longString = 'a'.repeat(300);
    
    const userData = {
      firstName: longString,
      lastName: longString,
      email: `${longString}@example.com`,
      password: 'TestPassword123!'
    };
    
    await registerPage.register(userData);
    
    // Should either truncate or show validation error
    // Adjust based on your implementation
  });

  test('should handle registration with optional username', async ({ registerPage, page }) => {
    const userData = {
      firstName: 'Test',
      lastName: 'User',
      email: TestUtils.generateRandomEmail(),
      password: 'TestPassword123!',
      username: TestUtils.generateRandomUsername()
    };
    
    await registerPage.register(userData);
    await registerPage.waitForRegistrationSuccess();
    
    expect(page.url()).toContain('/dashboard');
  });
});

test.describe('Registration Security', () => {
  test('should sanitize input data', async ({ registerPage, page }) => {
    const maliciousInput = '<script>alert("xss")</script>';
    
    const userData = {
      firstName: maliciousInput,
      lastName: maliciousInput,
      email: TestUtils.generateRandomEmail(),
      password: 'TestPassword123!'
    };
    
    // Verify no script execution
    const alertDialogs: string[] = [];
    page.on('dialog', dialog => {
      alertDialogs.push(dialog.message());
      dialog.dismiss();
    });

    await registerPage.register(userData);
    await page.waitForTimeout(1000);

    expect(alertDialogs).toHaveLength(0);
  });

  test('should prevent SQL injection', async ({ registerPage }) => {
    const sqlInjection = "'; DROP TABLE users; --";
    
    const userData = {
      firstName: sqlInjection,
      lastName: sqlInjection,
      email: TestUtils.generateRandomEmail(),
      password: 'TestPassword123!'
    };
    
    await registerPage.register(userData);
    
    // Should either succeed with sanitized data or show validation error
    // The important thing is that no SQL injection occurs
  });

  test('should enforce password complexity', async ({ registerPage }) => {
    const weakPasswords = [
      'password',
      '12345678',
      'PASSWORD',
      'Password',
      'password123'
    ];
    
    for (const password of weakPasswords) {
      await registerPage.clearForm();
      
      const userData = {
        firstName: 'Test',
        lastName: 'User',
        email: TestUtils.generateRandomEmail(),
        password: password
      };
      
      await registerPage.register(userData);
      await registerPage.expectFieldValidationError('password');
    }
  });
});
