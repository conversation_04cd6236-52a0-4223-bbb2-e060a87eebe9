import { test, expect } from '../../fixtures/test-fixtures';
import { TestUtils } from '../../fixtures/test-fixtures';

test.describe('Server Error Handling', () => {
  test('should handle 500 internal server errors gracefully', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate 500 server error
    await user1.page.route('**/api/messaging/**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ 
          error: 'Internal Server Error',
          message: 'Something went wrong on our end'
        })
      });
    });
    
    // Try to send a message
    await user1.dashboard.sendMessage('Server error test');
    
    // Should handle server error gracefully
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'server error'
    );
  });

  test('should handle 503 service unavailable errors', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate service unavailable
    await user1.page.route('**/api/**', route => {
      route.fulfill({
        status: 503,
        contentType: 'application/json',
        body: JSON.stringify({ 
          error: 'Service Unavailable',
          message: 'Service is temporarily unavailable'
        })
      });
    });
    
    // Try to perform an action
    await user1.dashboard.sendMessage('Service unavailable test');
    
    // Should show appropriate error message
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'service unavailable'
    );
  });

  test('should handle 502 bad gateway errors', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate bad gateway
    await user1.page.route('**/api/**', route => {
      route.fulfill({
        status: 502,
        contentType: 'text/html',
        body: '<html><body><h1>502 Bad Gateway</h1></body></html>'
      });
    });
    
    // Try to perform an action
    await user1.dashboard.sendMessage('Bad gateway test');
    
    // Should handle bad gateway error
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'server'
    );
  });

  test('should handle database connection errors', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate database connection error
    await user1.page.route('**/api/**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ 
          error: 'Database Connection Error',
          message: 'Unable to connect to database'
        })
      });
    });
    
    // Try to perform database-dependent action
    await user1.dashboard.sendMessage('Database error test');
    
    // Should handle database error gracefully
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'database'
    );
  });

  test('should handle API rate limiting (429 errors)', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate rate limiting
    await user1.page.route('**/api/messaging/**', route => {
      route.fulfill({
        status: 429,
        contentType: 'application/json',
        headers: {
          'Retry-After': '60'
        },
        body: JSON.stringify({ 
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.'
        })
      });
    });
    
    // Try to send multiple messages rapidly
    for (let i = 0; i < 3; i++) {
      await user1.dashboard.sendMessage(`Rate limit test ${i + 1}`);
    }
    
    // Should show rate limiting error
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'rate limit'
    );
  });

  test('should handle malformed JSON responses', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate malformed JSON response
    await user1.page.route('**/api/**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: '{"invalid": json response without closing brace'
      });
    });
    
    // Try to perform an action
    await user1.dashboard.sendMessage('Malformed JSON test');
    
    // Should handle malformed response gracefully
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'error'
    );
  });

  test('should handle server timeout errors', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate server timeout (very slow response)
    await user1.page.route('**/api/**', async route => {
      // Delay response for longer than client timeout
      await new Promise(resolve => setTimeout(resolve, 35000));
      route.continue();
    });
    
    // Try to send a message
    await user1.dashboard.sendMessage('Timeout test');
    
    // Should handle timeout gracefully
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'timeout'
    );
  });

  test('should handle authentication errors (401)', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate authentication error
    await user1.page.route('**/api/**', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ 
          error: 'Unauthorized',
          message: 'Authentication required'
        })
      });
    });
    
    // Try to perform an action
    await user1.dashboard.sendMessage('Auth error test');
    
    // Should handle auth error and potentially redirect to login
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'unauthorized'
    );
  });

  test('should handle authorization errors (403)', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate authorization error
    await user1.page.route('**/api/**', route => {
      route.fulfill({
        status: 403,
        contentType: 'application/json',
        body: JSON.stringify({ 
          error: 'Forbidden',
          message: 'Insufficient permissions'
        })
      });
    });
    
    // Try to perform an action
    await user1.dashboard.sendMessage('Permission error test');
    
    // Should handle permission error gracefully
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'permission'
    );
  });

  test('should handle resource not found errors (404)', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate resource not found
    await user1.page.route('**/api/messaging/conversations/**', route => {
      route.fulfill({
        status: 404,
        contentType: 'application/json',
        body: JSON.stringify({ 
          error: 'Not Found',
          message: 'Conversation not found'
        })
      });
    });
    
    // Try to access a conversation
    await user1.dashboard.sendMessage('Not found test');
    
    // Should handle not found error gracefully
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'not found'
    );
  });

  test('should handle server maintenance mode', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate maintenance mode
    await user1.page.route('**/api/**', route => {
      route.fulfill({
        status: 503,
        contentType: 'application/json',
        body: JSON.stringify({ 
          error: 'Service Unavailable',
          message: 'System is under maintenance. Please try again later.',
          maintenance: true
        })
      });
    });
    
    // Try to perform an action
    await user1.dashboard.sendMessage('Maintenance test');
    
    // Should show maintenance message
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'maintenance'
    );
  });

  test('should handle partial server failures', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate partial failure (some endpoints work, others don't)
    await user1.page.route('**/api/messaging/**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Messaging service unavailable' })
      });
    });
    
    // Other endpoints continue to work normally
    await user1.page.route('**/api/auth/**', route => {
      route.continue();
    });
    
    // Try messaging (should fail)
    await user1.dashboard.sendMessage('Partial failure test');
    
    // Should handle partial failure gracefully
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'service'
    );
  });

  test('should retry failed requests with exponential backoff', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    let requestCount = 0;
    const maxRetries = 3;
    
    // Simulate server errors for first few requests
    await user1.page.route('**/api/messaging/**', route => {
      requestCount++;
      if (requestCount <= maxRetries) {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Temporary server error' })
        });
      } else {
        route.continue();
      }
    });
    
    // Try to send a message
    const startTime = Date.now();
    await user1.dashboard.sendMessage('Retry test');
    
    // Should retry with increasing delays
    await user1.dashboard.waitForLoadingToFinish();
    const endTime = Date.now();
    
    // Verify retries occurred with appropriate timing
    expect(requestCount).toBeGreaterThan(1);
    
    // Should take longer due to exponential backoff
    const duration = endTime - startTime;
    expect(duration).toBeGreaterThan(1000); // At least 1 second for retries
  });

  test('should handle server errors during file operations', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate server error during file upload/download
    await user1.page.route('**/api/files/**', route => {
      route.fulfill({
        status: 507,
        contentType: 'application/json',
        body: JSON.stringify({ 
          error: 'Insufficient Storage',
          message: 'Server storage is full'
        })
      });
    });
    
    // This test assumes file upload functionality exists
    // For now, we'll simulate with a large message
    const largeMessage = 'File operation test: ' + 'x'.repeat(1000);
    await user1.dashboard.sendMessage(largeMessage);
    
    // Should handle storage error gracefully
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'storage'
    );
  });

  test('should handle cascading server failures', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate cascading failures (multiple services failing)
    const failingServices = [
      '**/api/messaging/**',
      '**/api/users/**',
      '**/api/conversations/**'
    ];
    
    for (const service of failingServices) {
      await user1.page.route(service, route => {
        route.fulfill({
          status: 503,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Service cascade failure' })
        });
      });
    }
    
    // Try to perform various actions
    await user1.dashboard.sendMessage('Cascade failure test');
    
    // Should handle cascading failures gracefully
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'service'
    );
  });

  test('should provide helpful error messages for different server errors', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    const errorScenarios = [
      {
        status: 500,
        error: 'Internal Server Error',
        expectedMessage: /server error|something went wrong/i
      },
      {
        status: 502,
        error: 'Bad Gateway',
        expectedMessage: /gateway|proxy error/i
      },
      {
        status: 503,
        error: 'Service Unavailable',
        expectedMessage: /service unavailable|temporarily unavailable/i
      },
      {
        status: 504,
        error: 'Gateway Timeout',
        expectedMessage: /timeout|taking too long/i
      }
    ];
    
    for (const scenario of errorScenarios) {
      // Setup error response
      await user1.page.route('**/api/**', route => {
        route.fulfill({
          status: scenario.status,
          contentType: 'application/json',
          body: JSON.stringify({ error: scenario.error })
        });
      });
      
      // Try to perform an action
      await user1.dashboard.sendMessage(`Test for ${scenario.status}`);
      
      // Verify appropriate error message
      await user1.dashboard.waitForErrorMessage();
      const errorMessage = await user1.dashboard.getErrorMessage();
      expect(errorMessage).toMatch(scenario.expectedMessage);
      
      // Clean up for next test
      await user1.page.unroute('**/api/**');
      await user1.dashboard.dismissError();
      await user1.page.waitForTimeout(500);
    }
  });

  test('should handle server errors during real-time operations', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Simulate server error for socket operations
    await user1.page.route('**/socket.io/**', route => {
      route.fulfill({
        status: 500,
        body: 'Socket server error'
      });
    });
    
    // Try to send a real-time message
    await user1.dashboard.sendMessage('Real-time server error test');
    
    // Should handle socket server errors gracefully
    await user1.dashboard.waitForDisconnection();
    expect(await user1.dashboard.isConnected()).toBe(false);
    
    // Should show appropriate error for real-time features
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'connection'
    );
  });
});
