import { test, expect } from '../../fixtures/test-fixtures';
import { TestUtils } from '../../fixtures/test-fixtures';

test.describe('Input Validation and Data Errors', () => {
  test('should handle empty message submission', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Try to send empty message
    await user1.dashboard.sendMessage('');
    
    // Should prevent sending empty message
    expect(await user1.dashboard.isSendButtonDisabled()).toBe(true);
    
    // Or show validation error
    const messageCount = await user1.dashboard.getMessageCount();
    await user1.page.waitForTimeout(1000);
    const newMessageCount = await user1.dashboard.getMessageCount();
    
    // Message count should not increase
    expect(newMessageCount).toBe(messageCount);
  });

  test('should handle extremely long messages', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Create extremely long message (beyond reasonable limit)
    const veryLongMessage = 'x'.repeat(10000);
    
    await user1.dashboard.sendMessage(veryLongMessage);
    
    // Should either truncate, show validation error, or handle gracefully
    await user1.page.waitForTimeout(2000);
    
    // Check if validation error is shown
    try {
      await user1.dashboard.waitForErrorMessage();
      const errorMessage = await user1.dashboard.getErrorMessage();
      expect(errorMessage).toMatch(/too long|character limit|maximum length/i);
    } catch {
      // If no error, message should be truncated or handled
      const lastMessage = await user1.dashboard.getLastMessage();
      expect(lastMessage.length).toBeLessThan(veryLongMessage.length);
    }
  });

  test('should handle special characters and emojis', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    const specialMessages = [
      '🎉🚀😀 Emojis test',
      '测试中文字符',
      'العربية اختبار',
      'Тест на русском',
      '!@#$%^&*()_+-=[]{}|;:,.<>?',
      'Line 1\nLine 2\nLine 3',
      'Tab\tSeparated\tText'
    ];
    
    for (const message of specialMessages) {
      await user1.dashboard.sendMessage(message);
      await user2.dashboard.waitForNewMessage();
      
      const receivedMessage = await user2.dashboard.getLastMessage();
      expect(receivedMessage).toContain(message);
    }
  });

  test('should handle malicious input attempts', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    const maliciousInputs = [
      '<script>alert("XSS")</script>',
      '<img src="x" onerror="alert(1)">',
      'javascript:alert("XSS")',
      '"><script>alert("XSS")</script>',
      '\'; DROP TABLE messages; --',
      '{{7*7}}', // Template injection
      '${7*7}', // Expression injection
      '../../../etc/passwd' // Path traversal
    ];
    
    for (const maliciousInput of maliciousInputs) {
      await user1.dashboard.sendMessage(maliciousInput);
      await user2.dashboard.waitForNewMessage();
      
      // Verify input is sanitized and doesn't execute
      const receivedMessage = await user2.dashboard.getLastMessage();
      
      // Should contain the text but not execute as code
      expect(receivedMessage).toContain(maliciousInput);
      
      // Verify no script execution occurred
      const alertDialogs: string[] = [];
      user1.page.on('dialog', dialog => {
        alertDialogs.push(dialog.message());
        dialog.dismiss();
      });
      
      await user1.page.waitForTimeout(1000);
      expect(alertDialogs).toHaveLength(0);
    }
  });

  test('should handle invalid conversation IDs', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Try to access conversation with invalid ID
    await user1.page.goto('/dashboard/conversations/invalid-id-123');
    
    // Should handle invalid conversation ID gracefully
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'not found'
    );
  });

  test('should handle invalid user searches', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    await user1.dashboard.openUserSearch();
    
    const invalidSearches = [
      '', // Empty search
      'nonexistentuser123456',
      '<script>alert("xss")</script>',
      'user@invalid-email-format',
      'a'.repeat(1000) // Very long search
    ];
    
    for (const search of invalidSearches) {
      await user1.dashboard.searchUser(search);
      await user1.page.waitForTimeout(1000);
      
      // Should handle invalid searches gracefully
      // Either show "no results" or validation error
      const searchResults = user1.page.locator('[data-testid="user-search-results"]');
      const resultsText = await searchResults.textContent();
      
      if (search === '') {
        // Empty search might show validation message
        expect(resultsText).toMatch(/enter|search|required/i);
      } else {
        // Invalid searches should show no results or error
        expect(resultsText).toMatch(/no results|not found|invalid/i);
      }
    }
    
    await user1.dashboard.closeUserSearch();
  });

  test('should handle form validation errors', async ({ registerPage }) => {
    await registerPage.goto();
    
    // Test various validation scenarios
    const invalidData = [
      {
        firstName: '',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'Password123!',
        expectedError: 'firstName'
      },
      {
        firstName: 'Test',
        lastName: '',
        email: '<EMAIL>',
        password: 'Password123!',
        expectedError: 'lastName'
      },
      {
        firstName: 'Test',
        lastName: 'User',
        email: 'invalid-email',
        password: 'Password123!',
        expectedError: 'email'
      },
      {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: '123',
        expectedError: 'password'
      }
    ];
    
    for (const data of invalidData) {
      await registerPage.clearForm();
      await registerPage.register(data);
      
      // Should show validation error for the problematic field
      await registerPage.expectFieldValidationError(data.expectedError as any);
    }
  });

  test('should handle file upload validation errors', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // This test assumes file upload functionality exists
    // For now, we'll simulate with message content validation
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Simulate various file-related validation errors
    const invalidFileScenarios = [
      'File too large: ' + 'x'.repeat(5000),
      'Invalid file type: executable.exe',
      'Malicious file: virus.bat'
    ];
    
    for (const scenario of invalidFileScenarios) {
      await user1.dashboard.sendMessage(scenario);
      
      // Should handle file validation appropriately
      await user1.page.waitForTimeout(1000);
      
      // Either show validation error or sanitize content
      try {
        await user1.dashboard.waitForErrorMessage();
        const errorMessage = await user1.dashboard.getErrorMessage();
        expect(errorMessage).toMatch(/file|upload|invalid/i);
        await user1.dashboard.dismissError();
      } catch {
        // If no error, content should be sanitized
        const lastMessage = await user1.dashboard.getLastMessage();
        expect(lastMessage).toBeDefined();
      }
    }
  });

  test('should handle concurrent validation requests', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Send multiple messages rapidly to test validation under load
    const rapidMessages = Array.from({ length: 10 }, (_, i) => `Rapid message ${i + 1}`);
    
    // Send all messages quickly
    const sendPromises = rapidMessages.map(message => 
      user1.dashboard.sendMessage(message)
    );
    
    await Promise.all(sendPromises);
    
    // Should handle concurrent validation without errors
    await user1.page.waitForTimeout(2000);
    
    // Verify no validation errors occurred
    const errorVisible = await user1.page.locator('[data-testid="error-message"]').isVisible();
    expect(errorVisible).toBe(false);
  });

  test('should handle data type validation errors', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate sending invalid data types through API manipulation
    await user1.page.route('**/api/messaging/**', route => {
      const request = route.request();
      if (request.method() === 'POST') {
        // Modify request to send invalid data types
        route.continue({
          postData: JSON.stringify({
            content: 12345, // Number instead of string
            type: true, // Boolean instead of string
            timestamp: 'invalid-date'
          })
        });
      } else {
        route.continue();
      }
    });
    
    await user1.dashboard.sendMessage('Data type validation test');
    
    // Should handle data type validation errors
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'validation'
    );
  });

  test('should handle missing required fields', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate sending incomplete data
    await user1.page.route('**/api/messaging/**', route => {
      const request = route.request();
      if (request.method() === 'POST') {
        // Send incomplete data (missing required fields)
        route.continue({
          postData: JSON.stringify({
            // Missing content field
            type: 'TEXT'
          })
        });
      } else {
        route.continue();
      }
    });
    
    await user1.dashboard.sendMessage('Missing fields test');
    
    // Should handle missing required fields
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'required'
    );
  });

  test('should handle character encoding issues', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Test various character encodings
    const encodingTests = [
      'UTF-8: 🌟✨💫⭐',
      'Latin-1: àáâãäåæçèéêë',
      'Cyrillic: абвгдеёжзийклмн',
      'Arabic: العربية',
      'Chinese: 中文测试',
      'Japanese: 日本語テスト',
      'Korean: 한국어 테스트'
    ];
    
    for (const message of encodingTests) {
      await user1.dashboard.sendMessage(message);
      await user2.dashboard.waitForNewMessage();
      
      const receivedMessage = await user2.dashboard.getLastMessage();
      expect(receivedMessage).toContain(message);
    }
  });

  test('should handle boundary value testing', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Test boundary values for message length
    const boundaryTests = [
      'a', // Minimum length (1 character)
      'ab', // Just above minimum
      'x'.repeat(999), // Just below maximum (assuming 1000 char limit)
      'x'.repeat(1000), // At maximum
      'x'.repeat(1001) // Just above maximum
    ];
    
    for (const message of boundaryTests) {
      await user1.dashboard.sendMessage(message);
      
      if (message.length <= 1000) {
        // Should succeed for valid lengths
        await user2.dashboard.waitForNewMessage();
        const receivedMessage = await user2.dashboard.getLastMessage();
        expect(receivedMessage).toContain(message);
      } else {
        // Should handle over-limit gracefully
        await user1.page.waitForTimeout(1000);
        
        try {
          await user1.dashboard.waitForErrorMessage();
          const errorMessage = await user1.dashboard.getErrorMessage();
          expect(errorMessage).toMatch(/too long|limit|maximum/i);
          await user1.dashboard.dismissError();
        } catch {
          // If no error, message should be truncated
          const lastMessage = await user1.dashboard.getLastMessage();
          expect(lastMessage.length).toBeLessThanOrEqual(1000);
        }
      }
    }
  });

  test('should handle null and undefined values', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate sending null/undefined values
    await user1.page.route('**/api/messaging/**', route => {
      const request = route.request();
      if (request.method() === 'POST') {
        route.continue({
          postData: JSON.stringify({
            content: null,
            type: undefined,
            metadata: null
          })
        });
      } else {
        route.continue();
      }
    });
    
    await user1.dashboard.sendMessage('Null/undefined test');
    
    // Should handle null/undefined values gracefully
    await TestUtils.verifyErrorHandling(
      user1.page,
      user1.dashboard,
      'validation'
    );
  });
});
