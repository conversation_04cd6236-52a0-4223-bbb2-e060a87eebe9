#!/bin/bash

# E2E Test Execution Script
# This script provides various options for running E2E tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
BROWSER="chromium"
HEADED=false
DEBUG=false
UI=false
PARALLEL=true
WORKERS=""
RETRIES=""
TIMEOUT=""
GREP=""
PROJECT=""
REPORTER="html"
OUTPUT_DIR="e2e/reports"
USE_MOCK_SERVER=false

# Function to print usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -b, --browser BROWSER     Browser to use (chromium, firefox, webkit, all)"
    echo "  -h, --headed              Run tests in headed mode"
    echo "  -d, --debug               Run tests in debug mode"
    echo "  -u, --ui                  Run tests in UI mode"
    echo "  -s, --serial              Run tests serially (not in parallel)"
    echo "  -w, --workers NUM         Number of worker processes"
    echo "  -r, --retries NUM         Number of retries for failed tests"
    echo "  -t, --timeout MS          Test timeout in milliseconds"
    echo "  -g, --grep PATTERN        Only run tests matching pattern"
    echo "  -p, --project PROJECT     Run specific project"
    echo "  --reporter REPORTER       Test reporter (html, json, junit, list)"
    echo "  --output-dir DIR          Output directory for reports"
    echo "  --mock-server             Use mock server instead of real services"
    echo "  --help                    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                        # Run all tests with default settings"
    echo "  $0 -b firefox -h          # Run tests in Firefox with headed mode"
    echo "  $0 -d -g \"login\"          # Debug tests matching 'login'"
    echo "  $0 -b all --serial        # Run tests in all browsers serially"
    echo "  $0 -u                     # Run tests in interactive UI mode"
}

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if services are running
check_services() {
    print_status $BLUE "🔍 Checking if services are running..."

    local services_running=true
    local critical_services_running=true

    # Check frontend (critical for E2E tests)
    if ! curl -s http://localhost:5173 > /dev/null 2>&1; then
        print_status $RED "❌ Frontend service not running on port 5173 (CRITICAL)"
        services_running=false
        critical_services_running=false
    else
        print_status $GREEN "✅ Frontend service is running"
    fi

    # Check backend (important but not always critical)
    if ! curl -s http://localhost:8000 > /dev/null 2>&1; then
        print_status $YELLOW "⚠️  Backend service not running on port 8000"
        services_running=false
    else
        print_status $GREEN "✅ Backend service is running"
    fi

    # Check socket server (important for real-time features)
    if ! curl -s http://localhost:7000 > /dev/null 2>&1; then
        print_status $YELLOW "⚠️  Socket server not running on port 7000"
        services_running=false
    else
        print_status $GREEN "✅ Socket server is running"
    fi

    if [ "$critical_services_running" = false ]; then
        print_status $RED "❌ Critical services are missing!"
        print_status $BLUE "ℹ️  To start services automatically:"
        echo "   $0 --start-services [other options]"
        echo ""
        print_status $BLUE "ℹ️  To start services manually:"
        echo "   Frontend: cd frontend && npm run dev"
        echo "   Backend:  cd backend && python manage.py runserver 8000"
        echo "   Socket:   cd socket-server && npm run dev"
        echo ""
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status $BLUE "💡 Tip: Use '$0 --start-services' to automatically start services"
            exit 1
        fi
    elif [ "$services_running" = false ]; then
        print_status $YELLOW "⚠️  Some optional services are not running."
        print_status $BLUE "ℹ️  This may affect certain test scenarios."
        echo ""
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Function to start services automatically
start_services() {
    print_status $BLUE "🚀 Starting services automatically..."

    # Check if directories exist
    if [ ! -d "frontend" ]; then
        print_status $RED "❌ Frontend directory not found"
        exit 1
    fi

    if [ ! -d "backend" ]; then
        print_status $RED "❌ Backend directory not found"
        exit 1
    fi

    if [ ! -d "socket-server" ]; then
        print_status $RED "❌ Socket server directory not found"
        exit 1
    fi

    # Start frontend (most critical for E2E tests)
    print_status $BLUE "Starting frontend server..."
    cd frontend
    if [ ! -f "package.json" ]; then
        print_status $RED "❌ Frontend package.json not found"
        cd ..
        exit 1
    fi

    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status $BLUE "Installing frontend dependencies..."
        npm install
    fi

    # Start frontend dev server
    npm run dev > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../frontend.pid
    cd ..

    # Start backend (if Python/Django is available)
    print_status $BLUE "Starting backend server..."
    cd backend
    if command -v python3 >/dev/null 2>&1; then
        # Check if virtual environment exists
        if [ -d "../venv" ]; then
            source ../venv/bin/activate
        fi

        # Try to start Django server
        python3 manage.py runserver 8000 > ../backend.log 2>&1 &
        BACKEND_PID=$!
        echo $BACKEND_PID > ../backend.pid
    else
        print_status $YELLOW "⚠️  Python not found, skipping backend"
    fi
    cd ..

    # Start socket server
    print_status $BLUE "Starting socket server..."
    cd socket-server
    if [ -f "package.json" ]; then
        # Install dependencies if needed
        if [ ! -d "node_modules" ]; then
            print_status $BLUE "Installing socket server dependencies..."
            npm install
        fi

        # Start socket server
        npm run dev > ../socket-server.log 2>&1 &
        SOCKET_PID=$!
        echo $SOCKET_PID > ../socket-server.pid
    else
        print_status $YELLOW "⚠️  Socket server package.json not found, skipping"
    fi
    cd ..

    # Wait for services to be ready
    print_status $BLUE "⏳ Waiting for services to be ready..."

    # Wait for frontend (most critical)
    print_status $BLUE "Waiting for frontend (port 5173)..."
    timeout 120 bash -c 'until curl -f http://localhost:5173 2>/dev/null; do sleep 2; done' || {
        print_status $RED "❌ Frontend failed to start within 120 seconds"
        print_status $BLUE "Frontend log:"
        tail -20 frontend.log 2>/dev/null || echo "No frontend log available"
        stop_services
        exit 1
    }
    print_status $GREEN "✅ Frontend is ready"

    # Wait for backend (optional)
    if [ -f "backend.pid" ]; then
        print_status $BLUE "Waiting for backend (port 8000)..."
        timeout 60 bash -c 'until curl -f http://localhost:8000 2>/dev/null; do sleep 2; done' && {
            print_status $GREEN "✅ Backend is ready"
        } || {
            print_status $YELLOW "⚠️  Backend not responding, continuing anyway"
        }
    fi

    # Wait for socket server (optional)
    if [ -f "socket-server.pid" ]; then
        print_status $BLUE "Waiting for socket server (port 7000)..."
        timeout 60 bash -c 'until curl -f http://localhost:7000 2>/dev/null; do sleep 2; done' && {
            print_status $GREEN "✅ Socket server is ready"
        } || {
            print_status $YELLOW "⚠️  Socket server not responding, continuing anyway"
        }
    fi

    print_status $GREEN "✅ Services startup completed"
}

# Function to start mock server
start_mock_server() {
    print_status $BLUE "🚀 Starting mock server..."

    if [ ! -f "e2e/utils/mock-server.js" ]; then
        print_status $RED "❌ Mock server script not found"
        exit 1
    fi

    # Start mock server
    node e2e/utils/mock-server.js > mock-server.log 2>&1 &
    MOCK_PID=$!
    echo $MOCK_PID > mock-server.pid

    # Wait for mock server to be ready
    print_status $BLUE "⏳ Waiting for mock server to be ready..."
    timeout 30 bash -c 'until curl -f http://localhost:5173/api/health 2>/dev/null; do sleep 1; done' || {
        print_status $RED "❌ Mock server failed to start"
        stop_services
        exit 1
    }

    print_status $GREEN "✅ Mock server is ready on http://localhost:5173"
}

# Function to stop services
stop_services() {
    print_status $BLUE "🛑 Stopping services..."

    if [ -f backend.pid ]; then
        kill $(cat backend.pid) 2>/dev/null || true
        rm backend.pid
    fi

    if [ -f socket-server.pid ]; then
        kill $(cat socket-server.pid) 2>/dev/null || true
        rm socket-server.pid
    fi

    if [ -f frontend.pid ]; then
        kill $(cat frontend.pid) 2>/dev/null || true
        rm frontend.pid
    fi

    if [ -f mock-server.pid ]; then
        kill $(cat mock-server.pid) 2>/dev/null || true
        rm mock-server.pid
    fi

    print_status $GREEN "✅ Services stopped"
}

# Function to build Playwright command
build_command() {
    local cmd="npx playwright test"
    
    # Add browser selection
    if [ "$BROWSER" != "all" ]; then
        cmd="$cmd --project=$BROWSER"
    fi
    
    # Add mode flags
    if [ "$HEADED" = true ]; then
        cmd="$cmd --headed"
    fi
    
    if [ "$DEBUG" = true ]; then
        cmd="$cmd --debug"
    fi
    
    if [ "$UI" = true ]; then
        cmd="$cmd --ui"
    fi
    
    # Add parallel/serial execution
    if [ "$PARALLEL" = false ]; then
        cmd="$cmd --workers=1"
    elif [ -n "$WORKERS" ]; then
        cmd="$cmd --workers=$WORKERS"
    fi
    
    # Add retries
    if [ -n "$RETRIES" ]; then
        cmd="$cmd --retries=$RETRIES"
    fi
    
    # Add timeout
    if [ -n "$TIMEOUT" ]; then
        cmd="$cmd --timeout=$TIMEOUT"
    fi
    
    # Add grep pattern
    if [ -n "$GREP" ]; then
        cmd="$cmd --grep=\"$GREP\""
    fi
    
    # Add project
    if [ -n "$PROJECT" ]; then
        cmd="$cmd --project=$PROJECT"
    fi
    
    # Add reporter
    cmd="$cmd --reporter=$REPORTER"
    
    echo "$cmd"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--browser)
            BROWSER="$2"
            shift 2
            ;;
        -h|--headed)
            HEADED=true
            shift
            ;;
        -d|--debug)
            DEBUG=true
            shift
            ;;
        -u|--ui)
            UI=true
            shift
            ;;
        -s|--serial)
            PARALLEL=false
            shift
            ;;
        -w|--workers)
            WORKERS="$2"
            shift 2
            ;;
        -r|--retries)
            RETRIES="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -g|--grep)
            GREP="$2"
            shift 2
            ;;
        -p|--project)
            PROJECT="$2"
            shift 2
            ;;
        --reporter)
            REPORTER="$2"
            shift 2
            ;;
        --reporter=*)
            REPORTER="${1#*=}"
            shift
            ;;
        --output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --output-dir=*)
            OUTPUT_DIR="${1#*=}"
            shift
            ;;
        --start-services)
            START_SERVICES=true
            shift
            ;;
        --mock-server)
            USE_MOCK_SERVER=true
            shift
            ;;
        --help)
            usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_status $BLUE "🎭 Playwright E2E Test Runner"
    print_status $BLUE "=============================="
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ] || [ ! -d "e2e" ]; then
        print_status $RED "❌ Please run this script from the chat application root directory"
        exit 1
    fi
    
    # Create output directory
    mkdir -p "$OUTPUT_DIR"
    
    # Start services if requested
    if [ "$USE_MOCK_SERVER" = true ]; then
        start_mock_server
        trap stop_services EXIT
    elif [ "$START_SERVICES" = true ]; then
        start_services
        trap stop_services EXIT
    else
        check_services
    fi
    
    # Build and execute command
    local cmd=$(build_command)
    
    print_status $BLUE "🚀 Running command: $cmd"
    print_status $BLUE "📊 Browser: $BROWSER"
    print_status $BLUE "📁 Output: $OUTPUT_DIR"
    
    # Execute the command
    if eval "$cmd"; then
        print_status $GREEN "✅ Tests completed successfully!"
        
        # Show report location
        if [ "$REPORTER" = "html" ] && [ -f "$OUTPUT_DIR/html/index.html" ]; then
            print_status $BLUE "📊 HTML report available at: $OUTPUT_DIR/html/index.html"
            print_status $BLUE "🌐 Open with: npm run test:e2e:report"
        fi
        
    else
        print_status $RED "❌ Tests failed!"
        
        # Show failure artifacts
        if [ -d "e2e/test-results" ]; then
            print_status $BLUE "🔍 Test artifacts available in: e2e/test-results/"
        fi
        
        exit 1
    fi
}

# Run main function
main "$@"
