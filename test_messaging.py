#!/usr/bin/env python3
import requests
import json

# Test messaging endpoints
BASE_URL = "http://localhost:8000"

def login_user(email, password):
    """Login and return token"""
    login_data = {"email": email, "password": password}
    response = requests.post(f"{BASE_URL}/api/auth/login/", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            return data['data']['tokens']['access'], data['data']['user']
    return None, None

def test_conversations(token):
    """Test conversation endpoints"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("Testing conversation list...")
    response = requests.get(f"{BASE_URL}/api/messaging/conversations/", headers=headers)
    print(f"Conversations response: {response.status_code}")
    if response.status_code == 200:
        print(f"Conversations: {response.json()}")
    
    return response.status_code == 200

def create_conversation(token, participant_ids, conversation_type="DIRECT", name=None):
    """Create a new conversation"""
    headers = {"Authorization": f"Bearer {token}"}
    
    data = {
        "type": conversation_type,
        "participant_ids": participant_ids
    }
    if name:
        data["name"] = name
    
    print(f"Creating conversation with participants: {participant_ids}")
    response = requests.post(f"{BASE_URL}/api/messaging/conversations/create/", json=data, headers=headers)
    print(f"Create conversation response: {response.status_code}")
    
    if response.status_code in [200, 201]:
        conversation = response.json()
        print(f"Created conversation: {conversation}")
        return conversation
    else:
        print(f"Error: {response.text}")
        return None

def send_message(token, conversation_id, content):
    """Send a message to a conversation"""
    headers = {"Authorization": f"Bearer {token}"}
    
    data = {
        "content": content,
        "message_type": "TEXT"
    }
    
    print(f"Sending message to conversation {conversation_id}: {content}")
    response = requests.post(f"{BASE_URL}/api/messaging/conversations/{conversation_id}/send/", json=data, headers=headers)
    print(f"Send message response: {response.status_code}")
    
    if response.status_code in [200, 201]:
        message = response.json()
        print(f"Sent message: {message}")
        return message
    else:
        print(f"Error: {response.text}")
        return None

def get_messages(token, conversation_id):
    """Get messages from a conversation"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"Getting messages from conversation {conversation_id}")
    response = requests.get(f"{BASE_URL}/api/messaging/conversations/{conversation_id}/messages/", headers=headers)
    print(f"Get messages response: {response.status_code}")
    
    if response.status_code == 200:
        messages = response.json()
        print(f"Messages: {messages}")
        return messages
    else:
        print(f"Error: {response.text}")
        return None

def main():
    print("=== Testing Phase 2 Messaging System ===\n")
    
    # Login as Alice
    print("1. Logging in as Alice...")
    alice_token, alice_user = login_user("<EMAIL>", "password123")
    if not alice_token:
        print("Failed to login as Alice")
        return
    print(f"Alice logged in successfully! User ID: {alice_user['id']}\n")
    
    # Login as Bob
    print("2. Logging in as Bob...")
    bob_token, bob_user = login_user("<EMAIL>", "password123")
    if not bob_token:
        print("Failed to login as Bob")
        return
    print(f"Bob logged in successfully! User ID: {bob_user['id']}\n")
    
    # Test conversation list (should be empty initially)
    print("3. Testing conversation list for Alice...")
    test_conversations(alice_token)
    print()
    
    # Create a conversation between Alice and Bob
    print("4. Creating conversation between Alice and Bob...")
    conversation = create_conversation(alice_token, [bob_user['id']], "DIRECT")
    if not conversation:
        print("Failed to create conversation")
        return
    conversation_id = conversation['id']
    print(f"Created conversation ID: {conversation_id}\n")
    
    # Send a message from Alice to Bob
    print("5. Alice sending message to Bob...")
    message1 = send_message(alice_token, conversation_id, "Hello Bob! This is Alice.")
    print()
    
    # Send a reply from Bob to Alice
    print("6. Bob replying to Alice...")
    message2 = send_message(bob_token, conversation_id, "Hi Alice! Great to hear from you!")
    print()
    
    # Get messages from Alice's perspective
    print("7. Getting conversation messages (Alice's view)...")
    messages = get_messages(alice_token, conversation_id)
    print()
    
    # Test conversation list again (should show the conversation)
    print("8. Testing conversation list for Alice (should show conversation)...")
    test_conversations(alice_token)
    print()
    
    print("=== Messaging API Test Complete ===")
    print("\nNext steps:")
    print("1. Open http://localhost:3000 in your browser")
    print("2. <NAME_EMAIL> / password123")
    print("3. You should see the conversation with Bob")
    print("4. Try sending real-time messages!")

if __name__ == "__main__":
    main()
