#!/bin/bash

# Array to store PIDs
declare -a PIDS

# Function to cleanup processes
cleanup() {
    echo "Stopping all services..."
    for pid in "${PIDS[@]}"; do
        if ps -p $pid > /dev/null; then
            echo "Killing process $pid"
            kill $pid 2>/dev/null
            wait $pid 2>/dev/null
        fi
    done
    exit 0
}

# Function to run a command in a new terminal
run_service() {
    echo "Starting $1..."
    cd "$2" && $3 &
    local PID=$!
    PIDS+=($PID)
    echo "$1 started (PID: $PID)"
}

# Store the base directory
BASE_DIR="$(pwd)"

# Set up trap for Ctrl+C and termination
trap cleanup SIGINT SIGTERM

# Start backend server
run_service "Backend Server" "$BASE_DIR/backend" "/home/<USER>/chatapp/venv/bin/python manage.py runserver 6000"

# Start frontend
run_service "Frontend" "$BASE_DIR/frontend" "npm run dev"

# Start socket server
run_service "Socket Server" "$BASE_DIR/socket-server" "npm run dev"

# Keep script running
echo "All services started. Press Ctrl+C to stop all services."
wait
