# 🧪 E2E Testing Guide - Chat Application

This guide provides comprehensive instructions for testing the chat application end-to-end, including encryption, real-time messaging, and user interactions.

## 🚀 Quick Start

### 1. Prerequisites
- All services running (backend on port 6000, frontend on port 5000, socket server on port 3001)
- Test users created in database
- <PERSON>rowser with developer tools access

### 2. Test Users Available
```
Alice Smith:    alice / alicepass123
Bob Johnson:    bob / bobpass123  
Test User 1:    testuser1 / testpass123
Test User 2:    testuser2 / testpass123
```

### 3. Running Tests

#### Option A: Using Test Runner HTML
1. Open `e2e-test-runner.html` in your browser
2. Follow the instructions to copy commands
3. Navigate to the chat app and open console
4. Paste and run the commands

#### Option B: Manual Console Testing
1. Navigate to http://localhost:5000
2. Open browser console (F12)
3. Copy and paste the content of `e2e-console-test.js`
4. Run: `const test = new E2ETest(); test.runFullTest();`

## 🔧 Fixed Issues

### 1. Web Crypto API Key Generation
**Problem:** `Cannot create a key using the specified key usages`
**Solution:** 
- Fixed key usage parameters in `checkCryptoSupport()` method
- Added proper error handling and fallback mechanisms
- Improved browser compatibility checks

### 2. Redux Store Configuration  
**Problem:** `No data found at state.api`
**Solution:**
- Fixed incorrect usage of RTK Query selectors
- Added missing encryption API exports to services index
- Corrected import order and initialization

### 3. Encryption Session Initialization
**Problem:** `Session not found for conversation`
**Solution:**
- Fixed API endpoint URLs for session creation
- Improved error handling and logging
- Added better session state management

### 4. Test User Creation
**Solution:**
- Created Django management command `create_test_users`
- Added 4 test users with proper credentials
- Automated user creation process

## 📋 Test Coverage

### Authentication
- [x] User login with valid credentials
- [x] Token storage and validation
- [x] Redirect to dashboard after login

### Encryption
- [x] Crypto API support detection
- [x] Key pair generation (X25519/ECDH fallback)
- [x] Signing key generation (Ed25519/ECDSA fallback)
- [x] Session initialization between users
- [x] Message encryption/decryption

### Real-time Messaging
- [x] Socket connection establishment
- [x] User search functionality
- [x] Conversation creation
- [x] Message sending with encryption
- [x] Real-time message delivery
- [x] Optimistic UI updates

### User Interface
- [x] Login form interaction
- [x] User search interface
- [x] Conversation list updates
- [x] Message input and display
- [x] Error handling and notifications

## 🔍 Monitoring Test Results

### Console Logs to Watch For
```
🔐 ✅ Encryption initialized successfully
🔐 ✅ Session initialized successfully  
🔐 ✅ Message encrypted successfully
📡 [SOCKET] Connected to server
✅ [SEND_MESSAGE] Message sent successfully
```

### Error Indicators
```
🔐 ❌ Failed to initialize encryption
❌ [SEND_MESSAGE] Failed to encrypt message
📡 [SOCKET] Connection failed
❌ Redux store error
```

## 🐛 Troubleshooting

### Common Issues

1. **Encryption Initialization Fails**
   - Check browser crypto support
   - Verify user authentication
   - Check console for specific crypto errors

2. **Socket Connection Issues**
   - Verify socket server is running on port 3001
   - Check CORS configuration
   - Monitor network tab for WebSocket connections

3. **Message Sending Fails**
   - Ensure encryption session is initialized
   - Check conversation creation
   - Verify user permissions

4. **UI Elements Not Found**
   - Wait for page to fully load
   - Check element selectors in test script
   - Verify React components have rendered

### Debug Commands
```javascript
// Check encryption status
console.log('Encryption initialized:', window.encryptionContext?.isInitialized);

// Check socket connection
console.log('Socket connected:', window.socketContext?.isConnected);

// Check Redux store state
console.log('Store state:', window.store?.getState());

// Check current user
console.log('Current user:', window.authContext?.user);
```

## 📊 Expected Test Results

### Successful Test Run
```
[INFO] Starting E2E Chat Application Test
[STEP] Step 1: Testing login process
[INFO] Successfully logged in and redirected to dashboard
[STEP] Step 2: Testing user search functionality  
[INFO] Found 1 search results
[STEP] Step 3: Testing conversation creation
[INFO] Conversation created successfully
[STEP] Step 4: Testing message sending with encryption
[INFO] Message appeared in chat successfully
[STEP] Step 5: Testing message sending with encryption
[INFO] Message appeared in chat successfully
[STEP] Step 6: Testing encryption status
[INFO] Encryption status check completed
[SUCCESS] All tests completed successfully! ✅
```

## 🔄 Running Multiple Test Scenarios

### Test Scenario 1: Alice → Bob
```javascript
const test1 = new E2ETest();
await test1.testLogin('alice', 'alicepass123');
await test1.testUserSearch('bob');
// Continue with conversation and messaging...
```

### Test Scenario 2: Bob → Alice (Reverse)
```javascript
const test2 = new E2ETest();
await test2.testLogin('bob', 'bobpass123');
await test2.testUserSearch('alice');
// Test receiving messages...
```

### Test Scenario 3: Group Conversations
```javascript
// Future enhancement for group messaging tests
```

## 📈 Performance Monitoring

### Key Metrics to Monitor
- Login time: < 3 seconds
- Encryption initialization: < 5 seconds
- Message encryption: < 1 second
- Socket connection: < 2 seconds
- UI responsiveness: Immediate

### Browser Compatibility
- Chrome 90+ (Full support)
- Firefox 88+ (Full support)
- Safari 14+ (Limited crypto support)
- Edge 90+ (Full support)

## 🎯 Next Steps

1. **Automated Testing**: Convert console tests to automated Playwright/Cypress tests
2. **Performance Testing**: Add timing measurements and performance benchmarks
3. **Cross-browser Testing**: Test on multiple browsers and devices
4. **Load Testing**: Test with multiple concurrent users
5. **Security Testing**: Verify encryption implementation security

---

**Happy Testing! 🚀**

For issues or questions, check the console logs and refer to the troubleshooting section above.
