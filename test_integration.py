#!/usr/bin/env python3
"""
Integration test script to verify all services are working together.
Run this after setting up all services to ensure proper integration.
"""

import requests
import json
import time
import sys

def test_django_backend():
    """Test Django backend is running and responsive."""
    try:
        response = requests.get('http://localhost:8000/admin/', timeout=5)
        if response.status_code == 200:
            print("✅ Django backend is running")
            return True
        else:
            print(f"❌ Django backend returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Django backend is not accessible: {e}")
        return False

def test_socket_server():
    """Test Socket.io server is running."""
    try:
        response = requests.get('http://localhost:3001/socket.io/', timeout=5)
        # Socket.io returns 400 for GET requests, which is expected
        if response.status_code in [400, 404]:
            print("✅ Socket.io server is running")
            return True
        else:
            print(f"❌ Socket.io server returned unexpected status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Socket.io server is not accessible: {e}")
        return False

def test_react_frontend():
    """Test React frontend is running."""
    try:
        response = requests.get('http://localhost:3000/', timeout=5)
        if response.status_code == 200:
            print("✅ React frontend is running")
            # Check if it contains expected content
            if 'vite' in response.text.lower() or 'react' in response.text.lower():
                print("✅ Vite-powered React app is working")
            return True
        else:
            print(f"❌ React frontend returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ React frontend is not accessible: {e}")
        return False

def test_django_api():
    """Test Django API endpoints."""
    try:
        # Test registration endpoint
        response = requests.post('http://localhost:8000/api/auth/register/', 
                               json={}, timeout=5)
        # Should return 400 for empty data, which means endpoint is working
        if response.status_code == 400:
            print("✅ Django API endpoints are accessible")
            return True
        else:
            print(f"❌ Django API returned unexpected status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Django API is not accessible: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🚀 Starting integration tests...\n")
    
    tests = [
        ("Django Backend", test_django_backend),
        ("Socket.io Server", test_socket_server),
        ("React Frontend", test_react_frontend),
        ("Django API", test_django_api),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Testing {test_name}...")
        result = test_func()
        results.append(result)
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("=" * 50)
    print(f"Integration Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All services are running correctly!")
        print("\nYou can now:")
        print("- Access Django admin at: http://localhost:8000/admin/")
        print("- Access React app at: http://localhost:3000/")
        print("- Socket.io server is ready at: http://localhost:3001/")
        return 0
    else:
        print("❌ Some services are not running properly.")
        print("\nPlease check:")
        print("1. All services are started")
        print("2. Environment variables are set correctly")
        print("3. Database and Redis are running")
        print("4. No port conflicts exist")
        return 1

if __name__ == "__main__":
    sys.exit(main())
