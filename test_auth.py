#!/usr/bin/env python3
import requests
import json

# Test authentication endpoints
BASE_URL = "http://localhost:8000"

def test_login():
    print("Testing login endpoint...")
    
    # Test login with alice
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/login/", json=login_data)
    print(f"Login response status: {response.status_code}")
    print(f"Login response: {response.json()}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            token = data['data']['tokens']['access']
            print(f"Login successful! Token: {token[:50]}...")
            return token
        else:
            print("Login failed: No success field")
            return None
    else:
        print("Login failed!")
        return None

def test_profile(token):
    print("\nTesting profile endpoint...")
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    response = requests.get(f"{BASE_URL}/api/auth/profile/", headers=headers)
    print(f"Profile response status: {response.status_code}")
    print(f"Profile response: {response.json()}")
    
    return response.status_code == 200

if __name__ == "__main__":
    token = test_login()
    if token:
        test_profile(token)
    else:
        print("Cannot test profile without valid token")
