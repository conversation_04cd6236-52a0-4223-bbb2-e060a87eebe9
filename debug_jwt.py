#!/usr/bin/env python3
import requests
import jwt
import json

# Test JWT token structure
BASE_URL = "http://localhost:8000"

def decode_jwt_token():
    # Login to get a token
    login_data = {"email": "<EMAIL>", "password": "password123"}
    response = requests.post(f"{BASE_URL}/api/auth/login/", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            token = data['data']['tokens']['access']
            print(f"JWT Token: {token}")
            
            # Decode without verification to see structure
            try:
                decoded = jwt.decode(token, options={"verify_signature": False})
                print(f"JWT Payload: {json.dumps(decoded, indent=2)}")
                return token, decoded
            except Exception as e:
                print(f"Error decoding JWT: {e}")
                return None, None
    
    print("Failed to get token")
    return None, None

def test_jwt_verification():
    token, payload = decode_jwt_token()
    if not token:
        return
    
    # Test with the JWT secret from .env
    jwt_secret = "your-jwt-secret-key"
    
    try:
        # Try to verify with the secret
        verified = jwt.decode(token, jwt_secret, algorithms=["HS256"])
        print(f"✅ JWT verification successful with secret: {verified}")
    except jwt.InvalidSignatureError:
        print("❌ JWT signature verification failed - secret mismatch")
    except jwt.ExpiredSignatureError:
        print("❌ JWT token expired")
    except Exception as e:
        print(f"❌ JWT verification error: {e}")

if __name__ == "__main__":
    test_jwt_verification()
