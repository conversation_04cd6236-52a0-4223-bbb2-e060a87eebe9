# Phase 5: Media Sharing & File Handling

**Duration**: 3-4 weeks | **Priority**: Medium

## Overview
This phase implements comprehensive media sharing capabilities including image uploads, file sharing, document handling, and media processing with encryption support.

## Prerequisites
- Phase 4 completed successfully
- Group chat functionality working
- End-to-end encryption stable
- File storage infrastructure available (AWS S3, local storage, etc.)

## Media Architecture

### Supported Media Types
- **Images**: JPEG, PNG, GIF, WebP (up to 10MB)
- **Documents**: PDF, DOC, DOCX, TXT, RTF (up to 25MB)
- **Archives**: ZIP, RAR, 7Z (up to 50MB)
- **Audio**: MP3, WAV, OGG (up to 25MB)
- **Video**: MP4, WebM, MOV (up to 100MB)

### Security Features
- Client-side encryption before upload
- Virus scanning integration
- File type validation
- Size restrictions
- Secure download links with expiration

## Database Schema Updates

### Step 1: Media Models

```python
# backend/apps/media/models.py
import uuid
import os
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import FileExtensionValidator

User = get_user_model()

class MediaFile(models.Model):
    MEDIA_TYPES = [
        ('image', 'Image'),
        ('document', 'Document'),
        ('audio', 'Audio'),
        ('video', 'Video'),
        ('archive', 'Archive'),
        ('other', 'Other'),
    ]
    
    PROCESSING_STATUS = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey('messaging.Message', on_delete=models.CASCADE, related_name='media_files')
    uploader = models.ForeignKey(User, on_delete=models.CASCADE, related_name='uploaded_media')
    
    # File information
    original_filename = models.CharField(max_length=255)
    file_type = models.CharField(max_length=10, choices=MEDIA_TYPES)
    mime_type = models.CharField(max_length=100)
    file_size = models.BigIntegerField()  # Size in bytes
    
    # Storage paths (encrypted)
    encrypted_file_path = models.CharField(max_length=500)
    thumbnail_path = models.CharField(max_length=500, blank=True, null=True)
    
    # Encryption
    encryption_key = models.TextField()  # Base64 encoded, encrypted with user's key
    encryption_iv = models.TextField()   # Base64 encoded initialization vector
    
    # Processing
    processing_status = models.CharField(max_length=15, choices=PROCESSING_STATUS, default='pending')
    processing_error = models.TextField(blank=True, null=True)
    
    # Metadata
    metadata = models.JSONField(default=dict)  # Image dimensions, duration, etc.
    
    # Security
    virus_scan_status = models.CharField(max_length=20, default='pending')
    virus_scan_result = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'media_files'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.original_filename} ({self.file_type})"
    
    @property
    def file_extension(self):
        return os.path.splitext(self.original_filename)[1].lower()
    
    @property
    def is_image(self):
        return self.file_type == 'image'
    
    @property
    def is_video(self):
        return self.file_type == 'video'

class MediaDownload(models.Model):
    """Track media downloads for analytics and security"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    media_file = models.ForeignKey(MediaFile, on_delete=models.CASCADE, related_name='downloads')
    downloaded_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='media_downloads')
    download_token = models.CharField(max_length=100, unique=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)
    downloaded_at = models.DateTimeField(null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'media_downloads'

class MediaProcessingJob(models.Model):
    """Track background media processing jobs"""
    JOB_TYPES = [
        ('thumbnail', 'Thumbnail Generation'),
        ('compression', 'File Compression'),
        ('virus_scan', 'Virus Scanning'),
        ('metadata_extraction', 'Metadata Extraction'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    media_file = models.ForeignKey(MediaFile, on_delete=models.CASCADE, related_name='processing_jobs')
    job_type = models.CharField(max_length=20, choices=JOB_TYPES)
    status = models.CharField(max_length=15, choices=MediaFile.PROCESSING_STATUS, default='pending')
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True, null=True)
    result_data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'media_processing_jobs'
```

### Step 2: Update Message Model

```python
# backend/apps/messaging/models.py - Add media support to Message model

class Message(models.Model):
    # ... existing fields ...
    
    # Media message support
    has_media = models.BooleanField(default=False)
    media_count = models.IntegerField(default=0)
    
    def save(self, *args, **kwargs):
        # Update media flags
        if self.pk:
            self.media_count = self.media_files.count()
            self.has_media = self.media_count > 0
        super().save(*args, **kwargs)
```

## File Upload Implementation

### Step 3: Media Upload API

```python
# backend/apps/media/views.py
import os
import magic
from django.conf import settings
from django.core.files.storage import default_storage
from django.http import HttpResponse, Http404
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.response import Response
from cryptography.fernet import Fernet
import base64

from .models import MediaFile, MediaDownload
from .serializers import MediaFileSerializer
from .utils import generate_thumbnail, extract_metadata, scan_for_virus

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@parser_classes([MultiPartParser, FormParser])
def upload_media(request):
    """Upload media file with encryption"""
    if 'file' not in request.FILES:
        return Response({'error': 'No file provided'}, status=status.HTTP_400_BAD_REQUEST)
    
    uploaded_file = request.FILES['file']
    message_id = request.data.get('message_id')
    
    if not message_id:
        return Response({'error': 'Message ID required'}, status=status.HTTP_400_BAD_REQUEST)
    
    # Validate file
    validation_error = validate_uploaded_file(uploaded_file)
    if validation_error:
        return Response({'error': validation_error}, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        # Determine file type
        mime_type = magic.from_buffer(uploaded_file.read(1024), mime=True)
        uploaded_file.seek(0)  # Reset file pointer
        
        file_type = determine_file_type(mime_type)
        
        # Generate encryption key
        encryption_key = Fernet.generate_key()
        fernet = Fernet(encryption_key)
        
        # Encrypt file content
        file_content = uploaded_file.read()
        encrypted_content = fernet.encrypt(file_content)
        
        # Generate unique filename
        file_extension = os.path.splitext(uploaded_file.name)[1]
        encrypted_filename = f"{uuid.uuid4()}{file_extension}.enc"
        
        # Save encrypted file
        file_path = f"media/{request.user.id}/{encrypted_filename}"
        saved_path = default_storage.save(file_path, ContentFile(encrypted_content))
        
        # Create media record
        media_file = MediaFile.objects.create(
            message_id=message_id,
            uploader=request.user,
            original_filename=uploaded_file.name,
            file_type=file_type,
            mime_type=mime_type,
            file_size=uploaded_file.size,
            encrypted_file_path=saved_path,
            encryption_key=base64.b64encode(encryption_key).decode('utf-8'),
            encryption_iv=base64.b64encode(os.urandom(16)).decode('utf-8'),
            processing_status='pending'
        )
        
        # Queue background processing
        from .tasks import process_media_file
        process_media_file.delay(str(media_file.id))
        
        return Response(
            MediaFileSerializer(media_file).data,
            status=status.HTTP_201_CREATED
        )
        
    except Exception as e:
        return Response(
            {'error': f'Upload failed: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

def validate_uploaded_file(uploaded_file):
    """Validate uploaded file"""
    # Check file size limits
    size_limits = {
        'image': 10 * 1024 * 1024,    # 10MB
        'document': 25 * 1024 * 1024, # 25MB
        'audio': 25 * 1024 * 1024,    # 25MB
        'video': 100 * 1024 * 1024,   # 100MB
        'archive': 50 * 1024 * 1024,  # 50MB
    }
    
    # Determine file type from extension
    file_extension = os.path.splitext(uploaded_file.name)[1].lower()
    file_type = get_file_type_from_extension(file_extension)
    
    if file_type == 'other':
        return "File type not supported"
    
    max_size = size_limits.get(file_type, 10 * 1024 * 1024)
    if uploaded_file.size > max_size:
        return f"File too large. Maximum size for {file_type} files is {max_size // (1024*1024)}MB"
    
    # Check for dangerous file types
    dangerous_extensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com']
    if file_extension in dangerous_extensions:
        return "File type not allowed for security reasons"
    
    return None

def determine_file_type(mime_type):
    """Determine file type from MIME type"""
    if mime_type.startswith('image/'):
        return 'image'
    elif mime_type.startswith('video/'):
        return 'video'
    elif mime_type.startswith('audio/'):
        return 'audio'
    elif mime_type in ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
        return 'document'
    elif mime_type in ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed']:
        return 'archive'
    else:
        return 'other'

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def download_media(request, media_id):
    """Download media file with decryption"""
    try:
        media_file = MediaFile.objects.get(id=media_id)
        
        # Check if user has access to this media
        # (they must be a participant in the conversation)
        conversation = media_file.message.conversation
        if not conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Access denied'}, status=status.HTTP_403_FORBIDDEN)
        
        # Generate download token
        download_token = generate_download_token()
        expires_at = timezone.now() + timedelta(hours=1)
        
        MediaDownload.objects.create(
            media_file=media_file,
            downloaded_by=request.user,
            download_token=download_token,
            expires_at=expires_at,
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        return Response({
            'download_url': f'/api/media/download/{download_token}/',
            'filename': media_file.original_filename,
            'file_size': media_file.file_size,
            'expires_at': expires_at.isoformat()
        })
        
    except MediaFile.DoesNotExist:
        return Response({'error': 'Media file not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['GET'])
def secure_download(request, download_token):
    """Secure download endpoint with token validation"""
    try:
        download = MediaDownload.objects.get(
            download_token=download_token,
            is_used=False,
            expires_at__gt=timezone.now()
        )
        
        media_file = download.media_file
        
        # Read and decrypt file
        with default_storage.open(media_file.encrypted_file_path, 'rb') as encrypted_file:
            encrypted_content = encrypted_file.read()
        
        # Decrypt content
        encryption_key = base64.b64decode(media_file.encryption_key)
        fernet = Fernet(encryption_key)
        decrypted_content = fernet.decrypt(encrypted_content)
        
        # Mark download as used
        download.is_used = True
        download.downloaded_at = timezone.now()
        download.save()
        
        # Return file response
        response = HttpResponse(
            decrypted_content,
            content_type=media_file.mime_type
        )
        response['Content-Disposition'] = f'attachment; filename="{media_file.original_filename}"'
        response['Content-Length'] = len(decrypted_content)
        
        return response
        
    except MediaDownload.DoesNotExist:
        raise Http404("Download link expired or invalid")

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_thumbnail(request, media_id):
    """Get thumbnail for image/video files"""
    try:
        media_file = MediaFile.objects.get(id=media_id)
        
        # Check access permissions
        conversation = media_file.message.conversation
        if not conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Access denied'}, status=status.HTTP_403_FORBIDDEN)
        
        if not media_file.thumbnail_path:
            return Response({'error': 'Thumbnail not available'}, status=status.HTTP_404_NOT_FOUND)
        
        # Return thumbnail (thumbnails are not encrypted for performance)
        with default_storage.open(media_file.thumbnail_path, 'rb') as thumbnail_file:
            thumbnail_content = thumbnail_file.read()
        
        return HttpResponse(
            thumbnail_content,
            content_type='image/jpeg'
        )
        
    except MediaFile.DoesNotExist:
        return Response({'error': 'Media file not found'}, status=status.HTTP_404_NOT_FOUND)
```

### Step 4: Background Processing Tasks

```python
# backend/apps/media/tasks.py
from celery import shared_task
from .models import MediaFile, MediaProcessingJob
from .utils import generate_thumbnail, extract_metadata, scan_for_virus
import logging

logger = logging.getLogger(__name__)

@shared_task
def process_media_file(media_file_id):
    """Process uploaded media file"""
    try:
        media_file = MediaFile.objects.get(id=media_file_id)
        media_file.processing_status = 'processing'
        media_file.save()
        
        # Create processing jobs
        jobs = []
        
        # Always scan for viruses
        jobs.append(create_processing_job(media_file, 'virus_scan'))
        
        # Generate thumbnail for images and videos
        if media_file.file_type in ['image', 'video']:
            jobs.append(create_processing_job(media_file, 'thumbnail'))
        
        # Extract metadata
        jobs.append(create_processing_job(media_file, 'metadata_extraction'))
        
        # Process each job
        all_successful = True
        for job in jobs:
            success = process_job(job)
            if not success:
                all_successful = False
        
        # Update final status
        if all_successful:
            media_file.processing_status = 'completed'
        else:
            media_file.processing_status = 'failed'
        
        media_file.save()
        
        # Notify clients via WebSocket
        from apps.messaging.socket_handlers import notify_media_processed
        notify_media_processed(media_file)
        
    except MediaFile.DoesNotExist:
        logger.error(f"Media file {media_file_id} not found")
    except Exception as e:
        logger.error(f"Error processing media file {media_file_id}: {str(e)}")
        try:
            media_file = MediaFile.objects.get(id=media_file_id)
            media_file.processing_status = 'failed'
            media_file.processing_error = str(e)
            media_file.save()
        except:
            pass

def create_processing_job(media_file, job_type):
    """Create a processing job"""
    return MediaProcessingJob.objects.create(
        media_file=media_file,
        job_type=job_type,
        status='pending'
    )

def process_job(job):
    """Process a single job"""
    try:
        job.status = 'processing'
        job.started_at = timezone.now()
        job.save()
        
        if job.job_type == 'virus_scan':
            result = scan_for_virus(job.media_file)
        elif job.job_type == 'thumbnail':
            result = generate_thumbnail(job.media_file)
        elif job.job_type == 'metadata_extraction':
            result = extract_metadata(job.media_file)
        else:
            raise ValueError(f"Unknown job type: {job.job_type}")
        
        job.status = 'completed'
        job.completed_at = timezone.now()
        job.result_data = result
        job.save()
        
        return True
        
    except Exception as e:
        job.status = 'failed'
        job.completed_at = timezone.now()
        job.error_message = str(e)
        job.save()
        
        logger.error(f"Job {job.id} failed: {str(e)}")
        return False
```

## Frontend Implementation

### Step 5: Media Upload Component

```typescript
// frontend/src/components/Media/MediaUpload.tsx
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  Box,
  Button,
  LinearProgress,
  Typography,
  Alert,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  AttachFile as AttachIcon,
} from '@mui/icons-material';
import axios from 'axios';

interface MediaUploadProps {
  conversationId: string;
  onUploadComplete: (mediaFiles: any[]) => void;
  onUploadError: (error: string) => void;
}

interface UploadFile {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
  mediaFile?: any;
}

const MediaUpload: React.FC<MediaUploadProps> = ({
  conversationId,
  onUploadComplete,
  onUploadError,
}) => {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      file,
      id: `${file.name}-${Date.now()}`,
      progress: 0,
      status: 'pending',
    }));
    
    setUploadFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    maxSize: 100 * 1024 * 1024, // 100MB
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
      'video/*': ['.mp4', '.webm', '.mov'],
      'audio/*': ['.mp3', '.wav', '.ogg'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/zip': ['.zip'],
      'application/x-rar-compressed': ['.rar'],
    },
  });

  const uploadFile = async (uploadFile: UploadFile) => {
    const formData = new FormData();
    formData.append('file', uploadFile.file);
    formData.append('conversation_id', conversationId);

    try {
      setUploadFiles(prev =>
        prev.map(f =>
          f.id === uploadFile.id
            ? { ...f, status: 'uploading' }
            : f
        )
      );

      const response = await axios.post('/api/media/upload/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          const progress = Math.round(
            (progressEvent.loaded * 100) / (progressEvent.total || 1)
          );
          
          setUploadFiles(prev =>
            prev.map(f =>
              f.id === uploadFile.id
                ? { ...f, progress }
                : f
            )
          );
        },
      });

      setUploadFiles(prev =>
        prev.map(f =>
          f.id === uploadFile.id
            ? { ...f, status: 'completed', mediaFile: response.data }
            : f
        )
      );

      return response.data;

    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Upload failed';
      
      setUploadFiles(prev =>
        prev.map(f =>
          f.id === uploadFile.id
            ? { ...f, status: 'error', error: errorMessage }
            : f
        )
      );

      throw error;
    }
  };

  const handleUploadAll = async () => {
    setIsUploading(true);
    
    const pendingFiles = uploadFiles.filter(f => f.status === 'pending');
    const uploadPromises = pendingFiles.map(uploadFile);
    
    try {
      const results = await Promise.allSettled(uploadPromises);
      const successful = results
        .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
        .map(result => result.value);
      
      if (successful.length > 0) {
        onUploadComplete(successful);
      }
      
      const failed = results.filter(result => result.status === 'rejected');
      if (failed.length > 0) {
        onUploadError(`${failed.length} files failed to upload`);
      }
      
    } catch (error) {
      onUploadError('Upload failed');
    } finally {
      setIsUploading(false);
    }
  };

  const removeFile = (fileId: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box>
      {/* Drop Zone */}
      <Box
        {...getRootProps()}
        sx={{
          border: '2px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.300',
          borderRadius: 2,
          p: 3,
          textAlign: 'center',
          cursor: 'pointer',
          backgroundColor: isDragActive ? 'action.hover' : 'transparent',
          transition: 'all 0.2s ease',
        }}
      >
        <input {...getInputProps()} />
        <UploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          or click to select files
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Supports images, videos, documents, and archives
        </Typography>
      </Box>

      {/* File List */}
      {uploadFiles.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Files to Upload ({uploadFiles.length})
          </Typography>
          
          <List>
            {uploadFiles.map((uploadFile) => (
              <ListItem key={uploadFile.id} divider>
                <ListItemText
                  primary={uploadFile.file.name}
                  secondary={
                    <Box>
                      <Typography variant="caption" display="block">
                        {formatFileSize(uploadFile.file.size)}
                      </Typography>
                      
                      {uploadFile.status === 'uploading' && (
                        <LinearProgress
                          variant="determinate"
                          value={uploadFile.progress}
                          sx={{ mt: 1 }}
                        />
                      )}
                      
                      {uploadFile.status === 'error' && (
                        <Alert severity="error" sx={{ mt: 1 }}>
                          {uploadFile.error}
                        </Alert>
                      )}
                      
                      {uploadFile.status === 'completed' && (
                        <Alert severity="success" sx={{ mt: 1 }}>
                          Upload completed
                        </Alert>
                      )}
                    </Box>
                  }
                />
                
                <ListItemSecondaryAction>
                  {uploadFile.status !== 'uploading' && (
                    <IconButton
                      edge="end"
                      onClick={() => removeFile(uploadFile.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  )}
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>

          {/* Upload Button */}
          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<UploadIcon />}
              onClick={handleUploadAll}
              disabled={isUploading || uploadFiles.every(f => f.status !== 'pending')}
            >
              {isUploading ? 'Uploading...' : 'Upload All'}
            </Button>
            
            <Button
              variant="outlined"
              onClick={() => setUploadFiles([])}
              disabled={isUploading}
            >
              Clear All
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default MediaUpload;
```

## Integration Points

### Message Integration
- Media messages display thumbnails
- Download links with expiration
- Progress indicators during upload

### Encryption Integration
- Client-side encryption before upload
- Secure key management
- Encrypted storage on server

## Acceptance Criteria

### Phase 5 Completion Checklist
- [ ] File upload with encryption working
- [ ] Multiple file type support
- [ ] Thumbnail generation functional
- [ ] Secure download system implemented
- [ ] Virus scanning integration
- [ ] File size and type validation
- [ ] Background processing working
- [ ] Media display in messages

### Testing Requirements
- [ ] File upload/download tests
- [ ] Encryption/decryption tests
- [ ] File validation tests
- [ ] Background processing tests
- [ ] Security tests for file access

## Common Issues & Troubleshooting

### Upload Failures
- Check file size limits
- Verify MIME type detection
- Ensure encryption key generation

### Processing Issues
- Monitor Celery worker status
- Check thumbnail generation
- Verify virus scanning integration

### Performance Optimization
- Implement file compression
- Use CDN for media delivery
- Optimize thumbnail sizes

## Next Phase Dependencies
- Media sharing fully functional
- File encryption working properly
- Background processing stable
- UI responsive for media operations

This phase enables rich media communication. Ensure thorough testing before proceeding to Phase 6 (WebRTC Calling).
