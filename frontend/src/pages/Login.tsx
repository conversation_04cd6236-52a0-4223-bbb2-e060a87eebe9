// frontend/src/pages/Login.tsx
import React, { useState, useRef } from 'react';
import { flushSync } from 'react-dom';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { Icon } from '../components/ui/Icon';
import { useAuth } from '../contexts/AuthContext';

export const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const errorRef = useRef('');
  const { login, loading, isAuthenticated, user } = useAuth();

  // Debug: Log auth state changes
  React.useEffect(() => {
    console.log('Auth state changed - isAuthenticated:', isAuthenticated, 'user:', user, 'loading:', loading);
  }, [isAuthenticated, user, loading]);

  // Debug: Track error state changes
  React.useEffect(() => {
    console.log('Error state changed to:', error);
  }, [error]);

  // Debug: Track component lifecycle
  React.useEffect(() => {
    console.log('Login component mounted');
    return () => {
      console.log('Login component unmounting');
    };
  }, []);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted, clearing error');
    setError('');

    try {
      console.log('Calling login function');
      await login(email, password);
      console.log('Login successful, navigating to dashboard');
      navigate('/dashboard');
    } catch (err: any) {
      console.log('Login error caught in component:', err);
      console.log('Setting error message:', err.message || 'Login failed');
      const errorMessage = err.message || 'Login failed';

      // Force synchronous state update
      flushSync(() => {
        setError(errorMessage);
      });

      errorRef.current = errorMessage;
      console.log('Error state set, current error:', errorMessage);
      console.log('Error ref set to:', errorRef.current);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
            <Icon name="message" className="text-blue-600" size={24} />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Or{' '}
            <Link
              to="/register"
              className="font-medium text-blue-600 hover:text-blue-500"
              data-testid="register-link"
            >
              create a new account
            </Link>
          </p>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <Input
              label="Email address"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              placeholder="Enter your email"
              icon="user"
              data-testid="email-input"
            />
            <div className="relative">
              <Input
                label="Password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                placeholder="Enter your password"
                data-testid="password-input"
              />
              <button
                type="button"
                className="absolute right-3 top-8 text-gray-400 hover:text-gray-600"
                onClick={() => setShowPassword(!showPassword)}
                data-testid="show-password-button"
              >
                <Icon name={showPassword ? 'eyeOff' : 'eye'} size={16} />
              </button>
            </div>
          </div>

          {error && (
            <div className="rounded-md bg-red-50 p-4" data-testid="error-message">
              <div className="flex">
                <Icon name="alert" className="text-red-400" size={16} />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    Login Error
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
          {console.log('Rendering Login component, error state:', error, 'error ref:', errorRef.current)}

          <div>
            <Button
              type="submit"
              className="w-full"
              loading={loading}
              disabled={!email || !password}
              data-testid="login-button"
            >
              Sign in
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
