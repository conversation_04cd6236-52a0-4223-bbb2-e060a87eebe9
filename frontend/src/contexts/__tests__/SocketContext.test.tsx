// frontend/src/contexts/__tests__/SocketContext.test.tsx
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Use vi.hoisted to ensure these are available during mock setup
const { mockSocket, mockIo } = vi.hoisted(() => {
  const mockSocket = {
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    disconnect: vi.fn(),
    connected: false,
  }

  const mockIo = vi.fn(() => mockSocket)

  return { mockSocket, mockIo }
})

vi.mock('socket.io-client', () => ({
  io: mockIo,
}))

import { render, screen, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { SocketProvider, useSocket } from '../SocketContext'
import { AuthProvider } from '../AuthContext'
import messageReducer from '../../store/slices/messageSlice'
import conversationReducer from '../../store/slices/conversationSlice'
import { api } from '../../services/api'

// Mock uuid
vi.mock('uuid', () => ({
  v4: () => 'mock-uuid-1234',
}))

// Mock auth context
const mockAuthContext = {
  user: {
    id: 'user-1',
    username: 'testuser',
    firstName: 'Test',
    lastName: 'User',
    profilePicture: null,
  },
  token: 'test-token',
  isAuthenticated: true,
  loading: false,
}

vi.mock('../AuthContext', async () => {
  const actual = await vi.importActual('../AuthContext')
  return {
    ...actual,
    useAuth: () => mockAuthContext,
  }
})

// Mock RTK Query mutation
const mockCreateConversation = vi.fn()
vi.mock('../../services/conversationApi', () => ({
  useCreateConversationMutation: () => [mockCreateConversation],
}))

// Test component that uses the SocketContext
const TestComponent = () => {
  const {
    isConnected,
    sendMessage,
    joinConversations,
    joinConversation,
    startTyping,
    stopTyping,
    setUserOnline,
  } = useSocket()

  return (
    <div>
      <div data-testid="connected">{isConnected ? 'Connected' : 'Disconnected'}</div>
      <button onClick={() => sendMessage('conv-1', 'Hello')} data-testid="send-message">
        Send Message
      </button>
      <button onClick={() => joinConversations()} data-testid="join-conversations">
        Join Conversations
      </button>
      <button onClick={() => joinConversation('conv-1')} data-testid="join-conversation">
        Join Conversation
      </button>
      <button onClick={() => startTyping('conv-1')} data-testid="start-typing">
        Start Typing
      </button>
      <button onClick={() => stopTyping('conv-1')} data-testid="stop-typing">
        Stop Typing
      </button>
      <button onClick={() => setUserOnline()} data-testid="set-online">
        Set Online
      </button>
    </div>
  )
}

const renderWithProviders = (component: React.ReactElement) => {
  const store = configureStore({
    reducer: {
      messages: messageReducer,
      conversations: conversationReducer,
      [api.reducerPath]: api.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(api.middleware),
  })

  return render(
    <Provider store={store}>
      <AuthProvider>
        <SocketProvider>
          {component}
        </SocketProvider>
      </AuthProvider>
    </Provider>
  )
}

describe('SocketContext', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Reset mock socket
    mockSocket.on.mockClear()
    mockSocket.off.mockClear()
    mockSocket.emit.mockClear()
    mockSocket.disconnect.mockClear()
    mockSocket.connected = false

    // Reset mock io function
    mockIo.mockReturnValue(mockSocket)

    mockAuthContext.isAuthenticated = true
    mockAuthContext.token = 'test-token'
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Provider initialization', () => {
    it('should initialize socket connection when authenticated', () => {
      renderWithProviders(<TestComponent />)

      expect(mockIo).toHaveBeenCalledWith('http://localhost:7000', {
        auth: {
          token: 'test-token',
        },
      })

      expect(screen.getByTestId('connected')).toHaveTextContent('Disconnected')
    })

    it('should not initialize socket when not authenticated', () => {
      mockAuthContext.isAuthenticated = false
      mockAuthContext.token = null

      renderWithProviders(<TestComponent />)

      expect(mockIo).not.toHaveBeenCalled()
    })

    it('should set up event listeners on socket connection', () => {
      renderWithProviders(<TestComponent />)

      expect(mockSocket.on).toHaveBeenCalledWith('connect', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('disconnect', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('error', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('conversations_joined', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('joined_conversation', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('new_message', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('message_sent', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('user_typing', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('user_status_change', expect.any(Function))
    })
  })

  describe('connection handling', () => {
    it('should update connection status on connect', async () => {
      renderWithProviders(<TestComponent />)

      // Simulate socket connect event
      const connectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect'
      )?.[1]

      act(() => {
        connectHandler?.()
      })

      await waitFor(() => {
        expect(screen.getByTestId('connected')).toHaveTextContent('Connected')
      })

      expect(mockSocket.emit).toHaveBeenCalledWith('join_conversations')
      expect(mockSocket.emit).toHaveBeenCalledWith('user_online')
    })

    it('should update connection status on disconnect', async () => {
      renderWithProviders(<TestComponent />)

      // First connect
      const connectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect'
      )?.[1]

      act(() => {
        connectHandler?.()
      })

      await waitFor(() => {
        expect(screen.getByTestId('connected')).toHaveTextContent('Connected')
      })

      // Then disconnect
      const disconnectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'disconnect'
      )?.[1]

      act(() => {
        disconnectHandler?.()
      })

      await waitFor(() => {
        expect(screen.getByTestId('connected')).toHaveTextContent('Disconnected')
      })
    })

    it('should handle socket errors', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      renderWithProviders(<TestComponent />)

      const errorHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'error'
      )?.[1]

      const mockError = { message: 'Connection failed', details: {} }

      act(() => {
        errorHandler?.(mockError)
      })

      expect(consoleSpy).toHaveBeenCalledWith('Socket error:', mockError)
      
      consoleSpy.mockRestore()
    })
  })

  describe('message operations', () => {
    it('should send message through socket', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(<TestComponent />)

      // Connect first
      const connectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect'
      )?.[1]

      act(() => {
        connectHandler?.()
      })

      await user.click(screen.getByTestId('send-message'))

      expect(mockSocket.emit).toHaveBeenCalledWith('send_message', {
        conversationId: 'conv-1',
        content: 'Hello',
        messageType: 'TEXT',
        tempId: 'mock-uuid-1234',
      })
    })

    it('should handle draft conversation creation', async () => {
      let contextSendMessage: any

      const TestComponentWithRef = () => {
        const { sendMessage, isConnected } = useSocket()
        contextSendMessage = sendMessage
        return (
          <div>
            <div data-testid="connected">{isConnected ? 'Connected' : 'Disconnected'}</div>
          </div>
        )
      }

      mockCreateConversation.mockResolvedValueOnce({
        unwrap: () => Promise.resolve({
          data: {
            id: 'real-conv-1',
            type: 'DIRECT',
            participants: [],
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-01-01T00:00:00Z',
          },
        }),
      })

      renderWithProviders(<TestComponentWithRef />)

      // Connect first
      const connectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect'
      )?.[1]

      act(() => {
        connectHandler?.()
      })

      // Send message to draft conversation
      await act(async () => {
        await contextSendMessage('draft-user-2', 'Hello')
      })

      expect(mockCreateConversation).toHaveBeenCalledWith({
        type: 'DIRECT',
        participant_ids: ['user'],
      })
    })

    it('should handle new message events', () => {
      renderWithProviders(<TestComponent />)

      const newMessageHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'new_message'
      )?.[1]

      const mockMessage = {
        id: 'msg-1',
        conversationId: 'conv-1',
        sender: { username: 'testuser' },
        content: 'Hello',
        createdAt: '2023-01-01T00:00:00Z',
      }

      act(() => {
        newMessageHandler?.(mockMessage)
      })

      // Should dispatch actions to Redux store
      // This would be verified by checking the store state in a real test
    })

    it('should handle message sent confirmation', () => {
      renderWithProviders(<TestComponent />)

      const messageSentHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'message_sent'
      )?.[1]

      const mockData = {
        tempId: 'temp-123',
        messageId: 'msg-456',
        status: 'sent',
      }

      act(() => {
        messageSentHandler?.(mockData)
      })

      // Should store the mapping for optimistic updates
    })
  })

  describe('conversation operations', () => {
    it('should join conversations', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(<TestComponent />)

      // Connect first
      const connectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect'
      )?.[1]

      act(() => {
        connectHandler?.()
      })

      await user.click(screen.getByTestId('join-conversations'))

      expect(mockSocket.emit).toHaveBeenCalledWith('join_conversations')
    })

    it('should join specific conversation', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(<TestComponent />)

      // Connect first
      const connectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect'
      )?.[1]

      act(() => {
        connectHandler?.()
      })

      await user.click(screen.getByTestId('join-conversation'))

      expect(mockSocket.emit).toHaveBeenCalledWith('join_conversation', {
        conversationId: 'conv-1',
      })
    })

    it('should handle conversations joined event', () => {
      renderWithProviders(<TestComponent />)

      const conversationsJoinedHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'conversations_joined'
      )?.[1]

      const mockData = {
        success: true,
        count: 2,
        conversations: [
          { id: 'conv-1', type: 'DIRECT' },
          { id: 'conv-2', type: 'GROUP' },
        ],
      }

      act(() => {
        conversationsJoinedHandler?.(mockData)
      })

      // Should dispatch actions to add conversations to store
    })
  })

  describe('typing indicators', () => {
    it('should start typing', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(<TestComponent />)

      // Connect first
      const connectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect'
      )?.[1]

      act(() => {
        connectHandler?.()
      })

      await user.click(screen.getByTestId('start-typing'))

      expect(mockSocket.emit).toHaveBeenCalledWith('typing_start', {
        conversationId: 'conv-1',
      })
    })

    it('should stop typing', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(<TestComponent />)

      // Connect first
      const connectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect'
      )?.[1]

      act(() => {
        connectHandler?.()
      })

      await user.click(screen.getByTestId('stop-typing'))

      expect(mockSocket.emit).toHaveBeenCalledWith('typing_stop', {
        conversationId: 'conv-1',
      })
    })

    it('should handle user typing events', () => {
      renderWithProviders(<TestComponent />)

      const userTypingHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'user_typing'
      )?.[1]

      const mockData = {
        userId: 'user-2',
        conversationId: 'conv-1',
        isTyping: true,
      }

      act(() => {
        userTypingHandler?.(mockData)
      })

      // Should dispatch typing action to Redux store
    })
  })

  describe('cleanup', () => {
    it('should cleanup socket connection on unmount', () => {
      const { unmount } = renderWithProviders(<TestComponent />)

      unmount()

      expect(mockSocket.off).toHaveBeenCalledWith('connect')
      expect(mockSocket.off).toHaveBeenCalledWith('disconnect')
      expect(mockSocket.off).toHaveBeenCalledWith('error')
      expect(mockSocket.disconnect).toHaveBeenCalled()
    })

    it('should cleanup when authentication changes', () => {
      const { rerender } = renderWithProviders(<TestComponent />)

      // Change auth state
      mockAuthContext.isAuthenticated = false
      mockAuthContext.token = null

      rerender(
        <Provider store={configureStore({
          reducer: {
            messages: messageReducer,
            conversations: conversationReducer,
            [api.reducerPath]: api.reducer,
          },
          middleware: (getDefaultMiddleware) =>
            getDefaultMiddleware().concat(api.middleware),
        })}>
          <AuthProvider>
            <SocketProvider>
              <TestComponent />
            </SocketProvider>
          </AuthProvider>
        </Provider>
      )

      expect(mockSocket.disconnect).toHaveBeenCalled()
    })
  })

  describe('error handling', () => {
    it('should throw error when useSocket is used outside provider', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      expect(() => {
        render(<TestComponent />)
      }).toThrow('useSocket must be used within a SocketProvider')

      consoleSpy.mockRestore()
    })

    it('should handle sendMessage when not connected', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      // Set auth context to not authenticated to prevent socket connection
      mockAuthContext.isAuthenticated = false
      mockAuthContext.token = null

      let contextSendMessage: any

      const TestComponentWithRef = () => {
        const { sendMessage, isConnected } = useSocket()
        contextSendMessage = sendMessage
        return (
          <div>
            <div data-testid="connected">{isConnected ? 'Connected' : 'Disconnected'}</div>
          </div>
        )
      }

      renderWithProviders(<TestComponentWithRef />)

      // Don't connect the socket
      const result = await contextSendMessage('conv-1', 'Hello')

      expect(result).toBe('')
      expect(consoleSpy).toHaveBeenCalledWith('Socket not connected or user not available')

      consoleSpy.mockRestore()
    })
  })
})
