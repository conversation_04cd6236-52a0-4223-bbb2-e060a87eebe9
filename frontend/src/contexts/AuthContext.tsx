// frontend/src/contexts/AuthContext.tsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import type { User } from '../types';
import {
  useLoginMutation,
  useRegisterMutation,
  useLogoutMutation,
  useGetCurrentUserQuery
} from '../services';
import type { LoginRequest } from '../types/api';
import { getUserDataSafe, clearUserData, validateAuthState } from '../utils/auth';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
}

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
}

interface RegisterData {
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  password: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Helper function to extract error message from RTK Query error
const getErrorMessage = (error: any): string => {
  // RTK Query error structure: error.data contains the response body
  if (error?.data?.error) {
    return error.data.error;
  }
  // For nested error structures
  if (error?.data?.data?.error) {
    return error.data.data.error;
  }
  // For direct API errors
  if (error?.error) {
    return error.error;
  }
  if (error?.message) {
    return error.message;
  }
  return 'Invalid credentials';
};

const authReducer = (state: AuthState, action: any): AuthState => {
  switch (action.type) {
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        loading: false,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
      };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    default:
      return state;
  }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Validate auth state on initialization
  const isValidAuth = validateAuthState();

  const [state, dispatch] = useReducer(authReducer, {
    user: isValidAuth ? getUserDataSafe() : null,
    token: isValidAuth ? localStorage.getItem('token') : null,
    isAuthenticated: isValidAuth,
    loading: true,
  });

  // RTK Query hooks
  const [loginMutation] = useLoginMutation();
  const [registerMutation] = useRegisterMutation();
  const [logoutMutation] = useLogoutMutation();

  // Check if user is authenticated on mount
  const { data: currentUserData, error: currentUserError, isLoading: isCheckingAuth } = useGetCurrentUserQuery(
    undefined,
    {
      skip: !state.token || !validateAuthState(),
      refetchOnMountOrArgChange: true
    }
  );

  // Validate auth state on mount and handle API responses
  useEffect(() => {
    if (currentUserData?.success && currentUserData.data) {
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user: currentUserData.data.user,
          token: localStorage.getItem('token')
        }
      });
    } else if (currentUserError) {
      // Token is invalid, clear all auth data
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      clearUserData();
      dispatch({ type: 'LOGOUT' });
    }

    if (!isCheckingAuth) {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [currentUserData, currentUserError, isCheckingAuth]);

  // Additional validation on mount to handle edge cases
  useEffect(() => {
    if (!validateAuthState() && (state.user || state.token || state.isAuthenticated)) {
      console.warn('Invalid auth state detected on mount, clearing...');
      dispatch({ type: 'LOGOUT' });
    }
  }, []); // Only run on mount

  const login = async (email: string, password: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      const result = await loginMutation({ email, password } as LoginRequest);

      if (result.error) {
        throw new Error(getErrorMessage(result.error));
      }

      if (result.data?.success && result.data.data) {
        const { user, tokens } = result.data.data;
        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: { user, token: tokens.access }
        });
      } else {
        throw new Error('Login failed');
      }
    } catch (error: any) {
      dispatch({ type: 'SET_LOADING', payload: false });
      throw error;
    }
  };

  const register = async (userData: RegisterData) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      const result = await registerMutation(userData);

      if (result.error) {
        throw new Error(getErrorMessage(result.error));
      }

      if (result.data?.success && result.data.data) {
        const { user, tokens } = result.data.data;
        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: { user, token: tokens.access }
        });
      } else {
        throw new Error('Registration failed');
      }
    } catch (error: any) {
      dispatch({ type: 'SET_LOADING', payload: false });
      throw error;
    }
  };

  const logout = async () => {
    try {
      await logoutMutation();
    } catch (error) {
      // Even if logout API fails, we should clear local state
      console.error('Logout API failed:', error);
    } finally {
      // Clear user data and update state
      clearUserData();
      dispatch({ type: 'LOGOUT' });
    }
  };

  return (
    <AuthContext.Provider value={{ ...state, login, register, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
