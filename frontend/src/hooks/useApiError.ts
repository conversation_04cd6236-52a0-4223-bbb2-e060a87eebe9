// frontend/src/hooks/useApiError.ts
import { useCallback } from 'react';
import type { BaseQueryError } from '../types';

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

export const useApiError = () => {
  const formatError = useCallback((error: any): ApiError => {
    // Handle RTK Query errors
    if (error && typeof error === 'object') {
      // RTK Query error format
      if (error.status && error.data) {
        return {
          message: error.data.error || 'An error occurred',
          status: error.status,
          code: error.data.code,
        };
      }
      
      // Network error
      if (error.error) {
        return {
          message: error.error,
          status: 0,
        };
      }
      
      // Generic error object
      if (error.message) {
        return {
          message: error.message,
        };
      }
    }
    
    // Fallback for unknown error types
    return {
      message: typeof error === 'string' ? error : 'An unexpected error occurred',
    };
  }, []);

  const isNetworkError = useCallback((error: any): boolean => {
    return error?.status === 0 || error?.error === 'Network Error';
  }, []);

  const isAuthError = useCallback((error: any): boolean => {
    return error?.status === 401 || error?.status === 403;
  }, []);

  const isValidationError = useCallback((error: any): boolean => {
    return error?.status === 400;
  }, []);

  const isServerError = useCallback((error: any): boolean => {
    return error?.status >= 500;
  }, []);

  return {
    formatError,
    isNetworkError,
    isAuthError,
    isValidationError,
    isServerError,
  };
};
