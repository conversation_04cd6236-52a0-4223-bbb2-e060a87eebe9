// frontend/src/services/encryptionApi.ts
import { api } from './api';
import type { KeyBundle } from '../utils/signalProtocol';

export interface UserKeyBundle {
  id: string;
  identity_public_key: string;
  signed_prekey_id: number;
  signed_prekey_public: string;
  signed_prekey_signature: string;
  created_at: string;
  updated_at: string;
}

export interface OneTimePreKey {
  id: string;
  key_id: number;
  public_key: string;
  is_used: boolean;
  created_at: string;
  used_at?: string;
}

export interface KeyBundleResponse {
  identity_public_key: string;
  signed_prekey_id: number;
  signed_prekey_public: string;
  signed_prekey_signature: string;
  one_time_prekey?: OneTimePreKey;
}

export interface ConversationSession {
  id: string;
  conversation: string;
  participant: string;
  session_state: Record<string, any>;
  root_key: string;
  chain_key_send?: string;
  chain_key_receive?: string;
  message_number_send: number;
  message_number_receive: number;
  previous_chain_length: number;
  created_at: string;
  updated_at: string;
}

export interface KeyBundleUpload {
  identity_public_key: string;
  signed_prekey_id: number;
  signed_prekey_public: string;
  signed_prekey_signature: string;
  one_time_prekeys?: Array<{
    key_id: number;
    public_key: string;
  }>;
}

export interface SessionCreate {
  conversation_id: string;
  participant_id: string;
  session_state: Record<string, any>;
  root_key: string;
  chain_key_send?: string;
  chain_key_receive?: string;
  message_number_send?: number;
  message_number_receive?: number;
  previous_chain_length?: number;
}

export interface SessionUpdate {
  session_state?: Record<string, any>;
  root_key?: string;
  chain_key_send?: string;
  chain_key_receive?: string;
  message_number_send?: number;
  message_number_receive?: number;
  previous_chain_length?: number;
}

export const encryptionApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Key bundle management
    uploadKeyBundle: builder.mutation<
      { message: string; key_bundle_id: string; one_time_prekeys_created: number },
      KeyBundleUpload
    >({
      query: (keyBundle) => ({
        url: '/encryption/key-bundle/upload/',
        method: 'POST',
        body: keyBundle,
      }),
      invalidatesTags: ['KeyBundle'],
    }),

    getKeyBundle: builder.query<KeyBundleResponse, string>({
      query: (userId) => `/encryption/key-bundle/${userId}/`,
      providesTags: (result, error, userId) => [{ type: 'KeyBundle', id: userId }],
    }),

    // One-time pre-key management
    uploadOneTimePreKeys: builder.mutation<
      { message: string; count: number; skipped: number },
      { prekeys: Array<{ key_id: number; public_key: string }> }
    >({
      query: (data) => ({
        url: '/encryption/prekeys/upload/',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['PreKeyCount'],
    }),

    getPreKeyCount: builder.query<
      { unused_prekeys: number; total_prekeys: number; usage_percentage: number },
      void
    >({
      query: () => '/encryption/prekeys/count/',
      providesTags: ['PreKeyCount'],
    }),

    // Key rotation
    rotateKeys: builder.mutation<
      { message: string; new_signed_prekey_id: number; new_one_time_prekeys_added: number },
      {
        new_signed_prekey_id: number;
        new_signed_prekey_public: string;
        new_signed_prekey_signature: string;
        additional_one_time_prekeys?: Array<{ key_id: number; public_key: string }>;
      }
    >({
      query: (data) => ({
        url: '/encryption/keys/rotate/',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['KeyBundle', 'PreKeyCount'],
    }),

    // Session management
    createConversationSession: builder.mutation<
      { message: string; session_id: string; created: boolean },
      SessionCreate
    >({
      query: (session) => ({
        url: '/encryption/sessions/create/',
        method: 'POST',
        body: session,
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'ConversationSession', id: arg.conversation_id },
      ],
    }),

    getConversationSession: builder.query<ConversationSession, string>({
      query: (conversationId) => `/encryption/sessions/${conversationId}/`,
      providesTags: (result, error, conversationId) => [
        { type: 'ConversationSession', id: conversationId },
      ],
    }),

    updateConversationSession: builder.mutation<
      { message: string; updated_fields: string[] },
      { conversationId: string; updates: SessionUpdate }
    >({
      query: ({ conversationId, updates }) => ({
        url: `/encryption/sessions/${conversationId}/update/`,
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'ConversationSession', id: arg.conversationId },
      ],
    }),
  }),
});

export const {
  useUploadKeyBundleMutation,
  useGetKeyBundleQuery,
  useUploadOneTimePreKeysMutation,
  useGetPreKeyCountQuery,
  useRotateKeysMutation,
  useCreateConversationSessionMutation,
  useGetConversationSessionQuery,
  useUpdateConversationSessionMutation,
} = encryptionApi;

// Helper functions to convert between API format and SignalProtocol format
export const convertApiKeyBundleToSignalFormat = (apiKeyBundle: KeyBundleResponse): KeyBundle => {
  return {
    identityKey: apiKeyBundle.identity_public_key,
    signedPreKey: {
      id: apiKeyBundle.signed_prekey_id,
      publicKey: apiKeyBundle.signed_prekey_public,
      signature: apiKeyBundle.signed_prekey_signature,
    },
    oneTimePreKey: apiKeyBundle.one_time_prekey ? {
      id: apiKeyBundle.one_time_prekey.key_id,
      publicKey: apiKeyBundle.one_time_prekey.public_key,
    } : undefined,
  };
};

export const convertSignalKeyBundleToApiFormat = (
  signalKeyBundle: KeyBundle,
  oneTimePreKeys?: Array<{ key_id: number; public_key: string }>
): KeyBundleUpload => {
  return {
    identity_public_key: signalKeyBundle.identityKey,
    signed_prekey_id: signalKeyBundle.signedPreKey.id,
    signed_prekey_public: signalKeyBundle.signedPreKey.publicKey,
    signed_prekey_signature: signalKeyBundle.signedPreKey.signature,
    one_time_prekeys: oneTimePreKeys,
  };
};
