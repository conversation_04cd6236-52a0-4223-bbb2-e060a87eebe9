// frontend/src/services/conversationApi.ts
import { api } from './api';
import type { ApiResponse, PaginatedResponse } from '../types';

export interface Conversation {
  id: string;
  type: 'DIRECT' | 'GROUP';
  name?: string;
  participants: Array<{
    id: string;
    username: string;
    first_name: string;
    last_name: string;
    profile_picture?: string;
  }>;
  lastMessage?: {
    id: string;
    content: string;
    sender: {
      username: string;
    };
    createdAt: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateConversationRequest {
  type: 'DIRECT' | 'GROUP';
  name?: string;
  participant_ids: string[];
}

export const conversationApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getConversations: builder.query<PaginatedResponse<Conversation>, void>({
      query: () => '/messaging/conversations/',
      providesTags: (result) => [
        { type: 'Conversation', id: 'LIST' },
        ...(result?.data?.results || []).map((conversation) => ({
          type: 'Conversation' as const,
          id: conversation.id,
        })),
      ],
      transformResponse: (response: any) => {
        // Handle the new API response format with success/data wrapper
        if (response.success && response.data) {
          return response.data;
        }
        // Handle legacy paginated responses
        if (response.results) {
          return response;
        }
        // Handle direct array responses
        if (Array.isArray(response)) {
          return {
            results: response,
            count: response.length,
          };
        }
        return response;
      },
      transformErrorResponse: (response: any) => ({
        status: response.status,
        data: response.data || { 
          success: false, 
          error: 'Failed to fetch conversations', 
          timestamp: new Date().toISOString() 
        }
      }),
    }),

    createConversation: builder.mutation<ApiResponse<Conversation>, CreateConversationRequest>({
      query: (conversationData) => ({
        url: '/messaging/conversations/create/',
        method: 'POST',
        body: conversationData,
      }),
      invalidatesTags: [
        { type: 'Conversation', id: 'LIST' },
      ],
      // Optimistic update
      onQueryStarted: async (conversationData, { dispatch, queryFulfilled }) => {
        try {
          const result = await queryFulfilled;
          
          if (result.data.success && result.data.data) {
            // Add new conversation to the list
            dispatch(
              conversationApi.util.updateQueryData('getConversations', undefined, (draft) => {
                if (draft.data && draft.data.results) {
                  // Check if conversation already exists
                  const exists = draft.data.results.some(conv => conv.id === result.data.data!.id);
                  if (!exists) {
                    draft.data.results.unshift(result.data.data!);
                    draft.data.count = (draft.data.count || 0) + 1;
                  }
                }
              })
            );
          }
        } catch (error) {
          // Error handling is done by RTK Query
        }
      },
      transformErrorResponse: (response: any) => ({
        status: response.status,
        data: response.data || { 
          success: false, 
          error: 'Failed to create conversation', 
          timestamp: new Date().toISOString() 
        }
      }),
    }),

    // For real-time conversation updates from socket
    updateConversationInCache: builder.mutation<void, { conversation: Conversation }>({
      queryFn: () => ({ data: undefined }),
      onQueryStarted: async ({ conversation }, { dispatch }) => {
        // Update conversation in cache without API call
        dispatch(
          conversationApi.util.updateQueryData('getConversations', undefined, (draft) => {
            if (draft.data && draft.data.results) {
              const index = draft.data.results.findIndex(conv => conv.id === conversation.id);
              if (index !== -1) {
                draft.data.results[index] = conversation;
              } else {
                // Add new conversation if it doesn't exist
                draft.data.results.unshift(conversation);
                draft.data.count = (draft.data.count || 0) + 1;
              }
            }
          })
        );
      },
    }),

    // Update last message for a conversation (called when new message arrives)
    updateConversationLastMessage: builder.mutation<void, {
      conversationId: string;
      lastMessage: Conversation['lastMessage'];
    }>({
      queryFn: () => ({ data: undefined }),
      onQueryStarted: async ({ conversationId, lastMessage }, { dispatch }) => {
        dispatch(
          conversationApi.util.updateQueryData('getConversations', undefined, (draft) => {
            if (draft.data && draft.data.results) {
              const conversation = draft.data.results.find(conv => conv.id === conversationId);
              if (conversation && lastMessage) {
                conversation.lastMessage = lastMessage;
                conversation.updatedAt = lastMessage.createdAt;
                
                // Move conversation to top
                const index = draft.data.results.indexOf(conversation);
                if (index > 0) {
                  draft.data.results.splice(index, 1);
                  draft.data.results.unshift(conversation);
                }
              }
            }
          })
        );
      },
    }),

    // Get a specific conversation by ID
    getConversation: builder.query<ApiResponse<Conversation>, string>({
      query: (conversationId) => `/messaging/conversations/${conversationId}/`,
      providesTags: (result, error, conversationId) => [
        { type: 'Conversation', id: conversationId },
      ],
      transformErrorResponse: (response: any) => ({
        status: response.status,
        data: response.data || { 
          success: false, 
          error: 'Failed to fetch conversation', 
          timestamp: new Date().toISOString() 
        }
      }),
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetConversationsQuery,
  useLazyGetConversationsQuery,
  useCreateConversationMutation,
  useUpdateConversationInCacheMutation,
  useUpdateConversationLastMessageMutation,
  useGetConversationQuery,
  useLazyGetConversationQuery,
} = conversationApi;
