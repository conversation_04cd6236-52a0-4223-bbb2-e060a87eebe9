// frontend/src/services/cacheUtils.ts
import { api } from './api';
import type { Message } from './messageApi';
import type { Conversation } from './conversationApi';

/**
 * Utility functions for managing RTK Query cache invalidation and updates
 */

// Cache invalidation helpers
export const invalidateConversations = () => {
  return api.util.invalidateTags([{ type: 'Conversation', id: 'LIST' }]);
};

export const invalidateMessages = (conversationId: string) => {
  return api.util.invalidateTags([{ type: 'Message', id: conversationId }]);
};

export const invalidateUserSearch = (query: string) => {
  return api.util.invalidateTags([{ type: 'User', id: `SEARCH-${query}` }]);
};

// Real-time update helpers for socket events
export const addMessageToCache = (conversationId: string, message: Message) => {
  return api.util.updateQueryData('getMessages', { conversationId }, (draft) => {
    if (draft.data && draft.data.results) {
      // Check if message already exists to avoid duplicates
      const exists = draft.data.results.some(msg => msg.id === message.id);
      if (!exists) {
        draft.data.results.push(message);
        draft.data.count = (draft.data.count || 0) + 1;
      }
    }
  });
};

export const updateConversationInCache = (conversation: Conversation) => {
  return api.util.updateQueryData('getConversations', undefined, (draft) => {
    if (draft.data && draft.data.results) {
      const index = draft.data.results.findIndex(conv => conv.id === conversation.id);
      if (index !== -1) {
        draft.data.results[index] = conversation;
      } else {
        // Add new conversation if it doesn't exist
        draft.data.results.unshift(conversation);
        draft.data.count = (draft.data.count || 0) + 1;
      }
    }
  });
};

export const updateConversationLastMessage = (
  conversationId: string, 
  lastMessage: Conversation['lastMessage']
) => {
  return api.util.updateQueryData('getConversations', undefined, (draft) => {
    if (draft.data && draft.data.results) {
      const conversation = draft.data.results.find(conv => conv.id === conversationId);
      if (conversation && lastMessage) {
        conversation.lastMessage = lastMessage;
        conversation.updatedAt = lastMessage.createdAt;
        
        // Move conversation to top
        const index = draft.data.results.indexOf(conversation);
        if (index > 0) {
          draft.data.results.splice(index, 1);
          draft.data.results.unshift(conversation);
        }
      }
    }
  });
};

// Optimistic update helpers
export const addOptimisticMessage = (
  conversationId: string, 
  tempMessage: Message
) => {
  return api.util.updateQueryData('getMessages', { conversationId }, (draft) => {
    if (draft.data && draft.data.results) {
      draft.data.results.push(tempMessage);
    }
  });
};

export const replaceOptimisticMessage = (
  conversationId: string, 
  tempId: string, 
  realMessage: Message
) => {
  return api.util.updateQueryData('getMessages', { conversationId }, (draft) => {
    if (draft.data && draft.data.results) {
      const index = draft.data.results.findIndex(msg => msg.id === tempId);
      if (index !== -1) {
        draft.data.results[index] = realMessage;
      }
    }
  });
};

export const removeOptimisticMessage = (conversationId: string, tempId: string) => {
  return api.util.updateQueryData('getMessages', { conversationId }, (draft) => {
    if (draft.data && draft.data.results) {
      const index = draft.data.results.findIndex(msg => msg.id === tempId);
      if (index !== -1) {
        draft.data.results.splice(index, 1);
      }
    }
  });
};

// Prefetch helpers for better UX
export const prefetchMessages = (conversationId: string) => {
  return api.util.prefetch('getMessages', { conversationId }, { force: false });
};

export const prefetchConversation = (conversationId: string) => {
  return api.util.prefetch('getConversation', conversationId, { force: false });
};

// Cache management
export const clearAllCache = () => {
  return api.util.resetApiState();
};

export const clearConversationCache = (conversationId: string) => {
  return api.util.invalidateTags([
    { type: 'Message', id: conversationId },
    { type: 'Conversation', id: conversationId }
  ]);
};
