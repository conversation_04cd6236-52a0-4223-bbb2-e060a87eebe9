// frontend/src/utils/auth.ts
import type { User, BackendUser } from '../types';

/**
 * Transform backend user data (snake_case) to frontend format (camelCase)
 */
export const transformBackendUser = (backendUser: BackendUser): User => {
  return {
    id: backendUser.id,
    email: backendUser.email,
    username: backendUser.username,
    firstName: backendUser.first_name,
    lastName: backendUser.last_name,
    profilePicture: backendUser.profile_picture,
    isVerified: backendUser.is_verified,
    lastSeen: backendUser.last_seen, // Keep as string for Redux serialization
    createdAt: backendUser.created_at, // Keep as string for Redux serialization
  };
};

/**
 * Store user data in localStorage
 */
export const storeUserData = (user: User): void => {
  try {
    localStorage.setItem('userData', JSON.stringify(user));
  } catch (error) {
    console.error('Failed to store user data:', error);
  }
};

/**
 * Validate user data structure
 */
const isValidUserData = (data: any): data is User => {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.id === 'string' &&
    typeof data.email === 'string' &&
    typeof data.username === 'string' &&
    typeof data.firstName === 'string' &&
    typeof data.lastName === 'string' &&
    typeof data.isVerified === 'boolean' &&
    typeof data.lastSeen === 'string' &&
    typeof data.createdAt === 'string'
  );
};

/**
 * Retrieve user data from localStorage
 */
export const getUserData = (): User | null => {
  try {
    const userData = localStorage.getItem('userData');
    if (!userData) return null;

    const parsed = JSON.parse(userData);

    // Validate that the parsed data has the required structure
    if (!isValidUserData(parsed)) {
      console.warn('Invalid user data structure in localStorage, clearing...');
      localStorage.removeItem('userData');
      return null;
    }

    // Validate date strings
    try {
      const lastSeenDate = new Date(parsed.lastSeen);
      const createdAtDate = new Date(parsed.createdAt);

      if (isNaN(lastSeenDate.getTime()) || isNaN(createdAtDate.getTime())) {
        console.warn('Invalid date values in user data, clearing...');
        localStorage.removeItem('userData');
        return null;
      }
    } catch (dateError) {
      console.warn('Failed to parse date values in user data, clearing...');
      localStorage.removeItem('userData');
      return null;
    }

    return parsed;
  } catch (error) {
    console.error('Failed to retrieve user data:', error);
    localStorage.removeItem('userData');
    return null;
  }
};

/**
 * Clear user data from localStorage
 */
export const clearUserData = (): void => {
  try {
    localStorage.removeItem('userData');
  } catch (error) {
    console.error('Failed to clear user data:', error);
  }
};

/**
 * Check if user is authenticated based on token and user data
 */
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('token');
  const userData = getUserData();
  return !!(token && userData);
};

/**
 * Validate and recover authentication state
 * Returns true if auth state is valid, false if it needs to be cleared
 */
export const validateAuthState = (): boolean => {
  const token = localStorage.getItem('token');
  const refreshToken = localStorage.getItem('refreshToken');
  const userData = getUserData();

  // If any critical auth data is missing, clear everything
  if (!token || !userData) {
    if (token || refreshToken || userData) {
      console.warn('Incomplete auth state detected, clearing all auth data...');
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      clearUserData();
    }
    return false;
  }

  return true;
};

/**
 * Safe getter for user data with fallback
 */
export const getUserDataSafe = (): User | null => {
  if (!validateAuthState()) {
    return null;
  }
  return getUserData();
};

/**
 * Convert user date strings to Date objects for display purposes
 */
export const getUserWithDates = (user: User): User & { lastSeenDate: Date; createdAtDate: Date } => {
  return {
    ...user,
    lastSeenDate: new Date(user.lastSeen),
    createdAtDate: new Date(user.createdAt),
  };
};

/**
 * Format user date for display
 */
export const formatUserDate = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleString();
  } catch (error) {
    return 'Invalid date';
  }
};
