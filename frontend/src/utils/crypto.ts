// frontend/src/utils/crypto.ts
export class CryptoUtils {
  private static readonly ALGORITHM = 'AES-GCM';
  private static readonly KEY_LENGTH = 256;
  private static readonly IV_LENGTH = 12;

  /**
   * Generate a new key pair for identity or ephemeral keys
   * Falls back to ECDH P-256 if X25519 is not supported
   */
  static async generateKeyPair(): Promise<CryptoKeyPair> {
    try {
      // Try X25519 first (modern browsers)
      console.debug('🔐 Attempting to generate X25519 key pair...');
      const keyPair = await window.crypto.subtle.generateKey(
        {
          name: 'X25519',
        },
        true, // extractable
        ['deriveKey', 'deriveBits']
      ) as CryptoKeyPair;

      if (!keyPair || !keyPair.publicKey || !keyPair.privateKey) {
        throw new Error('Generated key pair is invalid');
      }

      console.debug('🔐 ✅ X25519 key pair generated successfully');
      return keyPair;
    } catch (error) {
      console.warn('🔐 X25519 not supported, falling back to ECDH P-256:', error);

      try {
        // Fallback to ECDH P-256 (widely supported)
        console.debug('🔐 Attempting to generate ECDH P-256 key pair...');
        const keyPair = await window.crypto.subtle.generateKey(
          {
            name: 'ECDH',
            namedCurve: 'P-256',
          },
          true, // extractable
          ['deriveKey', 'deriveBits']
        ) as CryptoKeyPair;

        if (!keyPair || !keyPair.publicKey || !keyPair.privateKey) {
          throw new Error('Generated ECDH key pair is invalid');
        }

        console.debug('🔐 ✅ ECDH P-256 key pair generated successfully');
        return keyPair;
      } catch (fallbackError) {
        console.error('🔐 ❌ Both X25519 and ECDH P-256 key generation failed:', fallbackError);
        throw new Error(`Key generation failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`);
      }
    }
  }

  /**
   * Generate a signing key pair for signed pre-keys
   * Falls back to ECDSA P-256 if Ed25519 is not supported
   */
  static async generateSigningKeyPair(): Promise<CryptoKeyPair> {
    try {
      // Try Ed25519 first (modern browsers)
      console.debug('🔐 Attempting to generate Ed25519 signing key pair...');
      const keyPair = await window.crypto.subtle.generateKey(
        {
          name: 'Ed25519',
        },
        true,
        ['sign', 'verify']
      ) as CryptoKeyPair;

      if (!keyPair || !keyPair.publicKey || !keyPair.privateKey) {
        throw new Error('Generated signing key pair is invalid');
      }

      console.debug('🔐 ✅ Ed25519 signing key pair generated successfully');
      return keyPair;
    } catch (error) {
      console.warn('🔐 Ed25519 not supported, falling back to ECDSA P-256:', error);

      try {
        // Fallback to ECDSA P-256 (widely supported)
        console.debug('🔐 Attempting to generate ECDSA P-256 signing key pair...');
        const keyPair = await window.crypto.subtle.generateKey(
          {
            name: 'ECDSA',
            namedCurve: 'P-256',
          },
          true,
          ['sign', 'verify']
        ) as CryptoKeyPair;

        if (!keyPair || !keyPair.publicKey || !keyPair.privateKey) {
          throw new Error('Generated ECDSA signing key pair is invalid');
        }

        console.debug('🔐 ✅ ECDSA P-256 signing key pair generated successfully');
        return keyPair;
      } catch (fallbackError) {
        console.error('🔐 ❌ Both Ed25519 and ECDSA P-256 signing key generation failed:', fallbackError);
        throw new Error(`Signing key generation failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`);
      }
    }
  }

  /**
   * Export public key to base64 string
   */
  static async exportPublicKey(key: CryptoKey): Promise<string> {
    const exported = await window.crypto.subtle.exportKey('spki', key);
    return btoa(String.fromCharCode(...new Uint8Array(exported)));
  }

  /**
   * Export private key to base64 string
   */
  static async exportPrivateKey(key: CryptoKey): Promise<string> {
    const exported = await window.crypto.subtle.exportKey('pkcs8', key);
    return btoa(String.fromCharCode(...new Uint8Array(exported)));
  }

  /**
   * Import public key from base64 string
   */
  static async importPublicKey(keyData: string, algorithm: string): Promise<CryptoKey> {
    const binaryKey = Uint8Array.from(atob(keyData), c => c.charCodeAt(0));

    let keyAlgorithm;
    let keyUsages: KeyUsage[];

    if (algorithm === 'X25519') {
      keyAlgorithm = { name: 'X25519' };
      keyUsages = ['deriveKey', 'deriveBits'];
    } else if (algorithm === 'Ed25519') {
      keyAlgorithm = { name: 'Ed25519' };
      keyUsages = ['verify'];
    } else if (algorithm === 'ECDH') {
      keyAlgorithm = { name: 'ECDH', namedCurve: 'P-256' };
      keyUsages = ['deriveKey', 'deriveBits'];
    } else if (algorithm === 'ECDSA') {
      keyAlgorithm = { name: 'ECDSA', namedCurve: 'P-256' };
      keyUsages = ['verify'];
    } else {
      throw new Error(`Unsupported algorithm: ${algorithm}`);
    }

    return await window.crypto.subtle.importKey(
      'spki',
      binaryKey,
      keyAlgorithm,
      false,
      keyUsages
    );
  }

  /**
   * Import private key from base64 string
   */
  static async importPrivateKey(keyData: string, algorithm: string): Promise<CryptoKey> {
    const binaryKey = Uint8Array.from(atob(keyData), c => c.charCodeAt(0));

    let keyAlgorithm;
    let keyUsages: KeyUsage[];

    if (algorithm === 'X25519') {
      keyAlgorithm = { name: 'X25519' };
      keyUsages = ['deriveKey', 'deriveBits'];
    } else if (algorithm === 'Ed25519') {
      keyAlgorithm = { name: 'Ed25519' };
      keyUsages = ['sign'];
    } else if (algorithm === 'ECDH') {
      keyAlgorithm = { name: 'ECDH', namedCurve: 'P-256' };
      keyUsages = ['deriveKey', 'deriveBits'];
    } else if (algorithm === 'ECDSA') {
      keyAlgorithm = { name: 'ECDSA', namedCurve: 'P-256' };
      keyUsages = ['sign'];
    } else {
      throw new Error(`Unsupported algorithm: ${algorithm}`);
    }

    return await window.crypto.subtle.importKey(
      'pkcs8',
      binaryKey,
      keyAlgorithm,
      true,
      keyUsages
    );
  }

  /**
   * Derive shared secret from key pair (supports both X25519 and ECDH)
   */
  static async deriveSharedSecret(privateKey: CryptoKey, publicKey: CryptoKey): Promise<ArrayBuffer> {
    // Determine algorithm from the private key
    const algorithm = privateKey.algorithm.name;

    if (algorithm === 'X25519') {
      return await window.crypto.subtle.deriveBits(
        {
          name: 'X25519',
          public: publicKey,
        },
        privateKey,
        256
      );
    } else if (algorithm === 'ECDH') {
      return await window.crypto.subtle.deriveBits(
        {
          name: 'ECDH',
          public: publicKey,
        },
        privateKey,
        256
      );
    } else {
      throw new Error(`Unsupported algorithm for key derivation: ${algorithm}`);
    }
  }

  /**
   * Derive encryption key from shared secret using HKDF
   */
  static async deriveEncryptionKey(sharedSecret: ArrayBuffer, salt: Uint8Array, info?: string): Promise<CryptoKey> {
    const keyMaterial = await window.crypto.subtle.importKey(
      'raw',
      sharedSecret,
      'HKDF',
      false,
      ['deriveKey']
    );

    return await window.crypto.subtle.deriveKey(
      {
        name: 'HKDF',
        hash: 'SHA-256',
        salt: salt,
        info: new TextEncoder().encode(info || 'ChatApp Message Key'),
      },
      keyMaterial,
      { name: this.ALGORITHM, length: this.KEY_LENGTH },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Encrypt message content using AES-256-GCM
   */
  static async encryptMessage(content: string, key: CryptoKey): Promise<{
    encryptedData: string;
    iv: string;
  }> {
    const iv = window.crypto.getRandomValues(new Uint8Array(this.IV_LENGTH));
    const encodedContent = new TextEncoder().encode(content);

    const encryptedBuffer = await window.crypto.subtle.encrypt(
      {
        name: this.ALGORITHM,
        iv: iv,
      },
      key,
      encodedContent
    );

    return {
      encryptedData: btoa(String.fromCharCode(...new Uint8Array(encryptedBuffer))),
      iv: btoa(String.fromCharCode(...iv)),
    };
  }

  /**
   * Decrypt message content using AES-256-GCM
   */
  static async decryptMessage(encryptedData: string, iv: string, key: CryptoKey): Promise<string> {
    const encryptedBuffer = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));
    const ivBuffer = Uint8Array.from(atob(iv), c => c.charCodeAt(0));

    const decryptedBuffer = await window.crypto.subtle.decrypt(
      {
        name: this.ALGORITHM,
        iv: ivBuffer,
      },
      key,
      encryptedBuffer
    );

    return new TextDecoder().decode(decryptedBuffer);
  }

  /**
   * Sign data using signing key (supports both Ed25519 and ECDSA)
   */
  static async signData(data: string | Uint8Array, privateKey: CryptoKey): Promise<string> {
    const dataToSign = typeof data === 'string' ? new TextEncoder().encode(data) : data;
    const algorithm = privateKey.algorithm.name;

    let signature;
    if (algorithm === 'Ed25519') {
      signature = await window.crypto.subtle.sign(
        'Ed25519',
        privateKey,
        dataToSign
      );
    } else if (algorithm === 'ECDSA') {
      signature = await window.crypto.subtle.sign(
        {
          name: 'ECDSA',
          hash: 'SHA-256',
        },
        privateKey,
        dataToSign
      );
    } else {
      throw new Error(`Unsupported algorithm for signing: ${algorithm}`);
    }

    return btoa(String.fromCharCode(...new Uint8Array(signature)));
  }

  /**
   * Verify signature using public key (supports both Ed25519 and ECDSA)
   */
  static async verifySignature(data: string | Uint8Array, signature: string, publicKey: CryptoKey): Promise<boolean> {
    const dataToVerify = typeof data === 'string' ? new TextEncoder().encode(data) : data;
    const signatureBuffer = Uint8Array.from(atob(signature), c => c.charCodeAt(0));
    const algorithm = publicKey.algorithm.name;

    if (algorithm === 'Ed25519') {
      return await window.crypto.subtle.verify(
        'Ed25519',
        publicKey,
        signatureBuffer,
        dataToVerify
      );
    } else if (algorithm === 'ECDSA') {
      return await window.crypto.subtle.verify(
        {
          name: 'ECDSA',
          hash: 'SHA-256',
        },
        publicKey,
        signatureBuffer,
        dataToVerify
      );
    } else {
      throw new Error(`Unsupported algorithm for signature verification: ${algorithm}`);
    }
  }

  /**
   * Generate random bytes
   */
  static generateRandomBytes(length: number): Uint8Array {
    return window.crypto.getRandomValues(new Uint8Array(length));
  }

  /**
   * Get the algorithm name from a CryptoKey
   */
  static getKeyAlgorithm(key: CryptoKey): string {
    return key.algorithm.name;
  }

  /**
   * Check if the browser supports modern cryptographic algorithms
   */
  static async checkCryptoSupport(): Promise<{
    supportsX25519: boolean;
    supportsEd25519: boolean;
    supportsECDH: boolean;
    supportsECDSA: boolean;
  }> {
    const support = {
      supportsX25519: false,
      supportsEd25519: false,
      supportsECDH: false,
      supportsECDSA: false,
    };

    // Test X25519 support - use same key usages as generateKeyPair
    try {
      const keyPair = await window.crypto.subtle.generateKey(
        { name: 'X25519' },
        false,
        ['deriveKey', 'deriveBits']
      );
      // Verify we can actually use the keys
      if (keyPair && keyPair.publicKey && keyPair.privateKey) {
        support.supportsX25519 = true;
      }
    } catch (e) {
      console.debug('🔐 X25519 not supported:', e);
    }

    // Test Ed25519 support - use same key usages as generateSigningKeyPair
    try {
      const keyPair = await window.crypto.subtle.generateKey(
        { name: 'Ed25519' },
        false,
        ['sign', 'verify']
      );
      // Verify we can actually use the keys
      if (keyPair && keyPair.publicKey && keyPair.privateKey) {
        support.supportsEd25519 = true;
      }
    } catch (e) {
      console.debug('🔐 Ed25519 not supported:', e);
    }

    // Test ECDH support - use same key usages as generateKeyPair fallback
    try {
      const keyPair = await window.crypto.subtle.generateKey(
        { name: 'ECDH', namedCurve: 'P-256' },
        false,
        ['deriveKey', 'deriveBits']
      );
      // Verify we can actually use the keys
      if (keyPair && keyPair.publicKey && keyPair.privateKey) {
        support.supportsECDH = true;
      }
    } catch (e) {
      console.debug('🔐 ECDH not supported:', e);
    }

    // Test ECDSA support - use same key usages as generateSigningKeyPair fallback
    try {
      const keyPair = await window.crypto.subtle.generateKey(
        { name: 'ECDSA', namedCurve: 'P-256' },
        false,
        ['sign', 'verify']
      );
      // Verify we can actually use the keys
      if (keyPair && keyPair.publicKey && keyPair.privateKey) {
        support.supportsECDSA = true;
      }
    } catch (e) {
      console.debug('🔐 ECDSA not supported:', e);
    }

    return support;
  }

  /**
   * Hash data using SHA-256
   */
  static async hash(data: string | Uint8Array): Promise<string> {
    const dataToHash = typeof data === 'string' ? new TextEncoder().encode(data) : data;
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', dataToHash);
    return btoa(String.fromCharCode(...new Uint8Array(hashBuffer)));
  }

  /**
   * Derive key from password using PBKDF2
   */
  static async deriveKeyFromPassword(password: string, salt: Uint8Array, iterations: number = 100000): Promise<CryptoKey> {
    const keyMaterial = await window.crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(password),
      'PBKDF2',
      false,
      ['deriveKey']
    );

    return await window.crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: salt,
        iterations: iterations,
        hash: 'SHA-256',
      },
      keyMaterial,
      { name: this.ALGORITHM, length: this.KEY_LENGTH },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Combine multiple shared secrets using HKDF
   */
  static async combineSharedSecrets(secrets: ArrayBuffer[], salt?: Uint8Array): Promise<ArrayBuffer> {
    // Concatenate all secrets
    const totalLength = secrets.reduce((acc, secret) => acc + secret.byteLength, 0);
    const combined = new Uint8Array(totalLength);
    let offset = 0;
    
    for (const secret of secrets) {
      combined.set(new Uint8Array(secret), offset);
      offset += secret.byteLength;
    }

    // Use HKDF to derive a proper key from the combined secrets
    const keyMaterial = await window.crypto.subtle.importKey(
      'raw',
      combined,
      'HKDF',
      false,
      ['deriveBits']
    );

    return await window.crypto.subtle.deriveBits(
      {
        name: 'HKDF',
        hash: 'SHA-256',
        salt: salt || new Uint8Array(32), // Default salt if none provided
        info: new TextEncoder().encode('Combined Shared Secret'),
      },
      keyMaterial,
      256
    );
  }

  /**
   * Constant-time comparison of two arrays
   */
  static constantTimeEqual(a: Uint8Array, b: Uint8Array): boolean {
    if (a.length !== b.length) {
      return false;
    }
    
    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a[i] ^ b[i];
    }
    
    return result === 0;
  }
}
