// frontend/src/utils/__tests__/encryption-integration.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CryptoUtils } from '../crypto';
import { SignalProtocol } from '../signalProtocol';

// Mock Web Crypto API for testing
const mockCrypto = {
  subtle: {
    generateKey: vi.fn(),
    exportKey: vi.fn(),
    importKey: vi.fn(),
    deriveBits: vi.fn(),
    deriveKey: vi.fn(),
    encrypt: vi.fn(),
    decrypt: vi.fn(),
    sign: vi.fn(),
    verify: vi.fn(),
    digest: vi.fn(),
  },
  getRandomValues: vi.fn(),
};

// Setup global crypto mock
beforeEach(() => {
  Object.defineProperty(global, 'crypto', {
    value: mockCrypto,
    writable: true,
  });
  vi.clearAllMocks();
});

describe('End-to-End Encryption Integration Tests', () => {
  describe('Complete Encryption Flow', () => {
    it('should perform complete encryption flow between two users', async () => {
      // Mock crypto operations
      const mockKeyPair = {
        publicKey: { type: 'public' } as CryptoKey,
        privateKey: { type: 'private' } as CryptoKey,
      };
      
      mockCrypto.subtle.generateKey.mockResolvedValue(mockKeyPair);
      mockCrypto.subtle.exportKey.mockResolvedValue(new ArrayBuffer(32));
      mockCrypto.subtle.importKey.mockResolvedValue({ type: 'public' } as CryptoKey);
      mockCrypto.subtle.deriveBits.mockResolvedValue(new ArrayBuffer(32));
      mockCrypto.subtle.deriveKey.mockResolvedValue({ type: 'secret' } as CryptoKey);
      mockCrypto.subtle.encrypt.mockResolvedValue(new ArrayBuffer(16));
      mockCrypto.subtle.decrypt.mockResolvedValue(new TextEncoder().encode('Hello, World!'));
      mockCrypto.subtle.sign.mockResolvedValue(new ArrayBuffer(64));
      mockCrypto.getRandomValues.mockReturnValue(new Uint8Array(32));

      // Initialize two Signal Protocol instances (Alice and Bob)
      const alice = new SignalProtocol();
      const bob = new SignalProtocol();

      // Initialize keys for both users
      await alice.initializeKeys();
      await bob.initializeKeys();

      // Alice gets Bob's key bundle
      const bobKeyBundle = await bob.getKeyBundle();

      // Alice initializes session with Bob
      await alice.initializeSession('conversation-1', bobKeyBundle);

      // Alice encrypts a message
      const originalMessage = 'Hello, Bob!';
      const encryptedMessage = await alice.encryptMessage('conversation-1', originalMessage);

      expect(encryptedMessage).toHaveProperty('encryptedContent');
      expect(encryptedMessage).toHaveProperty('iv');
      expect(encryptedMessage).toHaveProperty('messageNumber');
      expect(encryptedMessage).toHaveProperty('ratchetKey');

      // Bob initializes session with Alice's key bundle
      const aliceKeyBundle = await alice.getKeyBundle();
      await bob.initializeSession('conversation-1', aliceKeyBundle);

      // Bob decrypts the message
      const decryptedMessage = await bob.decryptMessage('conversation-1', encryptedMessage);

      expect(decryptedMessage).toBe('Hello, World!'); // Mocked return value
    });

    it('should handle multiple messages with forward secrecy', async () => {
      // Setup mocks
      const mockKeyPair = {
        publicKey: { type: 'public' } as CryptoKey,
        privateKey: { type: 'private' } as CryptoKey,
      };
      
      mockCrypto.subtle.generateKey.mockResolvedValue(mockKeyPair);
      mockCrypto.subtle.exportKey.mockResolvedValue(new ArrayBuffer(32));
      mockCrypto.subtle.importKey.mockResolvedValue({ type: 'public' } as CryptoKey);
      mockCrypto.subtle.deriveBits.mockResolvedValue(new ArrayBuffer(32));
      mockCrypto.subtle.deriveKey.mockResolvedValue({ type: 'secret' } as CryptoKey);
      mockCrypto.subtle.encrypt.mockResolvedValue(new ArrayBuffer(16));
      mockCrypto.subtle.decrypt.mockResolvedValue(new TextEncoder().encode('Decrypted message'));
      mockCrypto.getRandomValues.mockReturnValue(new Uint8Array(32));

      const alice = new SignalProtocol();
      const bob = new SignalProtocol();

      await alice.initializeKeys();
      await bob.initializeKeys();

      const bobKeyBundle = await bob.getKeyBundle();
      await alice.initializeSession('conversation-1', bobKeyBundle);

      const aliceKeyBundle = await alice.getKeyBundle();
      await bob.initializeSession('conversation-1', aliceKeyBundle);

      // Send multiple messages
      const messages = ['Message 1', 'Message 2', 'Message 3'];
      const encryptedMessages = [];

      for (const message of messages) {
        const encrypted = await alice.encryptMessage('conversation-1', message);
        encryptedMessages.push(encrypted);
        
        // Verify message numbers increment
        expect(encrypted.messageNumber).toBe(encryptedMessages.length - 1);
      }

      // Decrypt all messages
      for (let i = 0; i < encryptedMessages.length; i++) {
        const decrypted = await bob.decryptMessage('conversation-1', encryptedMessages[i]);
        expect(decrypted).toBe('Decrypted message'); // Mocked return
      }

      // Verify session state updates
      const aliceSession = alice.getSessionState('conversation-1');
      const bobSession = bob.getSessionState('conversation-1');

      expect(aliceSession?.messageNumberSend).toBe(3);
      expect(bobSession?.messageNumberReceive).toBe(3);
    });

    it('should handle out-of-order message delivery', async () => {
      // Setup mocks
      const mockKeyPair = {
        publicKey: { type: 'public' } as CryptoKey,
        privateKey: { type: 'private' } as CryptoKey,
      };
      
      mockCrypto.subtle.generateKey.mockResolvedValue(mockKeyPair);
      mockCrypto.subtle.exportKey.mockResolvedValue(new ArrayBuffer(32));
      mockCrypto.subtle.importKey.mockResolvedValue({ type: 'public' } as CryptoKey);
      mockCrypto.subtle.deriveBits.mockResolvedValue(new ArrayBuffer(32));
      mockCrypto.subtle.deriveKey.mockResolvedValue({ type: 'secret' } as CryptoKey);
      mockCrypto.subtle.encrypt.mockResolvedValue(new ArrayBuffer(16));
      mockCrypto.subtle.decrypt.mockResolvedValue(new TextEncoder().encode('Decrypted message'));
      mockCrypto.getRandomValues.mockReturnValue(new Uint8Array(32));

      const alice = new SignalProtocol();
      const bob = new SignalProtocol();

      await alice.initializeKeys();
      await bob.initializeKeys();

      const bobKeyBundle = await bob.getKeyBundle();
      await alice.initializeSession('conversation-1', bobKeyBundle);

      const aliceKeyBundle = await alice.getKeyBundle();
      await bob.initializeSession('conversation-1', aliceKeyBundle);

      // Alice sends multiple messages
      const message1 = await alice.encryptMessage('conversation-1', 'First message');
      const message2 = await alice.encryptMessage('conversation-1', 'Second message');
      const message3 = await alice.encryptMessage('conversation-1', 'Third message');

      // Bob receives messages out of order (3, 1, 2)
      const decrypted3 = await bob.decryptMessage('conversation-1', message3);
      const decrypted1 = await bob.decryptMessage('conversation-1', message1);
      const decrypted2 = await bob.decryptMessage('conversation-1', message2);

      // All should decrypt successfully
      expect(decrypted3).toBe('Decrypted message');
      expect(decrypted1).toBe('Decrypted message');
      expect(decrypted2).toBe('Decrypted message');
    });
  });

  describe('Error Handling', () => {
    it('should handle encryption failures gracefully', async () => {
      mockCrypto.subtle.generateKey.mockResolvedValue({
        publicKey: { type: 'public' } as CryptoKey,
        privateKey: { type: 'private' } as CryptoKey,
      });
      mockCrypto.subtle.exportKey.mockResolvedValue(new ArrayBuffer(32));
      mockCrypto.subtle.encrypt.mockRejectedValue(new Error('Encryption failed'));

      const alice = new SignalProtocol();
      await alice.initializeKeys();

      // Mock session state
      alice.updateSessionState('conversation-1', {
        rootKey: 'mock-root-key',
        messageNumberSend: 0,
        messageNumberReceive: 0,
        previousChainLength: 0,
      });

      await expect(alice.encryptMessage('conversation-1', 'Test message')).rejects.toThrow('Encryption failed');
    });

    it('should handle decryption failures gracefully', async () => {
      mockCrypto.subtle.generateKey.mockResolvedValue({
        publicKey: { type: 'public' } as CryptoKey,
        privateKey: { type: 'private' } as CryptoKey,
      });
      mockCrypto.subtle.exportKey.mockResolvedValue(new ArrayBuffer(32));
      mockCrypto.subtle.deriveKey.mockResolvedValue({ type: 'secret' } as CryptoKey);
      mockCrypto.subtle.decrypt.mockRejectedValue(new Error('Decryption failed'));

      const bob = new SignalProtocol();
      await bob.initializeKeys();

      // Mock session state
      bob.updateSessionState('conversation-1', {
        rootKey: 'mock-root-key',
        messageNumberSend: 0,
        messageNumberReceive: 0,
        previousChainLength: 0,
      });

      const encryptedMessage = {
        encryptedContent: 'encrypted-data',
        iv: 'mock-iv',
        messageNumber: 0,
        ratchetKey: 'mock-ratchet-key',
        previousChainLength: 0,
      };

      await expect(bob.decryptMessage('conversation-1', encryptedMessage)).rejects.toThrow('Decryption failed');
    });

    it('should handle missing session gracefully', async () => {
      const alice = new SignalProtocol();
      await alice.initializeKeys();

      await expect(alice.encryptMessage('nonexistent-conversation', 'Test')).rejects.toThrow(
        'Session not found for conversation'
      );

      const encryptedMessage = {
        encryptedContent: 'encrypted-data',
        iv: 'mock-iv',
        messageNumber: 0,
        ratchetKey: 'mock-ratchet-key',
        previousChainLength: 0,
      };

      await expect(alice.decryptMessage('nonexistent-conversation', encryptedMessage)).rejects.toThrow(
        'Session not found for conversation'
      );
    });
  });

  describe('Key Management', () => {
    it('should generate and manage one-time pre-keys', async () => {
      mockCrypto.subtle.generateKey.mockResolvedValue({
        publicKey: { type: 'public' } as CryptoKey,
        privateKey: { type: 'private' } as CryptoKey,
      });
      mockCrypto.subtle.exportKey.mockResolvedValue(new ArrayBuffer(32));

      const alice = new SignalProtocol();
      await alice.initializeKeys();

      const preKeys = await alice.generateOneTimePreKeys(10, 100);

      expect(preKeys).toHaveLength(10);
      expect(preKeys[0]).toHaveProperty('id', 100);
      expect(preKeys[0]).toHaveProperty('publicKey');
      expect(preKeys[9]).toHaveProperty('id', 109);
    });

    it('should rotate signed pre-keys', async () => {
      mockCrypto.subtle.generateKey.mockResolvedValue({
        publicKey: { type: 'public' } as CryptoKey,
        privateKey: { type: 'private' } as CryptoKey,
      });
      mockCrypto.subtle.exportKey.mockResolvedValue(new ArrayBuffer(32));
      mockCrypto.subtle.sign.mockResolvedValue(new ArrayBuffer(64));

      const alice = new SignalProtocol();
      await alice.initializeKeys();

      const rotatedKey = await alice.rotateSignedPreKey();

      expect(rotatedKey).toHaveProperty('id');
      expect(rotatedKey).toHaveProperty('publicKey');
      expect(rotatedKey).toHaveProperty('signature');
      expect(typeof rotatedKey.id).toBe('number');
    });

    it('should export and import session state', () => {
      const alice = new SignalProtocol();
      
      const sessionState = {
        rootKey: 'mock-root-key',
        messageNumberSend: 5,
        messageNumberReceive: 3,
        previousChainLength: 0,
      };

      alice.updateSessionState('conversation-1', sessionState);
      const exported = alice.exportSessionState('conversation-1');
      
      expect(exported).toBe(JSON.stringify(sessionState));

      alice.clearSessions();
      alice.importSessionState('conversation-1', exported!);
      
      const imported = alice.getSessionState('conversation-1');
      expect(imported).toEqual(sessionState);
    });
  });

  describe('Performance and Security', () => {
    it('should handle large messages efficiently', async () => {
      // Setup mocks
      mockCrypto.subtle.generateKey.mockResolvedValue({
        publicKey: { type: 'public' } as CryptoKey,
        privateKey: { type: 'private' } as CryptoKey,
      });
      mockCrypto.subtle.exportKey.mockResolvedValue(new ArrayBuffer(32));
      mockCrypto.subtle.deriveKey.mockResolvedValue({ type: 'secret' } as CryptoKey);
      mockCrypto.subtle.encrypt.mockResolvedValue(new ArrayBuffer(1000));
      mockCrypto.subtle.decrypt.mockResolvedValue(new TextEncoder().encode('Large message content'));
      mockCrypto.getRandomValues.mockReturnValue(new Uint8Array(32));

      const alice = new SignalProtocol();
      await alice.initializeKeys();

      alice.updateSessionState('conversation-1', {
        rootKey: 'mock-root-key',
        messageNumberSend: 0,
        messageNumberReceive: 0,
        previousChainLength: 0,
      });

      // Create a large message (4KB)
      const largeMessage = 'A'.repeat(4096);
      
      const startTime = Date.now();
      const encrypted = await alice.encryptMessage('conversation-1', largeMessage);
      const encryptionTime = Date.now() - startTime;

      expect(encrypted).toHaveProperty('encryptedContent');
      expect(encryptionTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should clear sensitive data on logout', () => {
      const alice = new SignalProtocol();
      
      alice.updateSessionState('conversation-1', {
        rootKey: 'sensitive-root-key',
        messageNumberSend: 5,
        messageNumberReceive: 3,
        previousChainLength: 0,
      });

      alice.updateSessionState('conversation-2', {
        rootKey: 'another-sensitive-key',
        messageNumberSend: 2,
        messageNumberReceive: 1,
        previousChainLength: 0,
      });

      // Verify sessions exist
      expect(alice.getSessionState('conversation-1')).toBeDefined();
      expect(alice.getSessionState('conversation-2')).toBeDefined();

      // Clear all sessions
      alice.clearSessions();

      // Verify all sessions are cleared
      expect(alice.getSessionState('conversation-1')).toBeUndefined();
      expect(alice.getSessionState('conversation-2')).toBeUndefined();
    });
  });
});
