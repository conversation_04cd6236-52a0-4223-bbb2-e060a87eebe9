// frontend/src/utils/__tests__/crypto.test.ts
import { describe, it, expect, beforeAll, vi } from 'vitest';
import { CryptoUtils } from '../crypto';

// Mock Web Crypto API for testing
const mockCrypto = {
  subtle: {
    generateKey: vi.fn(),
    exportKey: vi.fn(),
    importKey: vi.fn(),
    deriveBits: vi.fn(),
    deriveKey: vi.fn(),
    encrypt: vi.fn(),
    decrypt: vi.fn(),
    sign: vi.fn(),
    verify: vi.fn(),
    digest: vi.fn(),
  },
  getRandomValues: vi.fn(),
};

// Setup global crypto mock
beforeAll(() => {
  Object.defineProperty(global, 'crypto', {
    value: mockCrypto,
    writable: true,
  });
});

describe('CryptoUtils', () => {
  describe('generateKeyPair', () => {
    it('should generate X25519 key pair', async () => {
      const mockKeyPair = {
        publicKey: { type: 'public' },
        privateKey: { type: 'private' },
      };
      
      mockCrypto.subtle.generateKey.mockResolvedValue(mockKeyPair);
      
      const result = await CryptoUtils.generateKeyPair();
      
      expect(mockCrypto.subtle.generateKey).toHaveBeenCalledWith(
        { name: 'X25519' },
        true,
        ['deriveKey', 'deriveBits']
      );
      expect(result).toBe(mockKeyPair);
    });
  });

  describe('generateSigningKeyPair', () => {
    it('should generate Ed25519 signing key pair', async () => {
      const mockKeyPair = {
        publicKey: { type: 'public' },
        privateKey: { type: 'private' },
      };
      
      mockCrypto.subtle.generateKey.mockResolvedValue(mockKeyPair);
      
      const result = await CryptoUtils.generateSigningKeyPair();
      
      expect(mockCrypto.subtle.generateKey).toHaveBeenCalledWith(
        { name: 'Ed25519' },
        true,
        ['sign', 'verify']
      );
      expect(result).toBe(mockKeyPair);
    });
  });

  describe('exportPublicKey', () => {
    it('should export public key to base64', async () => {
      const mockKey = { type: 'public' };
      const mockExported = new ArrayBuffer(32);
      const mockArray = new Uint8Array(mockExported);
      mockArray.fill(65); // Fill with 'A' character code
      
      mockCrypto.subtle.exportKey.mockResolvedValue(mockExported);
      
      const result = await CryptoUtils.exportPublicKey(mockKey as any);
      
      expect(mockCrypto.subtle.exportKey).toHaveBeenCalledWith('spki', mockKey);
      expect(result).toBe(btoa(String.fromCharCode(...mockArray)));
    });
  });

  describe('importPublicKey', () => {
    it('should import X25519 public key from base64', async () => {
      const keyData = 'QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUE='; // Base64 for 32 'A's
      const mockKey = { type: 'public' };
      
      mockCrypto.subtle.importKey.mockResolvedValue(mockKey);
      
      const result = await CryptoUtils.importPublicKey(keyData, 'X25519');
      
      expect(mockCrypto.subtle.importKey).toHaveBeenCalledWith(
        'spki',
        expect.any(Uint8Array),
        { name: 'X25519' },
        false,
        ['deriveKey', 'deriveBits']
      );
      expect(result).toBe(mockKey);
    });

    it('should import Ed25519 public key from base64', async () => {
      const keyData = 'QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUE=';
      const mockKey = { type: 'public' };
      
      mockCrypto.subtle.importKey.mockResolvedValue(mockKey);
      
      const result = await CryptoUtils.importPublicKey(keyData, 'Ed25519');
      
      expect(mockCrypto.subtle.importKey).toHaveBeenCalledWith(
        'spki',
        expect.any(Uint8Array),
        { name: 'Ed25519' },
        false,
        ['verify']
      );
      expect(result).toBe(mockKey);
    });

    it('should throw error for unsupported algorithm', async () => {
      const keyData = 'QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUE=';
      
      await expect(CryptoUtils.importPublicKey(keyData, 'UNSUPPORTED')).rejects.toThrow(
        'Unsupported algorithm: UNSUPPORTED'
      );
    });
  });

  describe('deriveSharedSecret', () => {
    it('should derive shared secret from X25519 key pair', async () => {
      const mockPrivateKey = { type: 'private' };
      const mockPublicKey = { type: 'public' };
      const mockSecret = new ArrayBuffer(32);
      
      mockCrypto.subtle.deriveBits.mockResolvedValue(mockSecret);
      
      const result = await CryptoUtils.deriveSharedSecret(mockPrivateKey as any, mockPublicKey as any);
      
      expect(mockCrypto.subtle.deriveBits).toHaveBeenCalledWith(
        {
          name: 'X25519',
          public: mockPublicKey,
        },
        mockPrivateKey,
        256
      );
      expect(result).toBe(mockSecret);
    });
  });

  describe('encryptMessage', () => {
    it('should encrypt message content', async () => {
      const content = 'Hello, World!';
      const mockKey = { type: 'secret' };
      const mockEncrypted = new ArrayBuffer(16);
      const mockIv = new Uint8Array(12);
      mockIv.fill(1);
      
      mockCrypto.getRandomValues.mockReturnValue(mockIv);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncrypted);
      
      const result = await CryptoUtils.encryptMessage(content, mockKey as any);
      
      expect(mockCrypto.getRandomValues).toHaveBeenCalledWith(expect.any(Uint8Array));
      expect(mockCrypto.subtle.encrypt).toHaveBeenCalledWith(
        {
          name: 'AES-GCM',
          iv: mockIv,
        },
        mockKey,
        expect.any(Uint8Array)
      );
      expect(result).toHaveProperty('encryptedData');
      expect(result).toHaveProperty('iv');
    });
  });

  describe('decryptMessage', () => {
    it('should decrypt message content', async () => {
      const encryptedData = 'QUFBQUFBQUFBQUFBQUFBQUE=';
      const iv = 'AQEBAQEBAQEBAQEBAQ==';
      const mockKey = { type: 'secret' };
      const mockDecrypted = new TextEncoder().encode('Hello, World!');
      
      mockCrypto.subtle.decrypt.mockResolvedValue(mockDecrypted.buffer);
      
      const result = await CryptoUtils.decryptMessage(encryptedData, iv, mockKey as any);
      
      expect(mockCrypto.subtle.decrypt).toHaveBeenCalledWith(
        {
          name: 'AES-GCM',
          iv: expect.any(Uint8Array),
        },
        mockKey,
        expect.any(Uint8Array)
      );
      expect(result).toBe('Hello, World!');
    });
  });

  describe('signData', () => {
    it('should sign data with Ed25519', async () => {
      const data = 'Hello, World!';
      const mockPrivateKey = { type: 'private' };
      const mockSignature = new ArrayBuffer(64);
      
      mockCrypto.subtle.sign.mockResolvedValue(mockSignature);
      
      const result = await CryptoUtils.signData(data, mockPrivateKey as any);
      
      expect(mockCrypto.subtle.sign).toHaveBeenCalledWith(
        'Ed25519',
        mockPrivateKey,
        expect.any(Uint8Array)
      );
      expect(typeof result).toBe('string');
    });
  });

  describe('verifySignature', () => {
    it('should verify signature with Ed25519', async () => {
      const data = 'Hello, World!';
      const signature = 'QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUE=';
      const mockPublicKey = { type: 'public' };
      
      mockCrypto.subtle.verify.mockResolvedValue(true);
      
      const result = await CryptoUtils.verifySignature(data, signature, mockPublicKey as any);
      
      expect(mockCrypto.subtle.verify).toHaveBeenCalledWith(
        'Ed25519',
        mockPublicKey,
        expect.any(Uint8Array),
        expect.any(Uint8Array)
      );
      expect(result).toBe(true);
    });
  });

  describe('generateRandomBytes', () => {
    it('should generate random bytes', () => {
      const mockBytes = new Uint8Array(32);
      mockBytes.fill(42);
      
      mockCrypto.getRandomValues.mockReturnValue(mockBytes);
      
      const result = CryptoUtils.generateRandomBytes(32);
      
      expect(mockCrypto.getRandomValues).toHaveBeenCalledWith(expect.any(Uint8Array));
      expect(result).toBe(mockBytes);
    });
  });

  describe('hash', () => {
    it('should hash data with SHA-256', async () => {
      const data = 'Hello, World!';
      const mockHash = new ArrayBuffer(32);
      
      mockCrypto.subtle.digest.mockResolvedValue(mockHash);
      
      const result = await CryptoUtils.hash(data);
      
      expect(mockCrypto.subtle.digest).toHaveBeenCalledWith(
        'SHA-256',
        expect.any(Uint8Array)
      );
      expect(typeof result).toBe('string');
    });
  });

  describe('constantTimeEqual', () => {
    it('should return true for equal arrays', () => {
      const a = new Uint8Array([1, 2, 3, 4]);
      const b = new Uint8Array([1, 2, 3, 4]);
      
      const result = CryptoUtils.constantTimeEqual(a, b);
      
      expect(result).toBe(true);
    });

    it('should return false for different arrays', () => {
      const a = new Uint8Array([1, 2, 3, 4]);
      const b = new Uint8Array([1, 2, 3, 5]);
      
      const result = CryptoUtils.constantTimeEqual(a, b);
      
      expect(result).toBe(false);
    });

    it('should return false for arrays of different lengths', () => {
      const a = new Uint8Array([1, 2, 3]);
      const b = new Uint8Array([1, 2, 3, 4]);
      
      const result = CryptoUtils.constantTimeEqual(a, b);
      
      expect(result).toBe(false);
    });
  });
});
