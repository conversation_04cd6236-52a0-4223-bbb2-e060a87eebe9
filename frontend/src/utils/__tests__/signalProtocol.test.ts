// frontend/src/utils/__tests__/signalProtocol.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SignalProtocol, type KeyBundle } from '../signalProtocol';
import { CryptoUtils } from '../crypto';

// Mock CryptoUtils
vi.mock('../crypto', () => ({
  CryptoUtils: {
    generateKeyPair: vi.fn(),
    generateSigningKeyPair: vi.fn(),
    exportPublicKey: vi.fn(),
    exportPrivateKey: vi.fn(),
    importPublicKey: vi.fn(),
    deriveSharedSecret: vi.fn(),
    deriveEncryptionKey: vi.fn(),
    encryptMessage: vi.fn(),
    decryptMessage: vi.fn(),
    signData: vi.fn(),
    generateRandomBytes: vi.fn(),
    combineSharedSecrets: vi.fn(),
  },
}));

describe('SignalProtocol', () => {
  let signalProtocol: SignalProtocol;
  let mockKeyPair: CryptoKeyPair;
  let mockSigningKeyPair: CryptoKeyPair;

  beforeEach(() => {
    signalProtocol = new SignalProtocol();
    
    mockKeyPair = {
      publicKey: { type: 'public' } as CryptoKey,
      privateKey: { type: 'private' } as CryptoKey,
    };
    
    mockSigningKeyPair = {
      publicKey: { type: 'public' } as CryptoKey,
      privateKey: { type: 'private' } as CryptoKey,
    };

    // Reset all mocks
    vi.clearAllMocks();
  });

  describe('initializeKeys', () => {
    it('should initialize all key pairs', async () => {
      (CryptoUtils.generateKeyPair as any).mockResolvedValue(mockKeyPair);
      (CryptoUtils.generateSigningKeyPair as any).mockResolvedValue(mockSigningKeyPair);

      await signalProtocol.initializeKeys();

      // Should generate identity key, signing key, signed pre-key, and 100 one-time pre-keys
      expect(CryptoUtils.generateKeyPair).toHaveBeenCalledTimes(102); // 1 identity + 1 signed pre-key + 100 one-time
      expect(CryptoUtils.generateSigningKeyPair).toHaveBeenCalledTimes(1);
    });
  });

  describe('getKeyBundle', () => {
    beforeEach(async () => {
      (CryptoUtils.generateKeyPair as any).mockResolvedValue(mockKeyPair);
      (CryptoUtils.generateSigningKeyPair as any).mockResolvedValue(mockSigningKeyPair);
      (CryptoUtils.exportPublicKey as any).mockResolvedValue('mock-public-key');
      (CryptoUtils.signData as any).mockResolvedValue('mock-signature');

      await signalProtocol.initializeKeys();
    });

    it('should return a valid key bundle', async () => {
      const keyBundle = await signalProtocol.getKeyBundle();

      expect(keyBundle).toHaveProperty('identityKey');
      expect(keyBundle).toHaveProperty('signedPreKey');
      expect(keyBundle.signedPreKey).toHaveProperty('id');
      expect(keyBundle.signedPreKey).toHaveProperty('publicKey');
      expect(keyBundle.signedPreKey).toHaveProperty('signature');
      expect(keyBundle).toHaveProperty('oneTimePreKey');
    });

    it('should throw error if keys not initialized', async () => {
      const uninitializedProtocol = new SignalProtocol();
      
      await expect(uninitializedProtocol.getKeyBundle()).rejects.toThrow('Keys not initialized');
    });
  });

  describe('initializeSession', () => {
    const mockRemoteKeyBundle: KeyBundle = {
      identityKey: 'remote-identity-key',
      signedPreKey: {
        id: 1,
        publicKey: 'remote-signed-prekey',
        signature: 'remote-signature',
      },
      oneTimePreKey: {
        id: 1,
        publicKey: 'remote-onetime-prekey',
      },
    };

    beforeEach(async () => {
      (CryptoUtils.generateKeyPair as any).mockResolvedValue(mockKeyPair);
      (CryptoUtils.generateSigningKeyPair as any).mockResolvedValue(mockSigningKeyPair);
      (CryptoUtils.importPublicKey as any).mockResolvedValue({ type: 'public' });
      (CryptoUtils.deriveSharedSecret as any).mockResolvedValue(new ArrayBuffer(32));
      (CryptoUtils.combineSharedSecrets as any).mockResolvedValue(new ArrayBuffer(32));
      (CryptoUtils.generateRandomBytes as any).mockReturnValue(new Uint8Array(32));
      (CryptoUtils.deriveEncryptionKey as any).mockResolvedValue({ type: 'secret' });
      (CryptoUtils.exportPrivateKey as any).mockResolvedValue('mock-private-key');
      (CryptoUtils.exportPublicKey as any).mockResolvedValue('mock-public-key');

      await signalProtocol.initializeKeys();
    });

    it('should initialize session with remote key bundle', async () => {
      const sessionState = await signalProtocol.initializeSession('conversation-1', mockRemoteKeyBundle);

      expect(sessionState).toHaveProperty('rootKey');
      expect(sessionState).toHaveProperty('messageNumberSend', 0);
      expect(sessionState).toHaveProperty('messageNumberReceive', 0);
      expect(sessionState).toHaveProperty('previousChainLength', 0);
      expect(sessionState).toHaveProperty('ratchetKeyPair');
    });

    it('should perform Triple Diffie-Hellman key exchange', async () => {
      await signalProtocol.initializeSession('conversation-1', mockRemoteKeyBundle);

      // Should derive 4 shared secrets (3DH + one-time pre-key)
      expect(CryptoUtils.deriveSharedSecret).toHaveBeenCalledTimes(4);
      expect(CryptoUtils.combineSharedSecrets).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.any(ArrayBuffer),
          expect.any(ArrayBuffer),
          expect.any(ArrayBuffer),
          expect.any(ArrayBuffer),
        ])
      );
    });

    it('should work without one-time pre-key', async () => {
      const keyBundleWithoutOneTime = {
        ...mockRemoteKeyBundle,
        oneTimePreKey: undefined,
      };

      const sessionState = await signalProtocol.initializeSession('conversation-1', keyBundleWithoutOneTime);

      expect(sessionState).toHaveProperty('rootKey');
      // Should derive 3 shared secrets (3DH only)
      expect(CryptoUtils.deriveSharedSecret).toHaveBeenCalledTimes(3);
    });
  });

  describe('encryptMessage', () => {
    beforeEach(async () => {
      (CryptoUtils.generateKeyPair as any).mockResolvedValue(mockKeyPair);
      (CryptoUtils.generateSigningKeyPair as any).mockResolvedValue(mockSigningKeyPair);
      (CryptoUtils.deriveEncryptionKey as any).mockResolvedValue({ type: 'secret' });
      (CryptoUtils.encryptMessage as any).mockResolvedValue({
        encryptedData: 'encrypted-content',
        iv: 'mock-iv',
      });

      await signalProtocol.initializeKeys();
      
      // Mock session state
      signalProtocol.updateSessionState('conversation-1', {
        rootKey: 'mock-root-key',
        messageNumberSend: 0,
        messageNumberReceive: 0,
        previousChainLength: 0,
        ratchetKeyPair: {
          private: 'mock-private-key',
          public: 'mock-public-key',
        },
      });
    });

    it('should encrypt message and update session state', async () => {
      const encryptedMessage = await signalProtocol.encryptMessage('conversation-1', 'Hello, World!');

      expect(encryptedMessage).toHaveProperty('encryptedContent', 'encrypted-content');
      expect(encryptedMessage).toHaveProperty('iv', 'mock-iv');
      expect(encryptedMessage).toHaveProperty('messageNumber', 0);
      expect(encryptedMessage).toHaveProperty('ratchetKey', 'mock-public-key');

      // Should increment message number
      const sessionState = signalProtocol.getSessionState('conversation-1');
      expect(sessionState?.messageNumberSend).toBe(1);
    });

    it('should throw error if session not found', async () => {
      await expect(signalProtocol.encryptMessage('nonexistent-conversation', 'Hello')).rejects.toThrow(
        'Session not found for conversation'
      );
    });
  });

  describe('decryptMessage', () => {
    beforeEach(async () => {
      (CryptoUtils.generateKeyPair as any).mockResolvedValue(mockKeyPair);
      (CryptoUtils.generateSigningKeyPair as any).mockResolvedValue(mockSigningKeyPair);
      (CryptoUtils.deriveEncryptionKey as any).mockResolvedValue({ type: 'secret' });
      (CryptoUtils.decryptMessage as any).mockResolvedValue('Hello, World!');

      await signalProtocol.initializeKeys();
      
      // Mock session state
      signalProtocol.updateSessionState('conversation-1', {
        rootKey: 'mock-root-key',
        messageNumberSend: 0,
        messageNumberReceive: 0,
        previousChainLength: 0,
      });
    });

    it('should decrypt message and update session state', async () => {
      const encryptedMessage = {
        encryptedContent: 'encrypted-content',
        iv: 'mock-iv',
        messageNumber: 0,
        ratchetKey: 'mock-ratchet-key',
        previousChainLength: 0,
      };

      const decryptedContent = await signalProtocol.decryptMessage('conversation-1', encryptedMessage);

      expect(decryptedContent).toBe('Hello, World!');

      // Should update message number
      const sessionState = signalProtocol.getSessionState('conversation-1');
      expect(sessionState?.messageNumberReceive).toBe(1);
    });

    it('should throw error if session not found', async () => {
      const encryptedMessage = {
        encryptedContent: 'encrypted-content',
        iv: 'mock-iv',
        messageNumber: 0,
        ratchetKey: 'mock-ratchet-key',
        previousChainLength: 0,
      };

      await expect(signalProtocol.decryptMessage('nonexistent-conversation', encryptedMessage)).rejects.toThrow(
        'Session not found for conversation'
      );
    });
  });

  describe('generateOneTimePreKeys', () => {
    beforeEach(async () => {
      (CryptoUtils.generateKeyPair as any).mockResolvedValue(mockKeyPair);
      (CryptoUtils.exportPublicKey as any).mockResolvedValue('mock-public-key');
    });

    it('should generate specified number of one-time pre-keys', async () => {
      const preKeys = await signalProtocol.generateOneTimePreKeys(5, 100);

      expect(preKeys).toHaveLength(5);
      expect(preKeys[0]).toHaveProperty('id', 100);
      expect(preKeys[0]).toHaveProperty('publicKey', 'mock-public-key');
      expect(preKeys[4]).toHaveProperty('id', 104);
    });
  });

  describe('session management', () => {
    it('should store and retrieve session state', () => {
      const sessionState = {
        rootKey: 'mock-root-key',
        messageNumberSend: 5,
        messageNumberReceive: 3,
        previousChainLength: 0,
      };

      signalProtocol.updateSessionState('conversation-1', sessionState);
      const retrieved = signalProtocol.getSessionState('conversation-1');

      expect(retrieved).toEqual(sessionState);
    });

    it('should return undefined for nonexistent session', () => {
      const retrieved = signalProtocol.getSessionState('nonexistent-conversation');
      expect(retrieved).toBeUndefined();
    });

    it('should export and import session state', () => {
      const sessionState = {
        rootKey: 'mock-root-key',
        messageNumberSend: 5,
        messageNumberReceive: 3,
        previousChainLength: 0,
      };

      signalProtocol.updateSessionState('conversation-1', sessionState);
      const exported = signalProtocol.exportSessionState('conversation-1');
      
      expect(exported).toBe(JSON.stringify(sessionState));

      signalProtocol.clearSessions();
      signalProtocol.importSessionState('conversation-1', exported!);
      
      const imported = signalProtocol.getSessionState('conversation-1');
      expect(imported).toEqual(sessionState);
    });

    it('should clear all sessions', () => {
      signalProtocol.updateSessionState('conversation-1', { rootKey: 'key1', messageNumberSend: 0, messageNumberReceive: 0, previousChainLength: 0 });
      signalProtocol.updateSessionState('conversation-2', { rootKey: 'key2', messageNumberSend: 0, messageNumberReceive: 0, previousChainLength: 0 });

      signalProtocol.clearSessions();

      expect(signalProtocol.getSessionState('conversation-1')).toBeUndefined();
      expect(signalProtocol.getSessionState('conversation-2')).toBeUndefined();
    });
  });
});
