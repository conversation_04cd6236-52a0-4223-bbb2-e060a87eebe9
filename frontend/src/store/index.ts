// frontend/src/store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import messageReducer from './slices/messageSlice';
import conversationReducer from './slices/conversationSlice';
import { api } from '../services';

export const store = configureStore({
  reducer: {
    messages: messageReducer,
    conversations: conversationReducer,
    // Add the RTK Query API reducer
    [api.reducerPath]: api.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    })
    // Add the RTK Query middleware
    .concat(api.middleware),
});

// Enable listener behavior for the store
setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
