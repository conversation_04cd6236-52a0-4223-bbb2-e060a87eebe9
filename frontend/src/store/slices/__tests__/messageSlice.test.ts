// frontend/src/store/slices/__tests__/messageSlice.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { configureStore } from '@reduxjs/toolkit'
import messageReducer, {
  addMessage,
  addOptimisticMessage,
  removeOptimisticMessage,
  updateOptimisticMessage,
  setTypingUsers,
  addTypingUser,
  removeTypingUser,
  setSendingMessage,
  removeSendingMessage,
  clearMessages,
  setError,
  updateMessageStatus,
  markMessageAsFailed,
  retryMessage,
  clearMessageError,
  type MessageState,
  type Message,
  type MessageStatusType,
} from '../messageSlice'

// Mock fetch for async thunks
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('messageSlice', () => {
  let store: ReturnType<typeof configureStore>

  const mockMessage: Message = {
    id: 'msg-1',
    conversationId: 'conv-1',
    sender: {
      id: 'user-1',
      username: 'testuser',
      first_name: 'Test',
      last_name: 'User',
      profile_picture: null,
    },
    content: 'Hello world!',
    messageType: 'TEXT',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  }

  const initialState: MessageState = {
    messages: {},
    loading: false,
    error: null,
    sendingMessages: {},
    optimisticMessageMap: {},
    messageStatuses: {},
    failedMessages: {},
    typingUsers: {},
  }

  beforeEach(() => {
    store = configureStore({
      reducer: {
        messages: messageReducer,
      },
    })
    vi.clearAllMocks()
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().messages
      expect(state).toEqual(initialState)
    })
  })

  describe('addMessage action', () => {
    it('should add a message to the correct conversation', () => {
      store.dispatch(addMessage(mockMessage))
      
      const state = store.getState().messages
      expect(state.messages['conv-1']).toHaveLength(1)
      expect(state.messages['conv-1'][0]).toEqual(mockMessage)
    })

    it('should add multiple messages to the same conversation', () => {
      const message2: Message = {
        ...mockMessage,
        id: 'msg-2',
        content: 'Second message',
      }

      store.dispatch(addMessage(mockMessage))
      store.dispatch(addMessage(message2))
      
      const state = store.getState().messages
      expect(state.messages['conv-1']).toHaveLength(2)
      // Check that both messages are present by ID since objects are recreated
      const messageIds = state.messages['conv-1'].map(msg => msg.id)
      expect(messageIds).toContain(mockMessage.id)
      expect(messageIds).toContain(message2.id)
    })

    it('should not add duplicate messages', () => {
      store.dispatch(addMessage(mockMessage))
      store.dispatch(addMessage(mockMessage)) // Same message
      
      const state = store.getState().messages
      expect(state.messages['conv-1']).toHaveLength(1)
    })

    it('should sort messages by creation date', () => {
      const olderMessage: Message = {
        ...mockMessage,
        id: 'msg-older',
        createdAt: '2022-12-31T00:00:00Z',
      }

      const newerMessage: Message = {
        ...mockMessage,
        id: 'msg-newer',
        createdAt: '2023-01-02T00:00:00Z',
      }

      // Add in random order
      store.dispatch(addMessage(newerMessage))
      store.dispatch(addMessage(mockMessage))
      store.dispatch(addMessage(olderMessage))
      
      const state = store.getState().messages
      const messages = state.messages['conv-1']
      
      expect(messages[0]).toEqual(olderMessage)
      expect(messages[1]).toEqual(mockMessage)
      expect(messages[2]).toEqual(newerMessage)
    })
  })

  describe('optimistic message actions', () => {
    it('should add optimistic message', () => {
      const optimisticMessage: Message = {
        ...mockMessage,
        id: 'temp-msg-1',
      }

      store.dispatch(addOptimisticMessage({
        tempId: 'temp-msg-1',
        message: optimisticMessage,
      }))
      
      const state = store.getState().messages
      expect(state.messages['conv-1']).toHaveLength(1)
      expect(state.messages['conv-1'][0]).toEqual(optimisticMessage)
      expect(state.optimisticMessageMap['temp-msg-1']).toBe('temp-msg-1')
    })

    it('should update optimistic message with real message', () => {
      const optimisticMessage: Message = {
        ...mockMessage,
        id: 'temp-msg-1',
        content: 'Sending...',
      }

      const realMessage: Message = {
        ...mockMessage,
        id: 'msg-real',
        content: 'Sent successfully!',
      }

      // Add optimistic message
      store.dispatch(addOptimisticMessage({
        tempId: 'temp-msg-1',
        message: optimisticMessage,
      }))

      // Update with real message
      store.dispatch(updateOptimisticMessage({
        tempId: 'temp-msg-1',
        realMessage: realMessage,
      }))
      
      const state = store.getState().messages
      expect(state.messages['conv-1']).toHaveLength(1)
      expect(state.messages['conv-1'][0]).toEqual(realMessage)
      expect(state.optimisticMessageMap['temp-msg-1']).toBe('msg-real')
    })

    it('should remove optimistic message', () => {
      const optimisticMessage: Message = {
        ...mockMessage,
        id: 'temp-msg-1',
      }

      // Add optimistic message
      store.dispatch(addOptimisticMessage({
        tempId: 'temp-msg-1',
        message: optimisticMessage,
      }))

      // Remove optimistic message
      store.dispatch(removeOptimisticMessage('temp-msg-1'))
      
      const state = store.getState().messages
      expect(state.messages['conv-1']).toHaveLength(0)
      expect(state.optimisticMessageMap['temp-msg-1']).toBeUndefined()
    })
  })

  describe('typing users actions', () => {
    it('should set typing users for a conversation', () => {
      store.dispatch(setTypingUsers({
        conversationId: 'conv-1',
        userIds: ['user-2', 'user-3'],
      }))
      
      const state = store.getState().messages
      expect(state.typingUsers['conv-1']).toEqual(['user-2', 'user-3'])
    })

    it('should add typing user', () => {
      store.dispatch(addTypingUser({
        conversationId: 'conv-1',
        userId: 'user-2',
      }))
      
      const state = store.getState().messages
      expect(state.typingUsers['conv-1']).toEqual(['user-2'])
    })

    it('should not add duplicate typing user', () => {
      store.dispatch(addTypingUser({
        conversationId: 'conv-1',
        userId: 'user-2',
      }))
      store.dispatch(addTypingUser({
        conversationId: 'conv-1',
        userId: 'user-2',
      }))
      
      const state = store.getState().messages
      expect(state.typingUsers['conv-1']).toEqual(['user-2'])
    })

    it('should remove typing user', () => {
      // First add some typing users
      store.dispatch(setTypingUsers({
        conversationId: 'conv-1',
        userIds: ['user-2', 'user-3'],
      }))

      // Remove one user
      store.dispatch(removeTypingUser({
        conversationId: 'conv-1',
        userId: 'user-2',
      }))
      
      const state = store.getState().messages
      expect(state.typingUsers['conv-1']).toEqual(['user-3'])
    })

    it('should handle removing non-existent typing user', () => {
      store.dispatch(removeTypingUser({
        conversationId: 'conv-1',
        userId: 'user-2',
      }))

      const state = store.getState().messages
      // If conversation doesn't exist in typingUsers, it remains undefined
      expect(state.typingUsers['conv-1']).toBeUndefined()
    })
  })

  describe('sending message actions', () => {
    it('should set sending message', () => {
      store.dispatch(setSendingMessage('temp-msg-1'))
      
      const state = store.getState().messages
      expect(state.sendingMessages['temp-msg-1']).toBe(true)
    })

    it('should remove sending message', () => {
      // First set sending
      store.dispatch(setSendingMessage('temp-msg-1'))
      
      // Then remove
      store.dispatch(removeSendingMessage('temp-msg-1'))
      
      const state = store.getState().messages
      expect(state.sendingMessages['temp-msg-1']).toBeUndefined()
    })
  })

  // Note: Message fetching is now handled by RTK Query in messageApi
  // These tests would be in the messageApi test file
  describe('fetchMessages async thunk (deprecated)', () => {
    it.skip('should handle successful fetch', async () => {
      // This test is skipped because fetchMessages is now handled by RTK Query
    })

    it.skip('should handle fetch error', async () => {
      // Skipped - now handled by RTK Query
    })

    it.skip('should handle network error', async () => {
      // Skipped - now handled by RTK Query
    })

    it.skip('should set loading state during fetch', () => {
      // Skipped - now handled by RTK Query
    })

    it.skip('should handle pagination', async () => {
      // Skipped - now handled by RTK Query
    })
  })

  describe('edge cases', () => {
    it('should handle adding message to non-existent conversation', () => {
      store.dispatch(addMessage(mockMessage))
      
      const state = store.getState().messages
      expect(state.messages['conv-1']).toBeDefined()
      expect(state.messages['conv-1']).toHaveLength(1)
    })

    it('should handle empty message content', () => {
      const emptyMessage: Message = {
        ...mockMessage,
        content: '',
      }

      store.dispatch(addMessage(emptyMessage))
      
      const state = store.getState().messages
      expect(state.messages['conv-1'][0].content).toBe('')
    })

    it('should handle messages with same timestamp', () => {
      const message2: Message = {
        ...mockMessage,
        id: 'msg-2',
        createdAt: mockMessage.createdAt, // Same timestamp
      }

      store.dispatch(addMessage(mockMessage))
      store.dispatch(addMessage(message2))

      const state = store.getState().messages
      expect(state.messages['conv-1']).toHaveLength(2)
      // Should maintain order of addition when timestamps are equal
      expect(state.messages['conv-1'][0]).toEqual(mockMessage)
      expect(state.messages['conv-1'][1]).toEqual(message2)
    })
  })

  describe('message status actions', () => {
    describe('updateMessageStatus', () => {
      it('should update message status', () => {
        store.dispatch(updateMessageStatus({
          messageId: 'msg-1',
          status: 'DELIVERED'
        }))

        const state = store.getState().messages
        expect(state.messageStatuses['msg-1']).toBe('DELIVERED')
        expect(state.failedMessages['msg-1']).toBeUndefined()
      })

      it('should mark message as failed', () => {
        store.dispatch(updateMessageStatus({
          messageId: 'msg-1',
          status: 'FAILED'
        }))

        const state = store.getState().messages
        expect(state.messageStatuses['msg-1']).toBe('FAILED')
        expect(state.failedMessages['msg-1']).toBe(true)
      })

      it('should update optimistic message map with tempId', () => {
        store.dispatch(updateMessageStatus({
          messageId: 'msg-1',
          status: 'DELIVERED',
          tempId: 'temp-1'
        }))

        const state = store.getState().messages
        expect(state.optimisticMessageMap['temp-1']).toBe('msg-1')
      })

      it('should remove from failed messages when status is not FAILED', () => {
        // First mark as failed
        store.dispatch(updateMessageStatus({
          messageId: 'msg-1',
          status: 'FAILED'
        }))

        // Then update to delivered
        store.dispatch(updateMessageStatus({
          messageId: 'msg-1',
          status: 'DELIVERED'
        }))

        const state = store.getState().messages
        expect(state.messageStatuses['msg-1']).toBe('DELIVERED')
        expect(state.failedMessages['msg-1']).toBeUndefined()
      })
    })

    describe('markMessageAsFailed', () => {
      it('should mark message as failed with messageId', () => {
        store.dispatch(markMessageAsFailed({
          messageId: 'msg-1'
        }))

        const state = store.getState().messages
        expect(state.messageStatuses['msg-1']).toBe('FAILED')
        expect(state.failedMessages['msg-1']).toBe(true)
      })

      it('should remove from sending messages with tempId', () => {
        // First set as sending
        store.dispatch(setSendingMessage('temp-1'))

        // Then mark as failed
        store.dispatch(markMessageAsFailed({
          tempId: 'temp-1'
        }))

        const state = store.getState().messages
        expect(state.sendingMessages['temp-1']).toBeUndefined()
      })

      it('should handle both messageId and tempId', () => {
        store.dispatch(setSendingMessage('temp-1'))

        store.dispatch(markMessageAsFailed({
          messageId: 'msg-1',
          tempId: 'temp-1'
        }))

        const state = store.getState().messages
        expect(state.messageStatuses['msg-1']).toBe('FAILED')
        expect(state.failedMessages['msg-1']).toBe(true)
        expect(state.sendingMessages['temp-1']).toBeUndefined()
      })
    })

    describe('retryMessage', () => {
      it('should remove from failed messages', () => {
        // First mark as failed
        store.dispatch(markMessageAsFailed({
          messageId: 'msg-1'
        }))

        // Then retry
        store.dispatch(retryMessage({
          messageId: 'msg-1'
        }))

        const state = store.getState().messages
        expect(state.failedMessages['msg-1']).toBeUndefined()
        expect(state.messageStatuses['msg-1']).toBe('SENT')
      })

      it('should set sending status with tempId', () => {
        store.dispatch(retryMessage({
          messageId: 'msg-1',
          tempId: 'temp-1'
        }))

        const state = store.getState().messages
        expect(state.sendingMessages['temp-1']).toBe(true)
      })
    })

    describe('clearMessageError', () => {
      it('should clear message error state', () => {
        // First mark as failed
        store.dispatch(markMessageAsFailed({
          messageId: 'msg-1'
        }))

        // Then clear error
        store.dispatch(clearMessageError('msg-1'))

        const state = store.getState().messages
        expect(state.failedMessages['msg-1']).toBeUndefined()
        expect(state.messageStatuses['msg-1']).toBeUndefined()
      })
    })
  })
})
