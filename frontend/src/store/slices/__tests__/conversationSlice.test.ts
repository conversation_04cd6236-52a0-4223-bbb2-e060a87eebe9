// frontend/src/store/slices/__tests__/conversationSlice.test.ts
import { describe, it, expect, beforeEach } from 'vitest'
import { configureStore } from '@reduxjs/toolkit'
import conversationReducer, {
  setSelectedConversation,
  clearSelectedConversation,
  createDraftConversation,
  removeDraftConversation,
  clearDraftConversations,
  type ConversationState,
  type DraftConversation,
} from '../conversationSlice'

describe('conversationSlice', () => {
  let store: ReturnType<typeof configureStore>

  const mockUser = {
    id: 'user-2',
    username: 'testuser',
    first_name: 'Test',
    last_name: 'User',
    profile_picture: null,
  }

  const mockDraftConversation: DraftConversation = {
    id: 'draft-1',
    type: 'DIRECT',
    participants: [mockUser],
    isDraft: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  }

  const initialState: ConversationState = {
    conversations: [],
    draftConversations: [],
    selectedConversationId: null,
    loading: false,
    error: null,
    creating: false,
  }

  beforeEach(() => {
    store = configureStore({
      reducer: {
        conversations: conversationReducer,
      },
    })
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().conversations
      expect(state).toEqual(initialState)
    })
  })

  describe('setSelectedConversation action', () => {
    it('should set selected conversation ID', () => {
      store.dispatch(setSelectedConversation('conv-1'))
      
      const state = store.getState().conversations
      expect(state.selectedConversationId).toBe('conv-1')
    })

    it('should update selected conversation ID', () => {
      store.dispatch(setSelectedConversation('conv-1'))
      store.dispatch(setSelectedConversation('conv-2'))
      
      const state = store.getState().conversations
      expect(state.selectedConversationId).toBe('conv-2')
    })

    it('should handle null conversation ID', () => {
      store.dispatch(setSelectedConversation('conv-1'))
      store.dispatch(setSelectedConversation(null))
      
      const state = store.getState().conversations
      expect(state.selectedConversationId).toBe(null)
    })
  })

  describe('clearSelectedConversation action', () => {
    it('should clear selected conversation', () => {
      store.dispatch(setSelectedConversation('conv-1'))
      store.dispatch(clearSelectedConversation())
      
      const state = store.getState().conversations
      expect(state.selectedConversationId).toBe(null)
    })

    it('should handle clearing when no conversation is selected', () => {
      store.dispatch(clearSelectedConversation())
      
      const state = store.getState().conversations
      expect(state.selectedConversationId).toBe(null)
    })
  })

  describe('createDraftConversation action', () => {
    it('should create a direct draft conversation', () => {
      store.dispatch(createDraftConversation({
        userId: 'user-2',
        username: 'testuser',
        first_name: 'Test',
        last_name: 'User',
        profile_picture: null,
      }))

      const state = store.getState().conversations
      expect(state.draftConversations).toHaveLength(1)

      const draft = state.draftConversations[0]
      expect(draft.type).toBe('DIRECT')
      expect(draft.participants).toHaveLength(1)
      expect(draft.participants[0]).toEqual({
        id: 'user-2',
        username: 'testuser',
        first_name: 'Test',
        last_name: 'User',
        profile_picture: undefined, // null gets converted to undefined
      })
      expect(draft.isDraft).toBe(true)
      expect(draft.id).toMatch(/^draft-/)
    })

    it('should not create duplicate draft conversations', () => {
      const userData = {
        userId: 'user-2',
        username: 'testuser',
        first_name: 'Test',
        last_name: 'User',
        profile_picture: null,
      }

      store.dispatch(createDraftConversation(userData))
      store.dispatch(createDraftConversation(userData)) // Same user
      
      const state = store.getState().conversations
      expect(state.draftConversations).toHaveLength(1)
    })

    it('should create multiple draft conversations for different users', () => {
      const userData1 = {
        userId: 'user-2',
        username: 'testuser1',
        first_name: 'Test',
        last_name: 'User1',
        profile_picture: null,
      }

      const userData2 = {
        userId: 'user-3',
        username: 'testuser2',
        first_name: 'Test',
        last_name: 'User2',
        profile_picture: null,
      }

      store.dispatch(createDraftConversation(userData1))
      store.dispatch(createDraftConversation(userData2))
      
      const state = store.getState().conversations
      expect(state.draftConversations).toHaveLength(2)
      // Since drafts are added to the beginning (unshift), the order is reversed
      expect(state.draftConversations[0].participants[0].id).toBe('user-3')
      expect(state.draftConversations[1].participants[0].id).toBe('user-2')
    })

    it('should generate unique IDs for draft conversations', () => {
      const userData1 = {
        userId: 'user-2',
        username: 'testuser1',
        first_name: 'Test',
        last_name: 'User1',
        profile_picture: null,
      }

      const userData2 = {
        userId: 'user-3',
        username: 'testuser2',
        first_name: 'Test',
        last_name: 'User2',
        profile_picture: null,
      }

      store.dispatch(createDraftConversation(userData1))
      store.dispatch(createDraftConversation(userData2))
      
      const state = store.getState().conversations
      const ids = state.draftConversations.map(draft => draft.id)
      expect(ids[0]).not.toBe(ids[1])
      expect(ids[0]).toMatch(/^draft-/)
      expect(ids[1]).toMatch(/^draft-/)
    })

    it('should set timestamps for draft conversations', () => {
      const beforeTime = Date.now()

      store.dispatch(createDraftConversation({
        userId: 'user-2',
        username: 'testuser',
        first_name: 'Test',
        last_name: 'User',
        profile_picture: null,
      }))

      const afterTime = Date.now()
      const state = store.getState().conversations
      const draft = state.draftConversations[0]

      const createdAtTime = new Date(draft.createdAt).getTime()
      expect(createdAtTime).toBeGreaterThanOrEqual(beforeTime)
      expect(createdAtTime).toBeLessThanOrEqual(afterTime)
      expect(draft.updatedAt).toBe(draft.createdAt)
    })
  })

  describe('removeDraftConversation action', () => {
    it('should remove draft conversation by ID', () => {
      // First create a draft
      store.dispatch(createDraftConversation({
        userId: 'user-2',
        username: 'testuser',
        first_name: 'Test',
        last_name: 'User',
        profile_picture: null,
      }))

      const state1 = store.getState().conversations
      const draftId = state1.draftConversations[0].id

      // Then remove it
      store.dispatch(removeDraftConversation(draftId))
      
      const state2 = store.getState().conversations
      expect(state2.draftConversations).toHaveLength(0)
    })

    it('should handle removing non-existent draft', () => {
      store.dispatch(removeDraftConversation('nonexistent-id'))
      
      const state = store.getState().conversations
      expect(state.draftConversations).toHaveLength(0)
    })

    it('should remove only the specified draft', () => {
      // Create two drafts
      store.dispatch(createDraftConversation({
        userId: 'user-2',
        username: 'testuser1',
        first_name: 'Test',
        last_name: 'User1',
        profile_picture: null,
      }))

      store.dispatch(createDraftConversation({
        userId: 'user-3',
        username: 'testuser2',
        first_name: 'Test',
        last_name: 'User2',
        profile_picture: null,
      }))

      const state1 = store.getState().conversations
      const firstDraftId = state1.draftConversations[0].id

      // Remove first draft
      store.dispatch(removeDraftConversation(firstDraftId))
      
      const state2 = store.getState().conversations
      expect(state2.draftConversations).toHaveLength(1)
      // Since we removed the first draft (user-3), user-2 should remain
      expect(state2.draftConversations[0].participants[0].id).toBe('user-2')
    })
  })

  describe('clearDraftConversations action', () => {
    it('should clear all draft conversations', () => {
      // Create multiple drafts
      store.dispatch(createDraftConversation({
        userId: 'user-2',
        username: 'testuser1',
        first_name: 'Test',
        last_name: 'User1',
        profile_picture: null,
      }))

      store.dispatch(createDraftConversation({
        userId: 'user-3',
        username: 'testuser2',
        first_name: 'Test',
        last_name: 'User2',
        profile_picture: null,
      }))

      // Clear all drafts
      store.dispatch(clearDraftConversations())
      
      const state = store.getState().conversations
      expect(state.draftConversations).toHaveLength(0)
    })

    it('should handle clearing when no drafts exist', () => {
      store.dispatch(clearDraftConversations())
      
      const state = store.getState().conversations
      expect(state.draftConversations).toHaveLength(0)
    })
  })

  describe('edge cases', () => {
    it('should handle undefined profile picture', () => {
      store.dispatch(createDraftConversation({
        userId: 'user-2',
        username: 'testuser',
        first_name: 'Test',
        last_name: 'User',
        profile_picture: undefined,
      }))

      const state = store.getState().conversations
      expect(state.draftConversations[0].participants[0].profile_picture).toBeUndefined()
    })

    it('should handle empty names', () => {
      store.dispatch(createDraftConversation({
        userId: 'user-2',
        username: 'testuser',
        first_name: '',
        last_name: '',
        profile_picture: null,
      }))
      
      const state = store.getState().conversations
      const participant = state.draftConversations[0].participants[0]
      expect(participant.first_name).toBe('')
      expect(participant.last_name).toBe('')
    })

    it('should handle special characters in usernames', () => {
      store.dispatch(createDraftConversation({
        userId: 'user-2',
        username: 'test.user_123',
        first_name: 'Test',
        last_name: 'User',
        profile_picture: null,
      }))
      
      const state = store.getState().conversations
      expect(state.draftConversations[0].participants[0].username).toBe('test.user_123')
    })
  })
})
