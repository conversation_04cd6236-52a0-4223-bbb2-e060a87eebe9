// frontend/src/components/Chat/__tests__/MessageList.test.tsx
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen } from '@testing-library/react'
import MessageList from '../MessageList'
import { renderWithStore, mockMessages } from '../../../test/utils'

// Mock scrollIntoView
Object.defineProperty(HTMLElement.prototype, 'scrollIntoView', {
  configurable: true,
  value: vi.fn(),
})

// Mock the Icon component
vi.mock('../../ui/Icon', () => ({
  Icon: ({ name, size, className }: any) => (
    <div data-testid={`icon-${name}`} className={className} style={{ width: size, height: size }}>
      {name}
    </div>
  )
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  formatDistanceToNow: vi.fn(() => '2 minutes ago')
}))

describe('MessageList', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders empty state when no messages', () => {
    renderWithStore(
      <MessageList conversationId="conv-1" currentUserId="user-1" />
    )

    expect(screen.getByText('No messages yet')).toBeInTheDocument()
    expect(screen.getByText('Start the conversation by sending a message')).toBeInTheDocument()
    expect(screen.getByTestId('icon-message-circle')).toBeInTheDocument()
  })

  it('renders messages correctly', () => {
    const initialState = {
      messages: {
        messages: {
          'conv-1': mockMessages
        }
      }
    }

    renderWithStore(
      <MessageList conversationId="conv-1" currentUserId="user-1" />,
      initialState
    )

    // Check that all messages are rendered
    expect(screen.getByText('Hello there!')).toBeInTheDocument()
    expect(screen.getByText('Hi! How are you?')).toBeInTheDocument()
  })

  it('displays different styling for own messages vs others', () => {
    const initialState = {
      messages: {
        messages: {
          'conv-1': mockMessages
        }
      }
    }

    renderWithStore(
      <MessageList conversationId="conv-1" currentUserId="user-1" />,
      initialState
    )

    // Own messages should be aligned right with blue background
    const ownMessageBubble = screen.getByText('Hello there!').closest('.bg-blue-600')
    expect(ownMessageBubble).toHaveClass('bg-blue-600', 'text-white')

    // Other's messages should be aligned left with gray background
    const otherMessageBubble = screen.getByText('Hi! How are you?').closest('.bg-gray-100')
    expect(otherMessageBubble).toHaveClass('bg-gray-100', 'text-gray-900')
  })

  it('displays user avatars for other users', () => {
    const initialState = {
      messages: {
        messages: {
          'conv-1': mockMessages
        }
      }
    }

    renderWithStore(
      <MessageList conversationId="conv-1" currentUserId="user-1" />,
      initialState
    )

    // Should show initials for users without avatars
    expect(screen.getByText('JS')).toBeInTheDocument() // Jane Smith initials
  })

  it('shows typing indicators', () => {
    const initialState = {
      messages: {
        messages: {
          'conv-1': mockMessages
        },
        typingUsers: {
          'conv-1': ['user-2']
        }
      }
    }

    renderWithStore(
      <MessageList conversationId="conv-1" currentUserId="user-1" />,
      initialState
    )

    // Should show typing indicator
    expect(screen.getByText('Someone is typing...')).toBeInTheDocument()
  })

  it('shows multiple users typing', () => {
    const initialState = {
      messages: {
        messages: {
          'conv-1': mockMessages
        },
        typingUsers: {
          'conv-1': ['user-2', 'user-3']
        }
      }
    }

    renderWithStore(
      <MessageList conversationId="conv-1" currentUserId="user-1" />,
      initialState
    )

    // Should show multiple users typing
    expect(screen.getByText('2 people are typing...')).toBeInTheDocument()
  })

  it('displays message timestamps', () => {
    const initialState = {
      messages: {
        messages: {
          'conv-1': mockMessages
        }
      }
    }

    renderWithStore(
      <MessageList conversationId="conv-1" currentUserId="user-1" />,
      initialState
    )

    // Should show relative timestamps
    expect(screen.getAllByText('2 minutes ago')).toHaveLength(2)
  })
})
