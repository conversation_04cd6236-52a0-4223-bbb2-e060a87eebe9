// frontend/src/components/Chat/__tests__/UserSearch.test.tsx
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import UserSearch from '../UserSearch'
import { renderWithStore, mockSearchResults } from '../../../test/utils'

// Mock the useSearchUsersQuery hook
const mockUseSearchUsersQuery = vi.fn()

vi.mock('../../../services', () => ({
  useSearchUsersQuery: () => mockUseSearchUsersQuery()
}))

// Mock the useDebounce hook
vi.mock('../../../hooks/useDebounce', () => ({
  useDebounce: (value: string) => value
}))

// Mock useDispatch
const mockDispatch = vi.fn()
vi.mock('react-redux', async () => {
  const actual = await vi.importActual('react-redux')
  return {
    ...actual,
    useDispatch: () => mockDispatch
  }
})

// Mock UI components
vi.mock('../../ui/LoadingSpinner', () => ({
  LoadingSpinner: ({ text, centered, className }: any) => (
    <div data-testid="loading-spinner" className={className}>
      {text}
    </div>
  )
}))

vi.mock('../../ui/ApiErrorDisplay', () => ({
  ApiErrorDisplay: ({ error, onRetry, size }: any) => (
    <div data-testid="api-error-display">
      <p>Error: {error.message || 'An error occurred'}</p>
      {onRetry && <button onClick={onRetry}>Retry</button>}
    </div>
  )
}))

vi.mock('../../ui/Icon', () => ({
  Icon: ({ name, size, className }: any) => (
    <div data-testid={`icon-${name}`} className={className} style={{ width: size, height: size }}>
      {name}
    </div>
  )
}))

vi.mock('../../ui/Button', () => ({
  Button: ({ children, disabled, onClick, size, className }: any) => (
    <button 
      disabled={disabled}
      onClick={onClick}
      className={className}
      data-testid="user-action-button"
    >
      {children}
    </button>
  )
}))

describe('UserSearch', () => {
  const mockOnClose = vi.fn()
  const mockOnConversationCreated = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockDispatch.mockClear()
    mockUseSearchUsersQuery.mockReturnValue({
      data: { data: [] },
      isLoading: false,
      error: null
    })
  })

  it('renders search modal correctly', () => {
    renderWithStore(
      <UserSearch 
        onClose={mockOnClose}
        onConversationCreated={mockOnConversationCreated}
      />
    )
    
    expect(screen.getByText('Start New Conversation')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Search users by name or username...')).toBeInTheDocument()
    expect(screen.getByTestId('icon-x')).toBeInTheDocument() // close button
  })

  it('handles search input', async () => {
    const user = userEvent.setup()
    
    renderWithStore(
      <UserSearch 
        onClose={mockOnClose}
        onConversationCreated={mockOnConversationCreated}
      />
    )
    
    const searchInput = screen.getByPlaceholderText('Search users by name or username...')
    await user.type(searchInput, 'john')
    
    expect(searchInput).toHaveValue('john')
  })

  it('displays search results', () => {
    mockUseSearchUsersQuery.mockReturnValue({
      data: { data: mockSearchResults },
      isLoading: false,
      error: null
    })

    renderWithStore(
      <UserSearch 
        onClose={mockOnClose}
        onConversationCreated={mockOnConversationCreated}
      />
    )
    
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('@john_doe')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    expect(screen.getByText('@jane_smith')).toBeInTheDocument()
  })

  it('shows loading state', () => {
    mockUseSearchUsersQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null
    })

    renderWithStore(
      <UserSearch 
        onClose={mockOnClose}
        onConversationCreated={mockOnConversationCreated}
      />
    )
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('shows error state', () => {
    mockUseSearchUsersQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: { message: 'Search failed' }
    })

    renderWithStore(
      <UserSearch 
        onClose={mockOnClose}
        onConversationCreated={mockOnConversationCreated}
      />
    )
    
    expect(screen.getByTestId('api-error-display')).toBeInTheDocument()
    expect(screen.getByText('Error: Search failed')).toBeInTheDocument()
  })

  it('shows empty state when no results', () => {
    mockUseSearchUsersQuery.mockReturnValue({
      data: { data: [] },
      isLoading: false,
      error: null
    })

    renderWithStore(
      <UserSearch 
        onClose={mockOnClose}
        onConversationCreated={mockOnConversationCreated}
      />
    )
    
    // Type something to trigger search
    const searchInput = screen.getByPlaceholderText('Search users by name or username...')
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } })
    
    expect(screen.getByText('No users found matching "nonexistent"')).toBeInTheDocument()
  })

  it('shows initial state message', () => {
    renderWithStore(
      <UserSearch 
        onClose={mockOnClose}
        onConversationCreated={mockOnConversationCreated}
      />
    )
    
    expect(screen.getByText('Type at least 2 characters to search')).toBeInTheDocument()
  })

  it('creates draft conversation when user is selected', async () => {
    const user = userEvent.setup()

    mockUseSearchUsersQuery.mockReturnValue({
      data: { data: mockSearchResults },
      isLoading: false,
      error: null
    })

    renderWithStore(
      <UserSearch
        onClose={mockOnClose}
        onConversationCreated={mockOnConversationCreated}
      />
    )

    // Click on a user button
    const userButton = screen.getAllByTestId('user-action-button')[0]
    await user.click(userButton)

    // Check that createDraftConversation action was dispatched
    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'conversations/createDraftConversation',
        payload: expect.objectContaining({
          userId: 'user-1',
          username: 'john_doe',
          first_name: 'John',
          last_name: 'Doe'
        })
      })
    )
  })

  it('calls onConversationCreated when draft is created', async () => {
    const user = userEvent.setup()
    
    mockUseSearchUsersQuery.mockReturnValue({
      data: { data: mockSearchResults },
      isLoading: false,
      error: null
    })

    renderWithStore(
      <UserSearch 
        onClose={mockOnClose}
        onConversationCreated={mockOnConversationCreated}
      />
    )
    
    // Click on a user button
    const userButton = screen.getAllByTestId('user-action-button')[0]
    await user.click(userButton)
    
    expect(mockOnConversationCreated).toHaveBeenCalled()
  })

  it('closes modal when close button is clicked', async () => {
    const user = userEvent.setup()
    
    renderWithStore(
      <UserSearch 
        onClose={mockOnClose}
        onConversationCreated={mockOnConversationCreated}
      />
    )
    
    const closeButton = screen.getByTestId('icon-x').closest('button')
    await user.click(closeButton!)
    
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('displays user avatars correctly', () => {
    mockUseSearchUsersQuery.mockReturnValue({
      data: { data: mockSearchResults },
      isLoading: false,
      error: null
    })

    renderWithStore(
      <UserSearch 
        onClose={mockOnClose}
        onConversationCreated={mockOnConversationCreated}
      />
    )
    
    // User with avatar
    const janeAvatar = screen.getByAltText('Jane Smith')
    expect(janeAvatar).toHaveAttribute('src', 'https://example.com/avatar.jpg')
    
    // Users without avatars should show initials
    expect(screen.getByText('JD')).toBeInTheDocument() // John Doe
  })
})
