// frontend/src/components/ui/ApiErrorDisplay.tsx
import React from 'react';
import { Icon } from './Icon';
import { Button } from './Button';
import { useApiError } from '../../hooks/useApiError';

interface ApiErrorDisplayProps {
  error: any;
  onRetry?: () => void;
  className?: string;
  showRetry?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const ApiErrorDisplay: React.FC<ApiErrorDisplayProps> = ({
  error,
  onRetry,
  className = '',
  showRetry = true,
  size = 'md',
}) => {
  const { formatError, isNetworkError, isAuthError, isServerError } = useApiError();
  const formattedError = formatError(error);

  const getErrorIcon = () => {
    if (isNetworkError(error)) return 'wifi-off';
    if (isAuthError(error)) return 'lock';
    if (isServerError(error)) return 'server';
    return 'alert-triangle';
  };

  const getErrorTitle = () => {
    if (isNetworkError(error)) return 'Connection Error';
    if (isAuthError(error)) return 'Authentication Error';
    if (isServerError(error)) return 'Server Error';
    return 'Error';
  };

  const sizeClasses = {
    sm: {
      container: 'p-3',
      icon: 16,
      title: 'text-sm font-medium',
      message: 'text-xs',
      button: 'text-xs px-2 py-1',
    },
    md: {
      container: 'p-4',
      icon: 20,
      title: 'text-base font-medium',
      message: 'text-sm',
      button: 'text-sm px-3 py-1.5',
    },
    lg: {
      container: 'p-6',
      icon: 24,
      title: 'text-lg font-semibold',
      message: 'text-base',
      button: 'text-base px-4 py-2',
    },
  };

  const styles = sizeClasses[size];

  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg ${styles.container} ${className}`}>
      <div className="flex items-start">
        <Icon 
          name={getErrorIcon()} 
          size={styles.icon} 
          className="text-red-500 flex-shrink-0 mt-0.5" 
        />
        <div className="ml-3 flex-1">
          <h3 className={`text-red-800 ${styles.title}`}>
            {getErrorTitle()}
          </h3>
          <p className={`text-red-700 mt-1 ${styles.message}`}>
            {formattedError.message}
          </p>
          {formattedError.status && (
            <p className={`text-red-600 mt-1 ${styles.message} opacity-75`}>
              Status: {formattedError.status}
            </p>
          )}
          {showRetry && onRetry && (
            <div className="mt-3">
              <Button
                onClick={onRetry}
                variant="outline"
                size="sm"
                className={`border-red-300 text-red-700 hover:bg-red-100 ${styles.button}`}
              >
                <Icon name="refresh-cw" size={14} className="mr-1" />
                Retry
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
