// frontend/src/__tests__/encryption-integration.test.tsx
import React, { useState } from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { store } from '../store';
import { AuthProvider } from '../contexts/AuthContext';
import { EncryptionProvider } from '../contexts/EncryptionContext';
import { SocketProvider } from '../contexts/SocketContext';
import { useEncryption } from '../contexts/EncryptionContext';

// Mock the encryption API
vi.mock('../services/encryptionApi', () => ({
  useUploadKeyBundleMutation: () => [vi.fn().mockResolvedValue({ unwrap: () => Promise.resolve({ message: 'Success' }) })],
  useGetKeyBundleQuery: () => ({ data: null }),
  useUploadOneTimePreKeysMutation: () => [vi.fn().mockResolvedValue({ unwrap: () => Promise.resolve({ count: 50 }) })],
  useCreateConversationSessionMutation: () => [vi.fn().mockResolvedValue({ unwrap: () => Promise.resolve({ session_id: 'test-session' }) })],
  useUpdateConversationSessionMutation: () => [vi.fn().mockResolvedValue({ unwrap: () => Promise.resolve({ message: 'Updated' }) })],
  convertApiKeyBundleToSignalFormat: vi.fn(),
  convertSignalKeyBundleToApiFormat: vi.fn(),
}));

// Mock the auth context
const mockUser = {
  id: 'user-1',
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  profilePicture: null,
};

vi.mock('../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => ({
    user: mockUser,
    isAuthenticated: true,
    token: 'mock-token',
  }),
}));

// Mock socket.io
vi.mock('socket.io-client', () => ({
  io: vi.fn(() => ({
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    join: vi.fn(),
    disconnect: vi.fn(),
  })),
}));

// Mock Web Crypto API
const mockCrypto = {
  subtle: {
    generateKey: vi.fn().mockResolvedValue({
      publicKey: { type: 'public' },
      privateKey: { type: 'private' },
    }),
    exportKey: vi.fn().mockResolvedValue(new ArrayBuffer(32)),
    importKey: vi.fn().mockResolvedValue({ type: 'public' }),
    deriveBits: vi.fn().mockResolvedValue(new ArrayBuffer(32)),
    deriveKey: vi.fn().mockResolvedValue({ type: 'secret' }),
    encrypt: vi.fn().mockResolvedValue(new ArrayBuffer(16)),
    decrypt: vi.fn().mockResolvedValue(new TextEncoder().encode('Hello, World!')),
    sign: vi.fn().mockResolvedValue(new ArrayBuffer(64)),
    verify: vi.fn().mockResolvedValue(true),
    digest: vi.fn().mockResolvedValue(new ArrayBuffer(32)),
  },
  getRandomValues: vi.fn().mockReturnValue(new Uint8Array(32)),
};

Object.defineProperty(global, 'crypto', {
  value: mockCrypto,
  writable: true,
});

// Test component that uses encryption
const TestEncryptionComponent: React.FC = () => {
  const [message, setMessage] = useState('');
  const [encryptedMessage, setEncryptedMessage] = useState('');
  const [decryptedMessage, setDecryptedMessage] = useState('');
  
  const { isInitialized, encryptMessage, decryptMessage } = useEncryption();

  const handleEncrypt = async () => {
    try {
      const encrypted = await encryptMessage('test-conversation', message);
      setEncryptedMessage(JSON.stringify(encrypted));
    } catch (error) {
      console.error('Encryption failed:', error);
    }
  };

  const handleDecrypt = async () => {
    try {
      if (encryptedMessage) {
        const encrypted = JSON.parse(encryptedMessage);
        const decrypted = await decryptMessage('test-conversation', encrypted);
        setDecryptedMessage(decrypted);
      }
    } catch (error) {
      console.error('Decryption failed:', error);
    }
  };

  return (
    <div>
      <div data-testid="encryption-status">
        {isInitialized ? 'Encryption Ready' : 'Encryption Not Ready'}
      </div>
      
      <input
        data-testid="message-input"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        placeholder="Enter message"
      />
      
      <button data-testid="encrypt-button" onClick={handleEncrypt} disabled={!isInitialized}>
        Encrypt
      </button>
      
      <div data-testid="encrypted-message">{encryptedMessage}</div>
      
      <button data-testid="decrypt-button" onClick={handleDecrypt} disabled={!encryptedMessage}>
        Decrypt
      </button>
      
      <div data-testid="decrypted-message">{decryptedMessage}</div>
    </div>
  );
};

// Imports are already at the top of the file

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Provider store={store}>
    <BrowserRouter>
      <AuthProvider>
        <EncryptionProvider>
          <SocketProvider>
            {children}
          </SocketProvider>
        </EncryptionProvider>
      </AuthProvider>
    </BrowserRouter>
  </Provider>
);

describe('Encryption Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize encryption and encrypt/decrypt messages', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <TestEncryptionComponent />
      </TestWrapper>
    );

    // Wait for encryption to initialize
    await waitFor(() => {
      expect(screen.getByTestId('encryption-status')).toHaveTextContent('Encryption Ready');
    }, { timeout: 5000 });

    // Enter a message
    const messageInput = screen.getByTestId('message-input');
    await user.type(messageInput, 'Hello, World!');

    // Encrypt the message
    const encryptButton = screen.getByTestId('encrypt-button');
    await user.click(encryptButton);

    // Wait for encryption to complete
    await waitFor(() => {
      const encryptedMessage = screen.getByTestId('encrypted-message');
      expect(encryptedMessage).not.toHaveTextContent('');
    });

    // Decrypt the message
    const decryptButton = screen.getByTestId('decrypt-button');
    await user.click(decryptButton);

    // Wait for decryption to complete
    await waitFor(() => {
      const decryptedMessage = screen.getByTestId('decrypted-message');
      expect(decryptedMessage).toHaveTextContent('Hello, World!');
    });
  });

  it('should handle encryption errors gracefully', async () => {
    // Mock encryption to fail
    mockCrypto.subtle.encrypt.mockRejectedValueOnce(new Error('Encryption failed'));
    
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <TestEncryptionComponent />
      </TestWrapper>
    );

    // Wait for encryption to initialize
    await waitFor(() => {
      expect(screen.getByTestId('encryption-status')).toHaveTextContent('Encryption Ready');
    });

    // Enter a message
    const messageInput = screen.getByTestId('message-input');
    await user.type(messageInput, 'Hello, World!');

    // Try to encrypt the message
    const encryptButton = screen.getByTestId('encrypt-button');
    await user.click(encryptButton);

    // The encrypted message should remain empty due to error
    await waitFor(() => {
      const encryptedMessage = screen.getByTestId('encrypted-message');
      expect(encryptedMessage).toHaveTextContent('');
    });
  });

  it('should handle decryption errors gracefully', async () => {
    // Mock decryption to fail
    mockCrypto.subtle.decrypt.mockRejectedValueOnce(new Error('Decryption failed'));
    
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <TestEncryptionComponent />
      </TestWrapper>
    );

    // Wait for encryption to initialize
    await waitFor(() => {
      expect(screen.getByTestId('encryption-status')).toHaveTextContent('Encryption Ready');
    });

    // Manually set an encrypted message
    const encryptedMessage = screen.getByTestId('encrypted-message');
    // Simulate having an encrypted message
    const testEncrypted = JSON.stringify({
      encryptedContent: 'test-encrypted',
      iv: 'test-iv',
      messageNumber: 0,
      ratchetKey: 'test-key',
      previousChainLength: 0,
    });
    
    // We need to trigger the state update through the component
    // This is a limitation of this test setup - in a real scenario,
    // the encrypted message would be set by the encrypt function
  });

  it('should not allow encryption when not initialized', () => {
    // Mock encryption as not initialized
    vi.mocked(useEncryption).mockReturnValue({
      isInitialized: false,
      initializeEncryption: vi.fn(),
      encryptMessage: vi.fn(),
      decryptMessage: vi.fn(),
      initializeConversationSession: vi.fn(),
      getSessionState: vi.fn(),
      generateAndUploadPreKeys: vi.fn(),
      rotateKeys: vi.fn(),
      clearEncryption: vi.fn(),
    });

    render(
      <TestWrapper>
        <TestEncryptionComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('encryption-status')).toHaveTextContent('Encryption Not Ready');
    expect(screen.getByTestId('encrypt-button')).toBeDisabled();
  });
});
