// frontend/src/test/utils.tsx
import React from 'react'
import { render } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import conversationReducer from '../store/slices/conversationSlice'
import messageReducer from '../store/slices/messageSlice'
import { api } from '../services/api'

export const createTestStore = (initialState: any = {}) => {
  return configureStore({
    reducer: {
      conversations: conversationReducer,
      messages: messageReducer,
      [api.reducerPath]: api.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(api.middleware),
    preloadedState: {
      conversations: {
        conversations: [],
        draftConversations: [],
        selectedConversationId: null,
        loading: false,
        error: null,
        creating: false,
        ...(initialState.conversations || {})
      },
      messages: {
        messages: {},
        loading: false,
        error: null,
        sendingMessages: {},
        optimisticMessageMap: {},
        typingUsers: {},
        ...(initialState.messages || {})
      },
      ...initialState
    }
  })
}

export const renderWithStore = (component: React.ReactElement, initialState = {}) => {
  const store = createTestStore(initialState)
  return {
    ...render(
      <Provider store={store}>
        {component}
      </Provider>
    ),
    store
  }
}

export const mockConversationsData = {
  results: [
    {
      id: 'conv-1',
      type: 'DIRECT',
      name: null,
      participants: [
        {
          id: 'user-1',
          username: 'john_doe',
          first_name: 'John',
          last_name: 'Doe',
          profile_picture: null
        },
        {
          id: 'user-2',
          username: 'jane_smith',
          first_name: 'Jane',
          last_name: 'Smith',
          profile_picture: null
        }
      ],
      lastMessage: {
        id: 'msg-1',
        content: 'Hello there!',
        sender: { username: 'jane_smith' },
        createdAt: new Date().toISOString()
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'conv-2',
      type: 'GROUP',
      name: 'Test Group',
      participants: [
        {
          id: 'user-1',
          username: 'john_doe',
          first_name: 'John',
          last_name: 'Doe',
          profile_picture: null
        },
        {
          id: 'user-2',
          username: 'jane_smith',
          first_name: 'Jane',
          last_name: 'Smith',
          profile_picture: null
        },
        {
          id: 'user-3',
          username: 'bob_wilson',
          first_name: 'Bob',
          last_name: 'Wilson',
          profile_picture: null
        }
      ],
      lastMessage: {
        id: 'msg-2',
        content: 'Group message',
        sender: { username: 'bob_wilson' },
        createdAt: new Date().toISOString()
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  count: 2,
  next: null,
  previous: null
}

export const mockMessages = [
  {
    id: 'msg-1',
    conversationId: 'conv-1',
    sender: {
      id: 'user-1',
      username: 'john_doe',
      first_name: 'John',
      last_name: 'Doe',
      profile_picture: null
    },
    content: 'Hello there!',
    messageType: 'TEXT' as const,
    createdAt: new Date(Date.now() - 120000).toISOString(),
    updatedAt: new Date(Date.now() - 120000).toISOString()
  },
  {
    id: 'msg-2',
    conversationId: 'conv-1',
    sender: {
      id: 'user-2',
      username: 'jane_smith',
      first_name: 'Jane',
      last_name: 'Smith',
      profile_picture: null
    },
    content: 'Hi! How are you?',
    messageType: 'TEXT' as const,
    createdAt: new Date(Date.now() - 60000).toISOString(),
    updatedAt: new Date(Date.now() - 60000).toISOString()
  }
]

export const mockSearchResults = [
  {
    id: 'user-1',
    username: 'john_doe',
    first_name: 'John',
    last_name: 'Doe',
    full_name: 'John Doe',
    profile_picture: null
  },
  {
    id: 'user-2',
    username: 'jane_smith',
    first_name: 'Jane',
    last_name: 'Smith',
    full_name: 'Jane Smith',
    profile_picture: 'https://example.com/avatar.jpg'
  }
]
