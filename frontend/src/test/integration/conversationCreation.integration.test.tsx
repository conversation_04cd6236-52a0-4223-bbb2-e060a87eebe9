// frontend/src/test/integration/conversationCreation.integration.test.tsx
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { api } from '../../services/api';
import { AuthProvider } from '../../contexts/AuthContext';
import { SocketProvider } from '../../contexts/SocketContext';
import { EncryptionProvider } from '../../contexts/EncryptionContext';
import conversationReducer from '../../store/slices/conversationSlice';
import messageReducer from '../../store/slices/messageSlice';
import { UserSearch } from '../../components/Chat/UserSearch';

// Mock socket instance
const mockSocket = {
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  connect: vi.fn(),
  disconnect: vi.fn(),
  join: vi.fn(),
};

// Mock the socket.io-client
vi.mock('socket.io-client', () => ({
  io: vi.fn(() => mockSocket),
}));

// Mock the encryption context
vi.mock('../../contexts/EncryptionContext', () => ({
  EncryptionProvider: ({ children }: { children: React.ReactNode }) => children,
  useEncryption: () => ({
    isInitialized: true,
    initializeEncryption: vi.fn(),
    encryptMessage: vi.fn().mockResolvedValue({
      encryptedContent: 'encrypted',
      iv: 'iv',
      messageNumber: 1,
      ratchetKey: 'key',
      previousChainLength: 0,
    }),
    decryptMessage: vi.fn().mockResolvedValue('decrypted content'),
    initializeConversationSession: vi.fn().mockResolvedValue(undefined),
    getSessionState: vi.fn(),
    generateAndUploadPreKeys: vi.fn(),
    rotateKeys: vi.fn(),
    clearEncryption: vi.fn(),
  }),
}));

// Mock the auth context
vi.mock('../../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => ({
    user: {
      id: 'user-1',
      username: 'testuser',
      firstName: 'Test',
      lastName: 'User',
      profilePicture: null,
    },
    token: 'test-token',
    isAuthenticated: true,
    loading: false,
    login: vi.fn(),
    register: vi.fn(),
    logout: vi.fn(),
  }),
}));

// Mock API responses
const mockCreateConversation = vi.fn();
const mockSearchUsers = vi.fn();

vi.mock('../../services/conversationApi', () => ({
  useCreateConversationMutation: () => [mockCreateConversation],
  conversationApi: {
    endpoints: {
      createConversation: {
        initiate: mockCreateConversation,
      },
    },
  },
}));

vi.mock('../../services/userApi', () => ({
  useSearchUsersQuery: () => ({
    data: {
      results: [
        {
          id: 'user-2',
          username: 'otheruser',
          first_name: 'Other',
          last_name: 'User',
          profile_picture: null,
        },
      ],
    },
    isLoading: false,
    error: null,
  }),
}));

describe('Conversation Creation Integration', () => {
  let store: any;
  let mockSocket: any;

  beforeEach(() => {
    // Create a test store
    store = configureStore({
      reducer: {
        conversations: conversationReducer,
        messages: messageReducer,
        [api.reducerPath]: api.reducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware().concat(api.middleware),
    });

    // Mock socket
    mockSocket = {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
      connect: vi.fn(),
      disconnect: vi.fn(),
      join: vi.fn(),
    };

    // Reset mocks
    mockCreateConversation.mockReset();
    mockSearchUsers.mockReset();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <Provider store={store}>
      <AuthProvider>
        <EncryptionProvider>
          <SocketProvider>
            {children}
          </SocketProvider>
        </EncryptionProvider>
      </AuthProvider>
    </Provider>
  );

  it('should create draft conversation when user is selected from search', async () => {
    const user = userEvent.setup();
    const onConversationCreated = vi.fn();
    const onClose = vi.fn();

    render(
      <TestWrapper>
        <UserSearch
          isOpen={true}
          onClose={onClose}
          onConversationCreated={onConversationCreated}
        />
      </TestWrapper>
    );

    // Wait for search results to load
    await waitFor(() => {
      expect(screen.getByText('Other User')).toBeInTheDocument();
    });

    // Click on a user to create conversation
    const userButton = screen.getByText('Other User');
    await user.click(userButton);

    // Verify draft conversation was created
    await waitFor(() => {
      expect(onConversationCreated).toHaveBeenCalled();
      expect(onClose).toHaveBeenCalled();
    });

    // Check that draft conversation is in the store
    const state = store.getState();
    expect(state.conversations.draftConversations).toHaveLength(1);
    expect(state.conversations.draftConversations[0].participants[0].id).toBe('user-2');
  });

  it('should handle socket-based conversation creation correctly', async () => {
    const TestComponent = () => {
      const { createConversation } = useSocket();

      React.useEffect(() => {
        const testConversationCreation = async () => {
          try {
            const conversation = await createConversation(['user-2'], 'DIRECT');
            console.log('Conversation created:', conversation);
          } catch (error) {
            console.error('Conversation creation failed:', error);
          }
        };

        testConversationCreation();
      }, [createConversation]);

      return <div>Test Component</div>;
    };

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // Verify socket emit was called
    expect(mockSocket.emit).toHaveBeenCalledWith('create_conversation', {
      participant_ids: ['user-2'],
      type: 'DIRECT',
      name: undefined
    });

    // Verify event listeners were set up
    expect(mockSocket.on).toHaveBeenCalledWith('conversation_created', expect.any(Function));
    expect(mockSocket.on).toHaveBeenCalledWith('conversation_error', expect.any(Function));
  });

  it('should handle complete message sending flow with draft conversation', async () => {
    const TestComponent = () => {
      const { sendMessage } = useSocket();

      React.useEffect(() => {
        const testMessageSending = async () => {
          try {
            // Simulate sending message to draft conversation
            const messageId = await sendMessage('draft-user-2-1234567890', 'Hello!', 'TEXT');
            console.log('Message sent with ID:', messageId);
          } catch (error) {
            console.error('Message sending failed:', error);
          }
        };

        testMessageSending();
      }, [sendMessage]);

      return <div>Test Component</div>;
    };

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // Verify conversation creation was triggered for draft
    expect(mockSocket.emit).toHaveBeenCalledWith('create_conversation', {
      participant_ids: ['user-2'],
      type: 'DIRECT',
      name: undefined
    });

    // Verify message sending would be triggered after conversation creation
    // (This would happen after the conversation_created event is received)
  });

  it('should handle socket errors gracefully', async () => {
    const TestComponent = () => {
      const { createConversation } = useSocket();

      React.useEffect(() => {
        const testErrorHandling = async () => {
          try {
            await createConversation(['invalid-user'], 'DIRECT');
          } catch (error) {
            expect(error).toBeInstanceOf(Error);
            console.log('Error handled correctly:', error.message);
          }
        };

        testErrorHandling();
      }, [createConversation]);

      return <div>Test Component</div>;
    };

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // Simulate error event
    const errorHandler = mockSocket.on.mock.calls.find(
      call => call[0] === 'conversation_error'
    )?.[1];

    if (errorHandler) {
      errorHandler({ message: 'Invalid participant ID' });
    }
  });
});
