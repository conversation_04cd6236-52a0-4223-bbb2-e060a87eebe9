// frontend/src/test/mocks/handlers.ts
import { http, HttpResponse } from 'msw'
import { mockUsers, mockConversations, mockMessages } from './data'

export const handlers = [
  // Auth endpoints
  http.post('/api/auth/login/', () => {
    return HttpResponse.json({
      success: true,
      data: {
        user: mockUsers[0],
        tokens: {
          access: 'mock-access-token',
          refresh: 'mock-refresh-token'
        }
      }
    })
  }),

  http.post('/api/auth/register/', () => {
    return HttpResponse.json({
      success: true,
      data: {
        user: mockUsers[0],
        tokens: {
          access: 'mock-access-token',
          refresh: 'mock-refresh-token'
        }
      }
    })
  }),

  http.get('/api/auth/profile/', () => {
    return HttpResponse.json({
      success: true,
      data: mockUsers[0]
    })
  }),

  http.post('/api/auth/logout/', () => {
    return HttpResponse.json({
      success: true,
      data: {}
    })
  }),

  // Messaging endpoints
  http.get('/api/messaging/conversations/', () => {
    return HttpResponse.json({
      success: true,
      data: {
        results: mockConversations,
        count: mockConversations.length,
        next: null,
        previous: null
      }
    })
  }),

  http.post('/api/messaging/conversations/create/', () => {
    return HttpResponse.json({
      success: true,
      data: mockConversations[0]
    })
  }),

  http.get('/api/messaging/conversations/:conversationId/messages/', ({ params }) => {
    const conversationId = params.conversationId as string
    const conversationMessages = mockMessages.filter(
      msg => msg.conversationId === conversationId
    )
    // Return direct pagination format (not wrapped in ApiResponse)
    return HttpResponse.json({
      results: conversationMessages,
      count: conversationMessages.length,
      next: null,
      previous: null
    })
  }),

  http.post('/api/messaging/conversations/:conversationId/send/', ({ params }) => {
    const conversationId = params.conversationId as string
    return HttpResponse.json({
      success: true,
      data: {
        id: 'new-message-id',
        conversationId,
        sender: mockUsers[0],
        content: 'Test message',
        messageType: 'TEXT',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    })
  }),

  http.get('/api/messaging/users/search/', ({ request }) => {
    const url = new URL(request.url)
    const query = url.searchParams.get('q')

    if (!query) {
      return HttpResponse.json({
        success: true,
        data: []
      })
    }

    const filteredUsers = mockUsers.filter(user =>
      user.username.toLowerCase().includes(query.toLowerCase()) ||
      user.first_name.toLowerCase().includes(query.toLowerCase()) ||
      user.last_name.toLowerCase().includes(query.toLowerCase())
    )

    return HttpResponse.json({
      success: true,
      data: filteredUsers
    })
  }),

  // User profile endpoints
  http.get('/api/messaging/users/:userId/', ({ params }) => {
    const userId = params.userId as string
    const user = mockUsers.find(u => u.id === userId)

    if (!user) {
      return new HttpResponse(null, {
        status: 404,
        statusText: 'Not Found'
      })
    }

    return HttpResponse.json({
      success: true,
      data: user
    })
  }),

  http.put('/api/auth/profile/', ({ request }) => {
    return HttpResponse.json({
      success: true,
      data: mockUsers[0]
    })
  }),

  // Test endpoints for API tests
  http.get('/api/test', () => {
    return HttpResponse.json({
      success: true,
      data: { message: 'Test successful' }
    })
  }),

  http.post('/api/test', () => {
    return HttpResponse.json({
      success: true,
      data: { message: 'Test mutation successful' }
    })
  }),

  http.post('/api/upload', () => {
    return HttpResponse.json({
      success: true,
      data: { url: 'http://example.com/file.jpg' }
    })
  })
]
