#!/usr/bin/env python3
import requests
import json

# Complete system test
BASE_URL = "http://localhost:8000"

def login_user(email, password):
    """Login and return token and user"""
    login_data = {"email": email, "password": password}
    response = requests.post(f"{BASE_URL}/api/auth/login/", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            return data['data']['tokens']['access'], data['data']['user']
    return None, None

def test_complete_system():
    print("🚀 === COMPLETE PHASE 2 SYSTEM TEST ===\n")
    
    # Test 1: Authentication
    print("1️⃣ Testing Authentication...")
    alice_token, alice_user = login_user("<EMAIL>", "password123")
    charlie_token, charlie_user = login_user("<EMAIL>", "password123")
    
    if not alice_token or not charlie_token:
        print("❌ Authentication failed")
        return
    print("✅ Authentication working\n")
    
    # Test 2: User Search
    print("2️⃣ Testing User Search...")
    headers = {"Authorization": f"Bearer {alice_token}"}
    response = requests.get(f"{BASE_URL}/api/messaging/users/search/?q=charlie", headers=headers)
    
    if response.status_code == 200 and response.json().get('success'):
        users = response.json()['data']
        if len(users) > 0:
            print("✅ User search working")
            print(f"   Found: {users[0]['full_name']} (@{users[0]['username']})")
        else:
            print("❌ User search returned no results")
            return
    else:
        print("❌ User search failed")
        return
    print()
    
    # Test 3: Conversation Creation
    print("3️⃣ Testing Conversation Creation...")
    conversation_data = {
        "type": "DIRECT",
        "participant_ids": [charlie_user['id']]
    }
    response = requests.post(f"{BASE_URL}/api/messaging/conversations/create/", 
                           json=conversation_data, headers=headers)
    
    if response.status_code in [200, 201]:
        conversation = response.json()
        conversation_id = conversation['id']
        print("✅ Conversation creation working")
        print(f"   Created conversation: {conversation_id}")
    else:
        print("❌ Conversation creation failed")
        print(f"   Error: {response.text}")
        return
    print()
    
    # Test 4: Message Sending
    print("4️⃣ Testing Message Sending...")
    message_data = {
        "content": "Hello Charlie! This is a test message from the complete system test.",
        "message_type": "TEXT"
    }
    response = requests.post(f"{BASE_URL}/api/messaging/conversations/{conversation_id}/send/", 
                           json=message_data, headers=headers)
    
    if response.status_code in [200, 201]:
        message = response.json()
        print("✅ Message sending working")
        print(f"   Sent message: {message['content'][:50]}...")
    else:
        print("❌ Message sending failed")
        print(f"   Error: {response.text}")
        return
    print()
    
    # Test 5: Message Retrieval
    print("5️⃣ Testing Message Retrieval...")
    response = requests.get(f"{BASE_URL}/api/messaging/conversations/{conversation_id}/messages/", 
                          headers=headers)
    
    if response.status_code == 200:
        messages = response.json()
        if messages.get('count', 0) > 0:
            print("✅ Message retrieval working")
            print(f"   Retrieved {messages['count']} messages")
        else:
            print("❌ No messages found")
            return
    else:
        print("❌ Message retrieval failed")
        return
    print()
    
    # Test 6: Conversation List
    print("6️⃣ Testing Conversation List...")
    response = requests.get(f"{BASE_URL}/api/messaging/conversations/", headers=headers)
    
    if response.status_code == 200:
        conversations = response.json()
        if conversations.get('count', 0) > 0:
            print("✅ Conversation list working")
            print(f"   Found {conversations['count']} conversations")
        else:
            print("❌ No conversations found")
            return
    else:
        print("❌ Conversation list failed")
        return
    print()
    
    print("🎉 === ALL TESTS PASSED! ===")
    print("\n📋 System Status:")
    print("✅ JWT Authentication: Working")
    print("✅ User Search: Working") 
    print("✅ Conversation Creation: Working")
    print("✅ Message Sending: Working")
    print("✅ Message Retrieval: Working")
    print("✅ Conversation List: Working")
    print("✅ Socket Server: Connected (check logs)")
    
    print("\n🌐 Frontend Testing Instructions:")
    print("1. Open: http://localhost:3000")
    print("2. Login as: <EMAIL> / password123")
    print("3. Click 'New Chat' button")
    print("4. Search for 'charlie' and start a conversation")
    print("5. Send real-time messages!")
    print("6. Open another browser <NAME_EMAIL> to test real-time messaging")

if __name__ == "__main__":
    test_complete_system()
