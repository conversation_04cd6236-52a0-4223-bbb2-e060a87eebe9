// E2E Console Test for Chat Application
// Run this in the browser console to test the complete messaging flow

class E2ETest {
  constructor() {
    this.testResults = [];
    this.currentStep = 0;
    this.totalSteps = 0;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    console.log(logMessage);
    this.testResults.push({ timestamp, type, message });
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async waitForElement(selector, timeout = 10000) {
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      const element = document.querySelector(selector);
      if (element) return element;
      await this.sleep(100);
    }
    throw new Error(`Element ${selector} not found within ${timeout}ms`);
  }

  async waitForCondition(condition, timeout = 10000) {
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      if (await condition()) return true;
      await this.sleep(100);
    }
    throw new Error(`Condition not met within ${timeout}ms`);
  }

  step(description) {
    this.currentStep++;
    this.log(`Step ${this.currentStep}: ${description}`, 'step');
  }

  async testLogin(username, password) {
    this.step('Testing login process');
    
    // Navigate to login page if not already there
    if (!window.location.pathname.includes('/login')) {
      window.location.href = '/login';
      await this.sleep(2000);
    }

    // Fill login form
    const usernameInput = await this.waitForElement('input[name="username"], input[type="text"]');
    const passwordInput = await this.waitForElement('input[name="password"], input[type="password"]');
    const loginButton = await this.waitForElement('button[type="submit"], button:contains("Login")');

    usernameInput.value = username;
    usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    passwordInput.value = password;
    passwordInput.dispatchEvent(new Event('input', { bubbles: true }));

    this.log(`Filled login form with username: ${username}`);

    // Submit login
    loginButton.click();
    this.log('Submitted login form');

    // Wait for redirect to dashboard
    await this.waitForCondition(() => window.location.pathname.includes('/dashboard'), 15000);
    this.log('Successfully logged in and redirected to dashboard');

    // Wait for encryption initialization
    await this.sleep(3000);
    this.log('Waited for encryption initialization');
  }

  async testUserSearch(searchTerm) {
    this.step('Testing user search functionality');

    // Find search input
    const searchInput = await this.waitForElement('input[placeholder*="search"], input[type="search"]');
    
    searchInput.value = searchTerm;
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    this.log(`Searched for user: ${searchTerm}`);

    // Wait for search results
    await this.sleep(2000);
    
    // Look for search results
    const searchResults = document.querySelectorAll('[data-testid="user-search-result"], .user-search-result');
    if (searchResults.length > 0) {
      this.log(`Found ${searchResults.length} search results`);
      return searchResults[0];
    } else {
      throw new Error('No search results found');
    }
  }

  async testConversationCreation(targetUser) {
    this.step('Testing conversation creation');

    // Click on the first search result to create conversation
    targetUser.click();
    this.log('Clicked on user to create conversation');

    // Wait for conversation to be created and selected
    await this.sleep(3000);
    
    // Check if conversation was created
    const conversationList = document.querySelectorAll('[data-testid="conversation-item"], .conversation-item');
    if (conversationList.length > 0) {
      this.log('Conversation created successfully');
      return conversationList[0];
    } else {
      throw new Error('Conversation was not created');
    }
  }

  async testMessageSending(messageContent) {
    this.step('Testing message sending with encryption');

    // Find message input
    const messageInput = await this.waitForElement('textarea[placeholder*="message"], input[placeholder*="message"]');
    
    messageInput.value = messageContent;
    messageInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    this.log(`Typed message: ${messageContent}`);

    // Send message (Enter key or send button)
    const sendButton = document.querySelector('button[type="submit"], button:contains("Send")');
    if (sendButton) {
      sendButton.click();
    } else {
      // Try Enter key
      messageInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
    }
    
    this.log('Sent message');

    // Wait for message to appear in chat
    await this.sleep(2000);
    
    // Check if message appears in the chat
    const messages = document.querySelectorAll('[data-testid="message"], .message');
    const lastMessage = messages[messages.length - 1];
    
    if (lastMessage && lastMessage.textContent.includes(messageContent)) {
      this.log('Message appeared in chat successfully');
      return lastMessage;
    } else {
      throw new Error('Message did not appear in chat');
    }
  }

  async testEncryptionStatus() {
    this.step('Testing encryption status');

    // Check console for encryption logs
    const encryptionLogs = this.testResults.filter(result => 
      result.message.includes('🔐') || result.message.includes('encryption')
    );

    if (encryptionLogs.length > 0) {
      this.log('Encryption logs found in console');
    }

    // Check if encryption context is available
    if (window.React && window.React.version) {
      this.log('React context available for encryption check');
    }

    this.log('Encryption status check completed');
  }

  async runFullTest() {
    try {
      this.log('Starting E2E Chat Application Test', 'start');
      this.totalSteps = 6;

      // Test 1: Login as first user
      await this.testLogin('alice', 'alicepass123');
      
      // Test 2: Search for another user
      const targetUser = await this.testUserSearch('bob');
      
      // Test 3: Create conversation
      await this.testConversationCreation(targetUser);
      
      // Test 4: Send a message
      await this.testMessageSending('Hello Bob! This is a test message from Alice.');
      
      // Test 5: Send another message to test encryption
      await this.sleep(1000);
      await this.testMessageSending('Testing encryption and real-time messaging! 🔐');
      
      // Test 6: Check encryption status
      await this.testEncryptionStatus();

      this.log('All tests completed successfully! ✅', 'success');
      this.printSummary();

    } catch (error) {
      this.log(`Test failed: ${error.message}`, 'error');
      console.error('Full error:', error);
      this.printSummary();
    }
  }

  printSummary() {
    console.log('\n=== E2E Test Summary ===');
    console.log(`Total steps completed: ${this.currentStep}/${this.totalSteps}`);
    console.log('\nTest Results:');
    
    this.testResults.forEach(result => {
      const icon = result.type === 'error' ? '❌' : 
                   result.type === 'success' ? '✅' : 
                   result.type === 'step' ? '🔄' : 'ℹ️';
      console.log(`${icon} ${result.message}`);
    });
    
    console.log('\n=== End Summary ===\n');
  }
}

// Instructions for running the test
console.log(`
🧪 E2E Chat Application Test Suite
==================================

To run the complete E2E test:
1. Make sure you're on the login page or dashboard
2. Run: const test = new E2ETest(); test.runFullTest();

To run individual tests:
- Login test: test.testLogin('alice', 'alicepass123');
- User search: test.testUserSearch('bob');
- Send message: test.testMessageSending('Hello World!');

Available test users:
- alice / alicepass123
- bob / bobpass123  
- testuser1 / testpass123
- testuser2 / testpass123

Ready to start testing! 🚀
`);

// Make the test class available globally
window.E2ETest = E2ETest;
