#!/usr/bin/env python3
import requests

# Test that the frontend can properly handle the conversation data
BASE_URL = "http://localhost:8000"

def test_conversation_data_structure():
    print("🔧 === TESTING FRONTEND DATA STRUCTURE FIX ===\n")
    
    # Login as Alice
    print("1. Logging in as Alice...")
    login_data = {"email": "<EMAIL>", "password": "password123"}
    response = requests.post(f"{BASE_URL}/api/auth/login/", json=login_data)
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json()['data']['tokens']['access']
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful\n")
    
    # Test conversation list endpoint
    print("2. Testing conversation list data structure...")
    response = requests.get(f"{BASE_URL}/api/messaging/conversations/", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Conversation list API working")
        
        if data.get('count', 0) > 0:
            # Check the first conversation structure
            conversation = data['results'][0]
            print(f"📋 Sample conversation structure:")
            print(f"   ID: {conversation['id']}")
            print(f"   Type: {conversation['type']}")
            print(f"   Participants: {len(conversation['participants'])}")
            
            # Check participant structure
            if conversation['participants']:
                participant = conversation['participants'][0]
                print(f"📋 Sample participant structure:")
                print(f"   ID: {participant['id']}")
                print(f"   Username: {participant['username']}")
                print(f"   First Name: {participant.get('first_name', 'N/A')}")
                print(f"   Last Name: {participant.get('last_name', 'N/A')}")
                print(f"   Profile Picture: {participant.get('profile_picture', 'N/A')}")
                
                # Verify the field names are correct (snake_case)
                required_fields = ['id', 'username', 'first_name', 'last_name']
                missing_fields = [field for field in required_fields if field not in participant]
                
                if not missing_fields:
                    print("✅ All required participant fields present (snake_case)")
                else:
                    print(f"❌ Missing participant fields: {missing_fields}")
            
            print()
        else:
            print("ℹ️  No conversations found (this is expected if none exist)")
    else:
        print(f"❌ Conversation list API failed: {response.status_code}")
        return
    
    print("🎉 Frontend data structure test complete!")
    print("\n📱 Frontend Testing:")
    print("1. Open: http://localhost:3002")
    print("2. Login as: <EMAIL> / password123")
    print("3. Check that conversation list loads without errors")
    print("4. Try the 'New Chat' button to search for users")

if __name__ == "__main__":
    test_conversation_data_structure()
