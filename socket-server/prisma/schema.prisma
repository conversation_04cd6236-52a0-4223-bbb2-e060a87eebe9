// socket-server/prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id              String   @id @default(uuid()) @db.Uuid
  password        String
  last_login      DateTime?
  is_superuser    <PERSON>olean  @default(false)
  email           String   @unique
  username        String   @unique
  firstName       String   @map("first_name")
  lastName        String   @map("last_name")
  profilePicture  String?  @map("profile_picture")
  isVerified      <PERSON><PERSON><PERSON>  @default(false) @map("is_verified")
  is_staff        <PERSON><PERSON>an  @default(false)
  is_active       <PERSON>olean  @default(true)
  date_joined     DateTime @default(now())
  lastSeen        DateTime @updatedAt @map("last_seen")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  sentMessages        Message[] @relation("MessageSender")
  conversations       ConversationParticipant[]
  messageStatuses     MessageStatus[] @relation("MessageStatuses")
  keyBundle           UserKeyBundle?
  oneTimePreKeys      OneTimePreKey[]
  conversationSessions ConversationSession[] @relation("ConversationSessions")

  @@map("users")
}

model Conversation {
  id          String   @id @default(uuid()) @db.Uuid
  type        String   @default("DIRECT") // DIRECT or GROUP
  name        String?  // For group chats
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  participants ConversationParticipant[]
  messages     Message[]
  sessions     ConversationSession[]

  @@map("conversations")
}

model ConversationParticipant {
  id             String   @id @default(uuid()) @db.Uuid
  conversationId String   @map("conversation_id") @db.Uuid
  userId         String   @map("user_id") @db.Uuid
  role           String   @default("MEMBER") // ADMIN or MEMBER
  joinedAt       DateTime @default(now()) @map("joined_at")

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@unique([conversationId, userId])
  @@map("conversation_participants")
}

model Message {
  id             String   @id @default(uuid()) @db.Uuid
  conversationId String   @map("conversation_id") @db.Uuid
  senderId       String   @map("sender_id") @db.Uuid
  content        String   @default("") // Empty for encrypted messages
  messageType    String   @default("TEXT") @map("message_type") // TEXT, IMAGE, FILE, SYSTEM

  // Encryption fields
  encryptedContent      String  @default("") @map("encrypted_content") // NOT NULL but can be empty
  messageKeyId          String? @map("message_key_id")
  senderRatchetKey      String? @map("sender_ratchet_key")
  messageNumber         Int     @default(0) @map("message_number")
  previousChainLength   Int     @default(0) @map("previous_chain_length")
  iv                    String? // Initialization vector for AES-GCM

  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  conversation Conversation      @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  sender       User              @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)
  statuses     MessageStatus[]

  @@map("messages")
}

model MessageStatus {
  id        String            @id @default(uuid()) @db.Uuid
  messageId String            @map("message_id") @db.Uuid
  userId    String            @map("user_id") @db.Uuid
  status    MessageStatusType @default(SENT)
  createdAt DateTime          @default(now()) @map("created_at")
  updatedAt DateTime          @updatedAt @map("updated_at")

  // Relations
  message Message @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user    User    @relation("MessageStatuses", fields: [userId], references: [id], onDelete: Cascade)

  // Each user can have only one status per message
  @@unique([messageId, userId])
  @@map("message_statuses")
}

enum MessageStatusType {
  SENT
  DELIVERED
  READ
  FAILED
}

// Encryption models
model UserKeyBundle {
  id                    String   @id @default(uuid()) @db.Uuid
  userId                String   @unique @map("user_id") @db.Uuid
  identityPublicKey     String   @map("identity_public_key")
  signedPrekeyId        Int      @map("signed_prekey_id")
  signedPrekeyPublic    String   @map("signed_prekey_public")
  signedPrekeySignature String   @map("signed_prekey_signature")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_key_bundles")
}

model OneTimePreKey {
  id        String    @id @default(uuid()) @db.Uuid
  userId    String    @map("user_id") @db.Uuid
  keyId     Int       @map("key_id")
  publicKey String    @map("public_key")
  isUsed    Boolean   @default(false) @map("is_used")
  createdAt DateTime  @default(now()) @map("created_at")
  usedAt    DateTime? @map("used_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, keyId])
  @@map("one_time_prekeys")
}

model ConversationSession {
  id                   String   @id @default(uuid()) @db.Uuid
  conversationId       String   @map("conversation_id") @db.Uuid
  participantId        String   @map("participant_id") @db.Uuid
  sessionState         Json     @map("session_state")
  rootKey              String   @map("root_key")
  chainKeySend         String?  @map("chain_key_send")
  chainKeyReceive      String?  @map("chain_key_receive")
  messageNumberSend    Int      @default(0) @map("message_number_send")
  messageNumberReceive Int      @default(0) @map("message_number_receive")
  previousChainLength  Int      @default(0) @map("previous_chain_length")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  participant  User         @relation("ConversationSessions", fields: [participantId], references: [id], onDelete: Cascade)
  messageKeys  MessageKey[]

  @@unique([conversationId, participantId])
  @@map("conversation_sessions")
}

model MessageKey {
  id            String              @id @default(uuid()) @db.Uuid
  sessionId     String              @map("session_id") @db.Uuid
  messageNumber Int                 @map("message_number")
  messageKey    String              @map("message_key")
  createdAt     DateTime            @default(now()) @map("created_at")

  // Relations
  session ConversationSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@unique([sessionId, messageNumber])
  @@map("message_keys")
}
