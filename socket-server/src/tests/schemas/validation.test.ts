// socket-server/src/tests/schemas/validation.test.ts
import { describe, it, expect } from 'vitest'
import {
  MessageCreateSchema,
  ConversationCreateSchema,
  TypingEventSchema,
  JoinRoomSchema,
  type MessageCreate,
  type ConversationCreate,
  type TypingEvent,
  type JoinRoom
} from '../../schemas'

describe('Schema Validation', () => {
  describe('MessageCreateSchema', () => {
    it('should validate valid message data', () => {
      const validMessage: MessageCreate = {
        content: 'Hello world!',
        conversationId: '123e4567-e89b-12d3-a456-************',
        messageType: 'TEXT',
        tempId: 'temp-123',
      }

      const result = MessageCreateSchema.safeParse(validMessage)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual(validMessage)
      }
    })

    it('should reject empty content', () => {
      const invalidMessage = {
        content: '',
        conversationId: '123e4567-e89b-12d3-a456-************',
        messageType: 'TEXT',
      }

      const result = MessageCreateSchema.safeParse(invalidMessage)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('content')
      }
    })

    it('should reject content that is too long', () => {
      const invalidMessage = {
        content: 'a'.repeat(4001), // Exceeds 4000 character limit
        conversationId: '123e4567-e89b-12d3-a456-************',
        messageType: 'TEXT',
      }

      const result = MessageCreateSchema.safeParse(invalidMessage)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('content')
      }
    })

    it('should reject invalid conversation ID format', () => {
      const invalidMessage = {
        content: 'Hello',
        conversationId: 'invalid-uuid',
        messageType: 'TEXT',
      }

      const result = MessageCreateSchema.safeParse(invalidMessage)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('conversationId')
      }
    })

    it('should reject invalid message type', () => {
      const invalidMessage = {
        content: 'Hello',
        conversationId: '123e4567-e89b-12d3-a456-************',
        messageType: 'INVALID_TYPE',
      }

      const result = MessageCreateSchema.safeParse(invalidMessage)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('messageType')
      }
    })

    it('should accept all valid message types', () => {
      const messageTypes = ['TEXT', 'IMAGE', 'FILE', 'SYSTEM']

      messageTypes.forEach(type => {
        const message = {
          content: 'Test content',
          conversationId: '123e4567-e89b-12d3-a456-************',
          messageType: type,
        }

        const result = MessageCreateSchema.safeParse(message)
        expect(result.success).toBe(true)
      })
    })

    it('should make tempId optional', () => {
      const messageWithoutTempId = {
        content: 'Hello',
        conversationId: '123e4567-e89b-12d3-a456-************',
        messageType: 'TEXT',
      }

      const result = MessageCreateSchema.safeParse(messageWithoutTempId)
      expect(result.success).toBe(true)
    })

    it('should accept whitespace-only content (schema does not trim)', () => {
      const messageWithWhitespace = {
        content: '   ',
        conversationId: '123e4567-e89b-12d3-a456-************',
        messageType: 'TEXT',
      }

      const result = MessageCreateSchema.safeParse(messageWithWhitespace)
      expect(result.success).toBe(true)
    })
  })

  describe('ConversationCreateSchema', () => {
    it('should validate direct conversation', () => {
      const validConversation: ConversationCreate = {
        type: 'DIRECT',
        participantIds: ['123e4567-e89b-12d3-a456-************'],
      }

      const result = ConversationCreateSchema.safeParse(validConversation)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual(validConversation)
      }
    })

    it('should validate group conversation with name', () => {
      const validConversation: ConversationCreate = {
        type: 'GROUP',
        name: 'Test Group',
        participantIds: [
          '123e4567-e89b-12d3-a456-************',
          '123e4567-e89b-12d3-a456-426614174001',
        ],
      }

      const result = ConversationCreateSchema.safeParse(validConversation)
      expect(result.success).toBe(true)
    })

    it('should validate group conversation without name', () => {
      const validConversation: ConversationCreate = {
        type: 'GROUP',
        participantIds: [
          '123e4567-e89b-12d3-a456-************',
          '123e4567-e89b-12d3-a456-426614174001',
        ],
      }

      const result = ConversationCreateSchema.safeParse(validConversation)
      expect(result.success).toBe(true)
    })

    it('should reject invalid conversation type', () => {
      const invalidConversation = {
        type: 'INVALID_TYPE',
        participantIds: ['123e4567-e89b-12d3-a456-************'],
      }

      const result = ConversationCreateSchema.safeParse(invalidConversation)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('type')
      }
    })

    it('should reject empty participant list', () => {
      const invalidConversation = {
        type: 'DIRECT',
        participantIds: [],
      }

      const result = ConversationCreateSchema.safeParse(invalidConversation)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('participantIds')
      }
    })

    it('should reject invalid participant ID format', () => {
      const invalidConversation = {
        type: 'DIRECT',
        participantIds: ['invalid-uuid'],
      }

      const result = ConversationCreateSchema.safeParse(invalidConversation)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('participantIds')
      }
    })

    it('should reject name that is too long', () => {
      const invalidConversation = {
        type: 'GROUP',
        name: 'a'.repeat(101), // Exceeds 100 character limit
        participantIds: ['123e4567-e89b-12d3-a456-************'],
      }

      const result = ConversationCreateSchema.safeParse(invalidConversation)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('name')
      }
    })

    it('should handle duplicate participant IDs', () => {
      const conversationWithDuplicates = {
        type: 'GROUP',
        participantIds: [
          '123e4567-e89b-12d3-a456-************',
          '123e4567-e89b-12d3-a456-************', // Duplicate
        ],
      }

      const result = ConversationCreateSchema.safeParse(conversationWithDuplicates)
      // Schema should still validate, but service should handle deduplication
      expect(result.success).toBe(true)
    })
  })

  describe('TypingEventSchema', () => {
    it('should validate typing event', () => {
      const validEvent: TypingEvent = {
        conversationId: '123e4567-e89b-12d3-a456-************',
      }

      const result = TypingEventSchema.safeParse(validEvent)
      expect(result.success).toBe(true)
    })

    it('should reject invalid conversation ID', () => {
      const invalidEvent = {
        conversationId: 'invalid-uuid',
      }

      const result = TypingEventSchema.safeParse(invalidEvent)
      expect(result.success).toBe(false)
    })

    it('should reject missing conversation ID', () => {
      const invalidEvent = {}

      const result = TypingEventSchema.safeParse(invalidEvent)
      expect(result.success).toBe(false)
    })
  })

  describe('JoinRoomSchema', () => {
    it('should validate join room event', () => {
      const validEvent: JoinRoom = {
        conversationId: '123e4567-e89b-12d3-a456-************',
      }

      const result = JoinRoomSchema.safeParse(validEvent)
      expect(result.success).toBe(true)
    })

    it('should reject invalid conversation ID', () => {
      const invalidEvent = {
        conversationId: 'invalid-uuid',
      }

      const result = JoinRoomSchema.safeParse(invalidEvent)
      expect(result.success).toBe(false)
    })

    it('should reject missing conversation ID', () => {
      const invalidEvent = {}

      const result = JoinRoomSchema.safeParse(invalidEvent)
      expect(result.success).toBe(false)
    })
  })



  describe('Edge cases and error handling', () => {
    it('should handle null values gracefully', () => {
      const result = MessageCreateSchema.safeParse(null)
      expect(result.success).toBe(false)
    })

    it('should handle undefined values gracefully', () => {
      const result = MessageCreateSchema.safeParse(undefined)
      expect(result.success).toBe(false)
    })

    it('should handle non-object values gracefully', () => {
      const result = MessageCreateSchema.safeParse('not an object')
      expect(result.success).toBe(false)
    })

    it('should provide detailed error messages', () => {
      const invalidMessage = {
        content: '',
        conversationId: 'invalid',
        messageType: 'INVALID',
      }

      const result = MessageCreateSchema.safeParse(invalidMessage)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.length).toBeGreaterThan(0)
        expect(result.error.issues[0]).toHaveProperty('message')
        expect(result.error.issues[0]).toHaveProperty('path')
      }
    })

    it('should handle extra fields gracefully', () => {
      const messageWithExtraFields = {
        content: 'Hello',
        conversationId: '123e4567-e89b-12d3-a456-************',
        messageType: 'TEXT',
        extraField: 'should be ignored',
      }

      const result = MessageCreateSchema.safeParse(messageWithExtraFields)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).not.toHaveProperty('extraField')
      }
    })
  })
})
