// socket-server/src/tests/middleware/auth.test.ts
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import jwt from 'jsonwebtoken'
import { authenticateSocket } from '../../middleware/auth'
import { prismaMock } from '../../test/setup'

describe('authenticateSocket', () => {
  let mockSocket: any
  let mockNext: ReturnType<typeof vi.fn>

  const mockUser = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    email: '<EMAIL>',
    username: 'testuser',
    firstName: 'Test',
    lastName: 'User',
    password: 'hashedpassword',
    last_login: new Date('2023-01-01T00:00:00Z'),
    is_superuser: false,
    profilePicture: null,
    isVerified: true,
    is_staff: false,
    is_active: true,
    date_joined: new Date('2023-01-01T00:00:00Z'),
    lastSeen: new Date('2023-01-01T00:00:00Z'),
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
  }

  const mockJWTPayload = {
    user_id: mockUser.id,
    token_type: 'access',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600,
    jti: 'jwt-id',
  }

  beforeEach(() => {
    // Reset the global mock
    vi.clearAllMocks()

    mockSocket = {
      handshake: {
        auth: {},
        headers: {},
      },
    }

    mockNext = vi.fn()

    // Set up environment variable
    process.env.JWT_SECRET = 'test-jwt-secret'
  })

  afterEach(() => {
    // Restore JWT_SECRET to the test value
    process.env.JWT_SECRET = 'test-jwt-secret'
  })

  describe('successful authentication', () => {
    it('should authenticate with token in auth object', async () => {
      mockSocket.handshake.auth.token = 'valid-token'
      vi.mocked(jwt.verify).mockReturnValue(mockJWTPayload as any)
      prismaMock.user.findUnique.mockResolvedValue(mockUser)

      await authenticateSocket(mockSocket, mockNext)

      expect(jwt.verify).toHaveBeenCalledWith('valid-token', 'test-jwt-secret')
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { id: mockUser.id },
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
        },
      })
      expect(mockSocket.userId).toBe(mockUser.id)
      expect(mockSocket.user).toEqual(mockUser)
      expect(mockNext).toHaveBeenCalledWith()
    })

    it('should authenticate with token in authorization header', async () => {
      mockSocket.handshake.headers.authorization = 'Bearer valid-token'
      vi.mocked(jwt.verify).mockReturnValue(mockJWTPayload as any)
      prismaMock.user.findUnique.mockResolvedValue(mockUser)

      await authenticateSocket(mockSocket, mockNext)

      expect(jwt.verify).toHaveBeenCalledWith('valid-token', 'test-jwt-secret')
      expect(mockSocket.userId).toBe(mockUser.id)
      expect(mockSocket.user).toEqual(mockUser)
      expect(mockNext).toHaveBeenCalledWith()
    })

    it('should prefer auth token over header token', async () => {
      mockSocket.handshake.auth.token = 'auth-token'
      mockSocket.handshake.headers.authorization = 'Bearer header-token'
      vi.mocked(jwt.verify).mockReturnValue(mockJWTPayload as any)
      prismaMock.user.findUnique.mockResolvedValue(mockUser)

      await authenticateSocket(mockSocket, mockNext)

      expect(jwt.verify).toHaveBeenCalledWith('auth-token', 'test-jwt-secret')
      expect(mockNext).toHaveBeenCalledWith()
    })
  })

  describe('authentication failures', () => {
    it('should fail when no token is provided', async () => {
      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('Authentication token required'))
      expect(jwt.verify).not.toHaveBeenCalled()
      expect(prismaMock.user.findUnique).not.toHaveBeenCalled()
    })

    it('should fail when JWT_SECRET is not configured', async () => {
      delete process.env.JWT_SECRET
      mockSocket.handshake.auth.token = 'valid-token'

      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('JWT secret not configured'))
      expect(jwt.verify).not.toHaveBeenCalled()
    })

    it('should fail when JWT verification fails', async () => {
      mockSocket.handshake.auth.token = 'invalid-token'
      vi.mocked(jwt.verify).mockImplementation(() => {
        throw new Error('Invalid token')
      })

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('Invalid authentication token'))
      expect(prismaMock.user.findUnique).not.toHaveBeenCalled()
      expect(consoleSpy).toHaveBeenCalledWith('Socket authentication error:', expect.any(Error))

      consoleSpy.mockRestore()
    })

    it('should fail when user is not found in database', async () => {
      mockSocket.handshake.auth.token = 'valid-token'
      vi.mocked(jwt.verify).mockReturnValue(mockJWTPayload as any)
      prismaMock.user.findUnique.mockResolvedValue(null)

      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('User not found'))
      expect(mockSocket.userId).toBeUndefined()
      expect(mockSocket.user).toBeUndefined()
    })

    it('should fail when database query fails', async () => {
      mockSocket.handshake.auth.token = 'valid-token'
      vi.mocked(jwt.verify).mockReturnValue(mockJWTPayload as any)
      prismaMock.user.findUnique.mockRejectedValue(new Error('Database error'))

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('Invalid authentication token'))
      expect(consoleSpy).toHaveBeenCalledWith('Socket authentication error:', expect.any(Error))

      consoleSpy.mockRestore()
    })

    it('should handle malformed JWT payload', async () => {
      mockSocket.handshake.auth.token = 'valid-token'
      vi.mocked(jwt.verify).mockReturnValue({ invalid: 'payload' } as any)
      prismaMock.user.findUnique.mockResolvedValue(null)

      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('User not found'))
    })

    it('should handle expired JWT token', async () => {
      mockSocket.handshake.auth.token = 'expired-token'
      const expiredError = new Error('jwt expired')
      expiredError.name = 'TokenExpiredError'
      vi.mocked(jwt.verify).mockImplementation(() => {
        throw expiredError
      })

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('Invalid authentication token'))
      expect(consoleSpy).toHaveBeenCalledWith('Socket authentication error:', expiredError)
      
      consoleSpy.mockRestore()
    })

    it('should handle malformed authorization header', async () => {
      mockSocket.handshake.headers.authorization = 'InvalidFormat token'

      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('Authentication token required'))
    })

    it('should handle empty authorization header', async () => {
      mockSocket.handshake.headers.authorization = 'Bearer '

      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('Authentication token required'))
    })
  })

  describe('edge cases', () => {
    it('should handle missing handshake object', async () => {
      const socketWithoutHandshake = {}

      await authenticateSocket(socketWithoutHandshake as any, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('Authentication token required'))
    })

    it('should handle missing auth and headers objects', async () => {
      mockSocket.handshake = {}

      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('Authentication token required'))
    })

    it('should handle null token values', async () => {
      mockSocket.handshake.auth.token = null
      mockSocket.handshake.headers.authorization = null

      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('Authentication token required'))
    })

    it('should handle undefined JWT_SECRET gracefully', async () => {
      delete process.env.JWT_SECRET
      mockSocket.handshake.auth.token = 'valid-token'

      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('JWT secret not configured'))
    })

    it('should handle empty JWT_SECRET', async () => {
      process.env.JWT_SECRET = ''
      mockSocket.handshake.auth.token = 'valid-token'

      await authenticateSocket(mockSocket, mockNext)

      expect(mockNext).toHaveBeenCalledWith(new Error('JWT secret not configured'))
    })

    it('should handle user with missing fields', async () => {
      mockSocket.handshake.auth.token = 'valid-token'
      vi.mocked(jwt.verify).mockReturnValue(mockJWTPayload as any)

      const incompleteUser = {
        id: mockUser.id,
        email: mockUser.email,
        // Missing other fields
      }

      prismaMock.user.findUnique.mockResolvedValue(incompleteUser as any)

      await authenticateSocket(mockSocket, mockNext)

      expect(mockSocket.userId).toBe(mockUser.id)
      expect(mockSocket.user).toEqual(incompleteUser)
      expect(mockNext).toHaveBeenCalledWith()
    })
  })
})
