// socket-server/src/tests/encryption/encryptionFlow.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { MessageService } from '../../services/messageService';
import { ConversationService } from '../../services/conversationService';
import { MessageStatusService } from '../../services/messageStatusService';
import { SocketEventHandler } from '../../events/socketEvents';
import { MessageCreateSchema } from '../../schemas';
import { Server } from 'socket.io';
import { createServer } from 'http';

// Mock Prisma
const prismaMock = {
  message: {
    create: vi.fn(),
    findMany: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
  conversation: {
    findUnique: vi.fn(),
    findMany: vi.fn(),
  },
  conversationParticipant: {
    findMany: vi.fn(),
  },
  user: {
    findUnique: vi.fn(),
    update: vi.fn(),
  },
};

describe('Encryption Flow Tests', () => {
  let messageService: MessageService;
  let conversationService: ConversationService;
  let messageStatusService: MessageStatusService;
  let socketEvents: SocketEventHandler;
  let io: Server;

  const mockUser = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    email: '<EMAIL>',
    username: 'testuser',
    firstName: 'Test',
    lastName: 'User',
    password: 'hashedpassword',
    last_login: new Date('2023-01-01T00:00:00Z'),
    is_superuser: false,
    profilePicture: null,
    isVerified: true,
    is_staff: false,
    is_active: true,
    date_joined: new Date('2023-01-01T00:00:00Z'),
    lastSeen: new Date('2023-01-01T00:00:00Z'),
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    messageService = new MessageService(prismaMock as any);
    conversationService = new ConversationService(prismaMock as any);
    messageStatusService = new MessageStatusService(prismaMock as any);

    const httpServer = createServer();
    io = new Server(httpServer);
    socketEvents = new SocketEventHandler(io, messageService, conversationService, messageStatusService);
  });

  describe('Encrypted Message Handling', () => {
    it('should validate encrypted message schema', () => {
      const encryptedMessageData = {
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        content: '', // Empty for encrypted messages
        encryptedContent: 'base64-encrypted-content',
        iv: 'base64-iv',
        messageNumber: 0,
        senderRatchetKey: 'base64-ratchet-key',
        previousChainLength: 0,
        messageType: 'TEXT',
        tempId: 'temp-123',
      };

      const result = MessageCreateSchema.safeParse(encryptedMessageData);
      expect(result.success).toBe(true);
    });

    it('should validate unencrypted message schema', () => {
      const unencryptedMessageData = {
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        content: 'Hello, World!',
        messageType: 'TEXT',
        tempId: 'temp-123',
      };

      const result = MessageCreateSchema.safeParse(unencryptedMessageData);
      expect(result.success).toBe(true);
    });

    it('should reject message with neither content nor encrypted content', () => {
      const invalidMessageData = {
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        messageType: 'TEXT',
        tempId: 'temp-123',
      };

      const result = MessageCreateSchema.safeParse(invalidMessageData);
      expect(result.success).toBe(false);
    });

    it('should create encrypted message in database', async () => {
      const mockEncryptedMessage = {
        id: '123e4567-e89b-12d3-a456-426614174003',
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        senderId: '123e4567-e89b-12d3-a456-426614174000',
        content: '',
        messageType: 'TEXT',
        encryptedContent: 'base64-encrypted-content',
        iv: 'base64-iv',
        messageNumber: 0,
        senderRatchetKey: 'base64-ratchet-key',
        previousChainLength: 0,
        messageKeyId: null,
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
        sender: {
          id: mockUser.id,
          username: mockUser.username,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          profilePicture: mockUser.profilePicture,
        },
      };

      prismaMock.message.create.mockResolvedValue(mockEncryptedMessage);

      const messageData = {
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        content: '',
        encryptedContent: 'base64-encrypted-content',
        iv: 'base64-iv',
        messageNumber: 0,
        senderRatchetKey: 'base64-ratchet-key',
        previousChainLength: 0,
        messageType: 'TEXT' as const,
      };

      const result = await messageService.createMessage(
        messageData,
        '123e4567-e89b-12d3-a456-426614174000'
      );

      expect(result).toEqual(mockEncryptedMessage);
      expect(prismaMock.message.create).toHaveBeenCalledWith({
        data: {
          conversationId: messageData.conversationId,
          senderId: '123e4567-e89b-12d3-a456-426614174000',
          content: '',
          messageType: messageData.messageType,
          encryptedContent: messageData.encryptedContent,
          iv: messageData.iv,
          messageNumber: messageData.messageNumber,
          senderRatchetKey: messageData.senderRatchetKey,
          previousChainLength: messageData.previousChainLength,
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              profilePicture: true,
            },
          },
        },
      });
    });

    it('should create unencrypted message in database', async () => {
      const mockUnencryptedMessage = {
        id: '123e4567-e89b-12d3-a456-426614174003',
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        senderId: '123e4567-e89b-12d3-a456-426614174000',
        content: 'Hello, World!',
        messageType: 'TEXT',
        encryptedContent: null,
        iv: null,
        messageNumber: 0,
        senderRatchetKey: null,
        previousChainLength: 0,
        messageKeyId: null,
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
        sender: {
          id: mockUser.id,
          username: mockUser.username,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          profilePicture: mockUser.profilePicture,
        },
      };

      prismaMock.message.create.mockResolvedValue(mockUnencryptedMessage);

      const messageData = {
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        content: 'Hello, World!',
        messageType: 'TEXT' as const,
      };

      const result = await messageService.createMessage(
        messageData,
        '123e4567-e89b-12d3-a456-426614174000'
      );

      expect(result).toEqual(mockUnencryptedMessage);
      expect(prismaMock.message.create).toHaveBeenCalledWith({
        data: {
          conversationId: messageData.conversationId,
          senderId: '123e4567-e89b-12d3-a456-426614174000',
          content: messageData.content,
          messageType: messageData.messageType,
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              profilePicture: true,
            },
          },
        },
      });
    });
  });

  describe('Socket Event Handling', () => {
    it('should emit encrypted message with all encryption fields', () => {
      const mockSocket = {
        id: 'socket-123',
        user: mockUser,
        join: vi.fn(),
        emit: vi.fn(),
        to: vi.fn().mockReturnThis(),
      };

      const mockEncryptedMessage = {
        id: '123e4567-e89b-12d3-a456-426614174003',
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        senderId: '123e4567-e89b-12d3-a456-426614174000',
        content: '',
        messageType: 'TEXT',
        encryptedContent: 'base64-encrypted-content',
        iv: 'base64-iv',
        messageNumber: 5,
        senderRatchetKey: 'base64-ratchet-key',
        previousChainLength: 2,
        messageKeyId: null,
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
        sender: {
          id: mockUser.id,
          username: mockUser.username,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          profilePicture: mockUser.profilePicture,
        },
      };

      // Mock the io.to method
      const mockTo = {
        emit: vi.fn(),
      };
      vi.spyOn(io, 'to').mockReturnValue(mockTo as any);

      // Simulate the socket event emission
      const expectedPayload = {
        id: mockEncryptedMessage.id,
        conversationId: mockEncryptedMessage.conversationId,
        sender: mockEncryptedMessage.sender,
        content: mockEncryptedMessage.content,
        messageType: mockEncryptedMessage.messageType,
        createdAt: mockEncryptedMessage.createdAt,
        updatedAt: mockEncryptedMessage.updatedAt,
        status: 'DELIVERED',
        encryptedContent: mockEncryptedMessage.encryptedContent,
        iv: mockEncryptedMessage.iv,
        messageNumber: mockEncryptedMessage.messageNumber,
        senderRatchetKey: mockEncryptedMessage.senderRatchetKey,
        previousChainLength: mockEncryptedMessage.previousChainLength,
      };

      // Manually call the emission logic
      io.to(`conversation_${mockEncryptedMessage.conversationId}`).emit('new_message', expectedPayload);

      expect(io.to).toHaveBeenCalledWith(`conversation_${mockEncryptedMessage.conversationId}`);
      expect(mockTo.emit).toHaveBeenCalledWith('new_message', expectedPayload);
    });

    it('should emit unencrypted message without encryption fields', () => {
      const mockUnencryptedMessage = {
        id: '123e4567-e89b-12d3-a456-426614174003',
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        senderId: '123e4567-e89b-12d3-a456-426614174000',
        content: 'Hello, World!',
        messageType: 'TEXT',
        encryptedContent: null,
        iv: null,
        messageNumber: 0,
        senderRatchetKey: null,
        previousChainLength: 0,
        messageKeyId: null,
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
        sender: {
          id: mockUser.id,
          username: mockUser.username,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          profilePicture: mockUser.profilePicture,
        },
      };

      // Mock the io.to method
      const mockTo = {
        emit: vi.fn(),
      };
      vi.spyOn(io, 'to').mockReturnValue(mockTo as any);

      // Expected payload should not include null encryption fields
      const expectedPayload = {
        id: mockUnencryptedMessage.id,
        conversationId: mockUnencryptedMessage.conversationId,
        sender: mockUnencryptedMessage.sender,
        content: mockUnencryptedMessage.content,
        messageType: mockUnencryptedMessage.messageType,
        createdAt: mockUnencryptedMessage.createdAt,
        updatedAt: mockUnencryptedMessage.updatedAt,
        status: 'DELIVERED',
      };

      // Manually call the emission logic
      io.to(`conversation_${mockUnencryptedMessage.conversationId}`).emit('new_message', expectedPayload);

      expect(io.to).toHaveBeenCalledWith(`conversation_${mockUnencryptedMessage.conversationId}`);
      expect(mockTo.emit).toHaveBeenCalledWith('new_message', expectedPayload);
    });
  });

  describe('Security and Privacy', () => {
    it('should never log or expose encrypted content', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const mockEncryptedMessage = {
        id: '123e4567-e89b-12d3-a456-426614174003',
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        senderId: '123e4567-e89b-12d3-a456-426614174000',
        content: '',
        messageType: 'TEXT',
        encryptedContent: 'sensitive-encrypted-content',
        iv: 'sensitive-iv',
        messageNumber: 0,
        senderRatchetKey: 'sensitive-ratchet-key',
        previousChainLength: 0,
        messageKeyId: null,
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
        sender: {
          id: mockUser.id,
          username: mockUser.username,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          profilePicture: mockUser.profilePicture,
        },
      };

      prismaMock.message.create.mockResolvedValue(mockEncryptedMessage);

      const messageData = {
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        content: '',
        encryptedContent: 'sensitive-encrypted-content',
        iv: 'sensitive-iv',
        messageNumber: 0,
        senderRatchetKey: 'sensitive-ratchet-key',
        previousChainLength: 0,
        messageType: 'TEXT' as const,
      };

      await messageService.createMessage(messageData, '123e4567-e89b-12d3-a456-426614174000');

      // Check that sensitive data is not logged
      const allLogs = [...consoleSpy.mock.calls, ...consoleErrorSpy.mock.calls].flat();
      const sensitiveData = ['sensitive-encrypted-content', 'sensitive-iv', 'sensitive-ratchet-key'];
      
      for (const sensitive of sensitiveData) {
        const foundInLogs = allLogs.some(log => 
          typeof log === 'string' && log.includes(sensitive)
        );
        expect(foundInLogs).toBe(false);
      }

      consoleSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });

    it('should handle malformed encryption data gracefully', () => {
      const malformedData = {
        conversationId: '123e4567-e89b-12d3-a456-426614174001',
        content: '',
        encryptedContent: 'invalid-base64-!@#$%',
        iv: 'invalid-iv-!@#$%',
        messageNumber: -1, // Invalid negative number
        senderRatchetKey: '', // Empty string
        previousChainLength: 'not-a-number', // Wrong type
        messageType: 'TEXT',
      };

      const result = MessageCreateSchema.safeParse(malformedData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        expect(result.error.issues).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              path: expect.arrayContaining(['messageNumber']),
            }),
            expect.objectContaining({
              path: expect.arrayContaining(['previousChainLength']),
            }),
          ])
        );
      }
    });
  });
});
