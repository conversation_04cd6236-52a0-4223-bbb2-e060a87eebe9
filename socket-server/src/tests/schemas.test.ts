// socket-server/src/tests/schemas.test.ts
import { describe, it, expect } from 'vitest';
import {
  UserSchema,
  MessageSchema,
  MessageCreateSchema,
  ConversationSchema,
  ConversationCreateSchema,
  JoinRoomSchema,
  LeaveRoomSchema,
  TypingEventSchema,
  AuthTokenSchema,
} from '../schemas';

describe('Zod Schema Validation Tests', () => {
  describe('UserSchema', () => {
    it('should validate a valid user object', () => {
      const validUser = {
        id: '123e4567-e89b-12d3-a456-************',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        profilePicture: 'https://example.com/avatar.jpg',
        isVerified: false,
        lastSeen: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = UserSchema.safeParse(validUser);
      expect(result.success).toBe(true);
    });

    it('should reject invalid email', () => {
      const invalidUser = {
        id: '123e4567-e89b-12d3-a456-************',
        email: 'invalid-email',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        isVerified: false,
        lastSeen: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = UserSchema.safeParse(invalidUser);
      expect(result.success).toBe(false);
    });

    it('should reject invalid UUID', () => {
      const invalidUser = {
        id: 'invalid-uuid',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        isVerified: false,
        lastSeen: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = UserSchema.safeParse(invalidUser);
      expect(result.success).toBe(false);
    });
  });

  describe('MessageCreateSchema', () => {
    it('should validate a valid message creation request', () => {
      const validMessage = {
        content: 'Hello, world!',
        conversationId: '123e4567-e89b-12d3-a456-************',
      };

      const result = MessageCreateSchema.safeParse(validMessage);
      expect(result.success).toBe(true);
    });

    it('should reject empty content', () => {
      const invalidMessage = {
        content: '',
        conversationId: '123e4567-e89b-12d3-a456-************',
      };

      const result = MessageCreateSchema.safeParse(invalidMessage);
      expect(result.success).toBe(false);
    });

    it('should reject content that is too long', () => {
      const invalidMessage = {
        content: 'a'.repeat(4001), // Exceeds 4000 character limit
        conversationId: '123e4567-e89b-12d3-a456-************',
      };

      const result = MessageCreateSchema.safeParse(invalidMessage);
      expect(result.success).toBe(false);
    });

    it('should reject invalid conversation ID', () => {
      const invalidMessage = {
        content: 'Hello, world!',
        conversationId: 'invalid-uuid',
      };

      const result = MessageCreateSchema.safeParse(invalidMessage);
      expect(result.success).toBe(false);
    });
  });

  describe('ConversationCreateSchema', () => {
    it('should validate a valid conversation creation request', () => {
      const validConversation = {
        name: 'Test Conversation',
        type: 'GROUP',
        participantIds: [
          '123e4567-e89b-12d3-a456-************',
          '123e4567-e89b-12d3-a456-************',
        ],
      };

      const result = ConversationCreateSchema.safeParse(validConversation);
      expect(result.success).toBe(true);
    });

    it('should validate conversation without name (direct message)', () => {
      const validConversation = {
        type: 'DIRECT',
        participantIds: ['123e4567-e89b-12d3-a456-************'],
      };

      const result = ConversationCreateSchema.safeParse(validConversation);
      expect(result.success).toBe(true);
    });

    it('should reject empty participant list', () => {
      const invalidConversation = {
        name: 'Test Conversation',
        type: 'GROUP',
        participantIds: [],
      };

      const result = ConversationCreateSchema.safeParse(invalidConversation);
      expect(result.success).toBe(false);
    });

    it('should reject invalid participant UUIDs', () => {
      const invalidConversation = {
        name: 'Test Conversation',
        type: 'GROUP',
        participantIds: ['invalid-uuid'],
      };

      const result = ConversationCreateSchema.safeParse(invalidConversation);
      expect(result.success).toBe(false);
    });
  });

  describe('JoinRoomSchema', () => {
    it('should validate a valid join room request', () => {
      const validJoinRoom = {
        conversationId: '123e4567-e89b-12d3-a456-************',
      };

      const result = JoinRoomSchema.safeParse(validJoinRoom);
      expect(result.success).toBe(true);
    });

    it('should reject invalid conversation ID', () => {
      const invalidJoinRoom = {
        conversationId: 'invalid-uuid',
      };

      const result = JoinRoomSchema.safeParse(invalidJoinRoom);
      expect(result.success).toBe(false);
    });
  });

  describe('TypingEventSchema', () => {
    it('should validate a valid typing event', () => {
      const validTyping = {
        conversationId: '123e4567-e89b-12d3-a456-************',
      };

      const result = TypingEventSchema.safeParse(validTyping);
      expect(result.success).toBe(true);
    });

    it('should reject invalid conversation ID', () => {
      const invalidTyping = {
        conversationId: 'invalid-uuid',
      };

      const result = TypingEventSchema.safeParse(invalidTyping);
      expect(result.success).toBe(false);
    });

    it('should reject missing conversation ID', () => {
      const invalidTyping = {};

      const result = TypingEventSchema.safeParse(invalidTyping);
      expect(result.success).toBe(false);
    });
  });

  describe('AuthTokenSchema', () => {
    it('should validate a valid auth token', () => {
      const validToken = {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
      };

      const result = AuthTokenSchema.safeParse(validToken);
      expect(result.success).toBe(true);
    });

    it('should reject empty token', () => {
      const invalidToken = {
        token: '',
      };

      const result = AuthTokenSchema.safeParse(invalidToken);
      expect(result.success).toBe(false);
    });
  });
});
