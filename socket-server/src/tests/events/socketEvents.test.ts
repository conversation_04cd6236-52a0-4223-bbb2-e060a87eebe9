// socket-server/src/tests/events/socketEvents.test.ts
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { Server } from 'socket.io'
import { mockDeep, mockReset, DeepMockProxy } from 'vitest-mock-extended'
import { SocketEventHandler } from '../../events/socketEvents'
import { MessageService } from '../../services/messageService'
import { ConversationService } from '../../services/conversationService'
import { MessageStatusService } from '../../services/messageStatusService'

// Mock Socket interface
interface MockSocket {
  userId: string
  user: any
  on: ReturnType<typeof vi.fn>
  emit: ReturnType<typeof vi.fn>
  join: ReturnType<typeof vi.fn>
  to: ReturnType<typeof vi.fn>
  broadcast: {
    emit: ReturnType<typeof vi.fn>
  }
}

describe('SocketEventHandler', () => {
  let socketEventHandler: SocketEventHandler
  let mockIo: DeepMockProxy<Server>
  let mockMessageService: DeepMockProxy<MessageService>
  let mockConversationService: DeepMockProxy<ConversationService>
  let mockMessageStatusService: DeepMockProxy<MessageStatusService>
  let mockSocket: MockSocket

  const mockUser = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    email: '<EMAIL>',
    username: 'testuser',
    firstName: 'Test',
    lastName: 'User',
    password: 'hashedpassword',
    last_login: new Date('2023-01-01T00:00:00Z'),
    is_superuser: false,
    profilePicture: null,
    isVerified: true,
    is_staff: false,
    is_active: true,
    date_joined: new Date('2023-01-01T00:00:00Z'),
    lastSeen: new Date('2023-01-01T00:00:00Z'),
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
  }

  const mockConversations = [
    {
      id: '123e4567-e89b-12d3-a456-************',
      type: 'DIRECT',
      name: null,
      participants: [],
      messages: [],
      lastMessage: {
        id: '123e4567-e89b-12d3-a456-************',
        conversationId: '123e4567-e89b-12d3-a456-************',
        senderId: '123e4567-e89b-12d3-a456-426614174000',
        content: 'Hello world!',
        messageType: 'TEXT',
        encryptedContent: null,
        iv: null,
        messageNumber: 0,
        senderRatchetKey: null,
        previousChainLength: 0,
        messageKeyId: null,
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
        sender: {
          id: mockUser.id,
          username: mockUser.username,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
        },
      },
      createdAt: new Date('2023-01-01T00:00:00Z'),
      updatedAt: new Date('2023-01-01T00:00:00Z'),
    },
    {
      id: '123e4567-e89b-12d3-a456-426614174002',
      type: 'GROUP',
      name: 'Test Group',
      participants: [],
      messages: [],
      lastMessage: {
        id: '123e4567-e89b-12d3-a456-426614174004',
        conversationId: '123e4567-e89b-12d3-a456-426614174002',
        senderId: '123e4567-e89b-12d3-a456-426614174000',
        content: 'Group message!',
        messageType: 'TEXT',
        encryptedContent: null,
        iv: null,
        messageNumber: 0,
        senderRatchetKey: null,
        previousChainLength: 0,
        messageKeyId: null,
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
        sender: {
          id: mockUser.id,
          username: mockUser.username,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
        },
      },
      createdAt: new Date('2023-01-01T00:00:00Z'),
      updatedAt: new Date('2023-01-01T00:00:00Z'),
    },
  ]

  const mockMessage = {
    id: '123e4567-e89b-12d3-a456-************',
    conversationId: '123e4567-e89b-12d3-a456-************',
    senderId: '123e4567-e89b-12d3-a456-426614174000',
    content: 'Hello world!',
    messageType: 'TEXT',
    // Encryption fields
    encryptedContent: null,
    messageKeyId: null,
    senderRatchetKey: null,
    messageNumber: 0,
    previousChainLength: 0,
    iv: null,
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
    sender: {
      id: mockUser.id,
      username: mockUser.username,
      firstName: mockUser.firstName,
      lastName: mockUser.lastName,
      profilePicture: mockUser.profilePicture,
    },
  }

  beforeEach(() => {
    mockIo = mockDeep<Server>()
    mockMessageService = mockDeep<MessageService>()
    mockConversationService = mockDeep<ConversationService>()
    mockMessageStatusService = mockDeep<MessageStatusService>()

    socketEventHandler = new SocketEventHandler(
      mockIo,
      mockMessageService,
      mockConversationService,
      mockMessageStatusService
    )

    mockSocket = {
      userId: mockUser.id,
      user: mockUser,
      on: vi.fn(),
      emit: vi.fn(),
      join: vi.fn(),
      to: vi.fn(() => ({ emit: vi.fn() })),
      broadcast: {
        emit: vi.fn(),
      },
    }
  })

  afterEach(() => {
    mockReset(mockIo)
    mockReset(mockMessageService)
    mockReset(mockConversationService)
    vi.clearAllMocks()
  })

  describe('handleConnection', () => {
    it('should set up all event listeners', () => {
      socketEventHandler.handleConnection(mockSocket as any)

      expect(mockSocket.on).toHaveBeenCalledWith('join_conversations', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('send_message', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('typing_start', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('typing_stop', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('join_conversation', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('user_online', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('disconnect', expect.any(Function))
    })

    it('should log user connection', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      
      socketEventHandler.handleConnection(mockSocket as any)

      expect(consoleSpy).toHaveBeenCalledWith(
        `User ${mockUser.id} connected to messaging`
      )
      
      consoleSpy.mockRestore()
    })
  })

  describe('handleJoinConversations', () => {
    it('should join user to all their conversations', async () => {
      mockConversationService.getUserConversations.mockResolvedValue(mockConversations)

      // Get the handler function
      socketEventHandler.handleConnection(mockSocket as any)
      const joinConversationsHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'join_conversations'
      )?.[1]

      await joinConversationsHandler()

      expect(mockConversationService.getUserConversations).toHaveBeenCalledWith(mockUser.id)
      expect(mockSocket.join).toHaveBeenCalledWith('conversation_123e4567-e89b-12d3-a456-************')
      expect(mockSocket.join).toHaveBeenCalledWith('conversation_123e4567-e89b-12d3-a456-426614174002')
      expect(mockSocket.emit).toHaveBeenCalledWith('conversations_joined', {
        success: true,
        count: 2,
        conversations: mockConversations,
      })
    })

    it('should handle errors when joining conversations', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockConversationService.getUserConversations.mockRejectedValue(new Error('Database error'))

      socketEventHandler.handleConnection(mockSocket as any)
      const joinConversationsHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'join_conversations'
      )?.[1]

      await joinConversationsHandler()

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Failed to join conversations',
      })
      expect(consoleSpy).toHaveBeenCalledWith('Error joining conversations:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })

    it('should handle empty conversations list', async () => {
      mockConversationService.getUserConversations.mockResolvedValue([])

      socketEventHandler.handleConnection(mockSocket as any)
      const joinConversationsHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'join_conversations'
      )?.[1]

      await joinConversationsHandler()

      expect(mockSocket.join).not.toHaveBeenCalled()
      expect(mockSocket.emit).toHaveBeenCalledWith('conversations_joined', {
        success: true,
        count: 0,
        conversations: [],
      })
    })
  })

  describe('handleSendMessage', () => {
    const messageData = {
      content: 'Hello world!',
      conversationId: '123e4567-e89b-12d3-a456-************',
      messageType: 'TEXT',
      tempId: 'temp-123',
    }

    it('should create and broadcast message successfully', async () => {
      mockMessageService.createMessage.mockResolvedValue(mockMessage)
      mockIo.to.mockReturnValue({ emit: vi.fn() } as any)

      socketEventHandler.handleConnection(mockSocket as any)
      const sendMessageHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'send_message'
      )?.[1]

      await sendMessageHandler(messageData)

      expect(mockMessageService.createMessage).toHaveBeenCalledWith(messageData, mockUser.id)
      expect(mockIo.to).toHaveBeenCalledWith('conversation_123e4567-e89b-12d3-a456-************')
      expect(mockSocket.emit).toHaveBeenCalledWith('message_sent', {
        tempId: 'temp-123',
        messageId: mockMessage.id,
        status: 'DELIVERED',
      })
    })

    it('should handle validation errors', async () => {
      // Import ZodError to create a proper instance
      const { z } = await import('zod')

      try {
        z.object({ content: z.string().min(1) }).parse({ content: '' })
      } catch (error) {
        mockMessageService.createMessage.mockRejectedValue(error)
      }

      socketEventHandler.handleConnection(mockSocket as any)
      const sendMessageHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'send_message'
      )?.[1]

      await sendMessageHandler(messageData)

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Invalid message data',
        details: expect.any(Array),
      })
    })

    it('should handle general errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockMessageService.createMessage.mockRejectedValue(new Error('Database error'))

      socketEventHandler.handleConnection(mockSocket as any)
      const sendMessageHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'send_message'
      )?.[1]

      await sendMessageHandler(messageData)

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Database error',
      })
      expect(consoleSpy).toHaveBeenCalledWith('Error sending message:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })

    it('should handle unknown errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockMessageService.createMessage.mockRejectedValue('Unknown error')

      socketEventHandler.handleConnection(mockSocket as any)
      const sendMessageHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'send_message'
      )?.[1]

      await sendMessageHandler(messageData)

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Failed to send message',
      })
      
      consoleSpy.mockRestore()
    })
  })

  describe('handleTypingStart', () => {
    const typingData = {
      conversationId: '123e4567-e89b-12d3-a456-************',
    }

    it('should broadcast typing start when user has access', async () => {
      mockMessageService.verifyConversationAccess.mockResolvedValue({ id: 'participant-1' } as any)
      mockSocket.to = vi.fn(() => ({ emit: vi.fn() }))

      socketEventHandler.handleConnection(mockSocket as any)
      const typingStartHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'typing_start'
      )?.[1]

      await typingStartHandler(typingData)

      expect(mockMessageService.verifyConversationAccess).toHaveBeenCalledWith(
        mockUser.id,
        typingData.conversationId
      )
      expect(mockSocket.to).toHaveBeenCalledWith('conversation_123e4567-e89b-12d3-a456-************')
    })

    it('should not broadcast typing when user has no access', async () => {
      mockMessageService.verifyConversationAccess.mockResolvedValue(null)
      mockSocket.to = vi.fn(() => ({ emit: vi.fn() }))

      socketEventHandler.handleConnection(mockSocket as any)
      const typingStartHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'typing_start'
      )?.[1]

      await typingStartHandler(typingData)

      expect(mockSocket.to).not.toHaveBeenCalled()
    })

    it('should handle validation errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      socketEventHandler.handleConnection(mockSocket as any)
      const typingStartHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'typing_start'
      )?.[1]

      await typingStartHandler({ invalidData: true })

      expect(consoleSpy).toHaveBeenCalledWith('Error handling typing start:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })
  })

  describe('handleTypingStop', () => {
    const typingData = {
      conversationId: '123e4567-e89b-12d3-a456-************',
    }

    it('should broadcast typing stop when user has access', async () => {
      mockMessageService.verifyConversationAccess.mockResolvedValue({ id: 'participant-1' } as any)
      mockSocket.to = vi.fn(() => ({ emit: vi.fn() }))

      socketEventHandler.handleConnection(mockSocket as any)
      const typingStopHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'typing_stop'
      )?.[1]

      await typingStopHandler(typingData)

      expect(mockMessageService.verifyConversationAccess).toHaveBeenCalledWith(
        mockUser.id,
        typingData.conversationId
      )
      expect(mockSocket.to).toHaveBeenCalledWith('conversation_123e4567-e89b-12d3-a456-************')
    })

    it('should not broadcast typing when user has no access', async () => {
      mockMessageService.verifyConversationAccess.mockResolvedValue(null)
      mockSocket.to = vi.fn(() => ({ emit: vi.fn() }))

      socketEventHandler.handleConnection(mockSocket as any)
      const typingStopHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'typing_stop'
      )?.[1]

      await typingStopHandler(typingData)

      expect(mockSocket.to).not.toHaveBeenCalled()
    })

    it('should handle errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      socketEventHandler.handleConnection(mockSocket as any)
      const typingStopHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'typing_stop'
      )?.[1]

      await typingStopHandler({ invalidData: true })

      expect(consoleSpy).toHaveBeenCalledWith('Error handling typing stop:', expect.any(Error))

      consoleSpy.mockRestore()
    })
  })

  describe('handleJoinConversation', () => {
    const joinData = {
      conversationId: '123e4567-e89b-12d3-a456-************',
    }

    it('should join conversation when user has access', async () => {
      mockConversationService.joinConversation.mockResolvedValue(true)

      socketEventHandler.handleConnection(mockSocket as any)
      const joinConversationHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'join_conversation'
      )?.[1]

      await joinConversationHandler(joinData)

      expect(mockConversationService.joinConversation).toHaveBeenCalledWith(
        mockUser.id,
        joinData.conversationId
      )
      expect(mockSocket.join).toHaveBeenCalledWith('conversation_123e4567-e89b-12d3-a456-************')
      expect(mockSocket.emit).toHaveBeenCalledWith('joined_conversation', {
        conversationId: joinData.conversationId,
      })
    })

    it('should deny access when user cannot join conversation', async () => {
      mockConversationService.joinConversation.mockResolvedValue(false)

      socketEventHandler.handleConnection(mockSocket as any)
      const joinConversationHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'join_conversation'
      )?.[1]

      await joinConversationHandler(joinData)

      expect(mockSocket.join).not.toHaveBeenCalled()
      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Access denied to conversation',
      })
    })

    it('should handle validation errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      socketEventHandler.handleConnection(mockSocket as any)
      const joinConversationHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'join_conversation'
      )?.[1]

      await joinConversationHandler({ invalidData: true })

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Failed to join conversation',
      })
      expect(consoleSpy).toHaveBeenCalledWith('Error joining conversation:', expect.any(Error))

      consoleSpy.mockRestore()
    })

    it('should handle service errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockConversationService.joinConversation.mockRejectedValue(new Error('Database error'))

      socketEventHandler.handleConnection(mockSocket as any)
      const joinConversationHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'join_conversation'
      )?.[1]

      await joinConversationHandler(joinData)

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Failed to join conversation',
      })
      expect(consoleSpy).toHaveBeenCalledWith('Error joining conversation:', expect.any(Error))

      consoleSpy.mockRestore()
    })
  })

  describe('handleUserOnline', () => {
    it('should update user status and broadcast online status', async () => {
      mockMessageService.updateUserStatus.mockResolvedValue(true)

      socketEventHandler.handleConnection(mockSocket as any)
      const userOnlineHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'user_online'
      )?.[1]

      await userOnlineHandler()

      expect(mockMessageService.updateUserStatus).toHaveBeenCalledWith(mockUser.id, true)
      expect(mockSocket.broadcast.emit).toHaveBeenCalledWith('user_status_change', {
        userId: mockUser.id,
        status: 'online',
      })
    })

    it('should handle errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockMessageService.updateUserStatus.mockRejectedValue(new Error('Database error'))

      socketEventHandler.handleConnection(mockSocket as any)
      const userOnlineHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'user_online'
      )?.[1]

      await userOnlineHandler()

      expect(consoleSpy).toHaveBeenCalledWith('Error updating user online status:', expect.any(Error))

      consoleSpy.mockRestore()
    })
  })

  describe('handleDisconnect', () => {
    it('should update user status and broadcast offline status', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      mockMessageService.updateUserStatus.mockResolvedValue(true)

      socketEventHandler.handleConnection(mockSocket as any)
      const disconnectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'disconnect'
      )?.[1]

      await disconnectHandler()

      expect(consoleSpy).toHaveBeenCalledWith(`User ${mockUser.id} disconnected from messaging`)
      expect(mockMessageService.updateUserStatus).toHaveBeenCalledWith(mockUser.id, false)
      expect(mockSocket.broadcast.emit).toHaveBeenCalledWith('user_status_change', {
        userId: mockUser.id,
        status: 'offline',
      })

      consoleSpy.mockRestore()
    })

    it('should handle errors gracefully', async () => {
      const consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockMessageService.updateUserStatus.mockRejectedValue(new Error('Database error'))

      socketEventHandler.handleConnection(mockSocket as any)
      const disconnectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'disconnect'
      )?.[1]

      await disconnectHandler()

      expect(consoleErrorSpy).toHaveBeenCalledWith('Error updating user offline status:', expect.any(Error))

      consoleLogSpy.mockRestore()
      consoleErrorSpy.mockRestore()
    })
  })

  describe('message status events', () => {
    const mockMessageStatus = {
      id: '123e4567-e89b-12d3-a456-426614174004',
      messageId: '123e4567-e89b-12d3-a456-************',
      userId: '123e4567-e89b-12d3-a456-426614174000',
      status: 'DELIVERED' as const,
      createdAt: new Date('2023-01-01T00:00:00Z'),
      updatedAt: new Date('2023-01-01T00:00:00Z'),
      user: {
        id: mockUser.id,
        username: mockUser.username,
        firstName: mockUser.firstName,
        lastName: mockUser.lastName,
        profilePicture: mockUser.profilePicture,
      }
    }

    describe('message_delivered event', () => {
      const deliveredData = {
        messageId: '123e4567-e89b-12d3-a456-************',
        tempId: 'temp-123'
      }

      it('should mark message as delivered successfully', async () => {
        mockMessageStatusService.markMessageAsDelivered.mockResolvedValue(mockMessageStatus)
        mockMessageService.getMessageById.mockResolvedValue(mockMessage)
        mockIo.to.mockReturnValue({ emit: vi.fn() } as any)

        socketEventHandler.handleConnection(mockSocket as any)
        const deliveredHandler = mockSocket.on.mock.calls.find(
          call => call[0] === 'message_delivered'
        )?.[1]

        await deliveredHandler(deliveredData)

        expect(mockMessageStatusService.markMessageAsDelivered).toHaveBeenCalledWith(
          deliveredData.messageId,
          mockUser.id
        )
        expect(mockIo.to).toHaveBeenCalledWith('conversation_123e4567-e89b-12d3-a456-************')
      })

      it('should handle validation errors', async () => {
        const invalidData = { messageId: 'invalid-uuid' }

        socketEventHandler.handleConnection(mockSocket as any)
        const deliveredHandler = mockSocket.on.mock.calls.find(
          call => call[0] === 'message_delivered'
        )?.[1]

        await deliveredHandler(invalidData)

        expect(mockSocket.emit).toHaveBeenCalledWith('error', {
          message: 'Failed to update message status',
          details: expect.any(Array)
        })
      })
    })

    describe('message_read event', () => {
      const readData = {
        messageId: '123e4567-e89b-12d3-a456-************',
        tempId: 'temp-123'
      }

      it('should mark message as read successfully', async () => {
        const readMessageStatus = { ...mockMessageStatus, status: 'READ' as const }
        mockMessageStatusService.markMessageAsRead.mockResolvedValue(readMessageStatus)
        mockMessageService.getMessageById.mockResolvedValue(mockMessage)
        mockIo.to.mockReturnValue({ emit: vi.fn() } as any)

        socketEventHandler.handleConnection(mockSocket as any)
        const readHandler = mockSocket.on.mock.calls.find(
          call => call[0] === 'message_read'
        )?.[1]

        await readHandler(readData)

        expect(mockMessageStatusService.markMessageAsRead).toHaveBeenCalledWith(
          readData.messageId,
          mockUser.id
        )
        expect(mockIo.to).toHaveBeenCalledWith('conversation_123e4567-e89b-12d3-a456-************')
      })
    })

    describe('message_failed event', () => {
      const failedData = {
        messageId: '123e4567-e89b-12d3-a456-************',
        tempId: 'temp-123'
      }

      it('should mark message as failed successfully', async () => {
        const failedMessageStatus = { ...mockMessageStatus, status: 'FAILED' as const }
        mockMessageStatusService.markMessageAsFailed.mockResolvedValue(failedMessageStatus)

        socketEventHandler.handleConnection(mockSocket as any)
        const failedHandler = mockSocket.on.mock.calls.find(
          call => call[0] === 'message_failed'
        )?.[1]

        await failedHandler(failedData)

        expect(mockMessageStatusService.markMessageAsFailed).toHaveBeenCalledWith(
          failedData.messageId,
          mockUser.id
        )
        expect(mockSocket.emit).toHaveBeenCalledWith('message_status_updated', {
          messageId: failedData.messageId,
          userId: mockUser.id,
          status: 'FAILED',
          tempId: failedData.tempId,
          updatedAt: failedMessageStatus.updatedAt
        })
      })
    })
  })
})
