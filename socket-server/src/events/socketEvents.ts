// socket-server/src/events/socketEvents.ts
import { Server, Socket } from 'socket.io';
import { MessageService } from '../services/messageService';
import { ConversationService } from '../services/conversationService';
import { MessageStatusService } from '../services/messageStatusService';
import { z } from 'zod';
import {
  MessageCreateSchema,
  JoinRoomSchema,
  TypingEventSchema,
  MessageStatusInputSchema
} from '../schemas';

interface AuthenticatedSocket extends Socket {
  userId: string;
  user: any;
}

export class SocketEventHandler {
  private io: Server;
  private messageService: MessageService;
  private conversationService: ConversationService;
  private messageStatusService: MessageStatusService;

  constructor(
    io: Server,
    messageService: MessageService,
    conversationService: ConversationService,
    messageStatusService: MessageStatusService
  ) {
    this.io = io;
    this.messageService = messageService;
    this.conversationService = conversationService;
    this.messageStatusService = messageStatusService;
  }

  handleConnection(socket: AuthenticatedSocket) {
    console.log(`User ${socket.userId} connected to messaging`);

    // Join user to their conversations
    socket.on('join_conversations', () => this.handleJoinConversations(socket));

    // Handle new message creation
    socket.on('send_message', (data) => this.handleSendMessage(socket, data));

    // Handle typing indicators
    socket.on('typing_start', (data) => this.handleTypingStart(socket, data));
    socket.on('typing_stop', (data) => this.handleTypingStop(socket, data));

    // Handle joining specific conversation room
    socket.on('join_conversation', (data) => this.handleJoinConversation(socket, data));

    // Handle user status
    socket.on('user_online', () => this.handleUserOnline(socket));

    // Handle message status updates
    socket.on('message_delivered', (data) => this.handleMessageDelivered(socket, data));
    socket.on('message_read', (data) => this.handleMessageRead(socket, data));
    socket.on('message_failed', (data) => this.handleMessageFailed(socket, data));

    // Handle conversation operations
    socket.on('create_conversation', (data) => this.handleCreateConversation(socket, data));
    socket.on('get_conversations', (data) => this.handleGetConversations(socket, data));
    socket.on('get_conversation_messages', (data) => this.handleGetConversationMessages(socket, data));

    // Handle disconnection
    socket.on('disconnect', () => this.handleDisconnect(socket));
  }

  private async handleJoinConversations(socket: AuthenticatedSocket) {
    try {
      const conversations = await this.conversationService.getUserConversations(socket.userId);

      // Join all conversation rooms
      for (const conversation of conversations) {
        socket.join(`conversation_${conversation.id}`);
      }

      socket.emit('conversations_joined', {
        success: true,
        count: conversations.length,
        conversations: conversations
      });
    } catch (error) {
      console.error('Error joining conversations:', error);
      socket.emit('error', { message: 'Failed to join conversations' });
    }
  }

  private async handleSendMessage(socket: AuthenticatedSocket, data: any) {
    try {
      console.log('📨 [SOCKET_SERVER] Handling send_message event:', {
        userId: socket.userId,
        conversationId: data.conversationId,
        hasContent: !!data.content,
        hasEncryptedContent: !!data.encryptedContent,
        tempId: data.tempId
      });

      const message = await this.messageService.createMessage(data, socket.userId);

      // Create DELIVERED status for the sender
      await this.messageStatusService.markMessageAsDelivered(message.id, socket.userId);

      // Emit to all participants in the conversation
      const messagePayload: any = {
        id: message.id,
        conversationId: message.conversationId,
        sender: message.sender,
        content: message.content,
        messageType: message.messageType,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt,
        status: 'DELIVERED'
      };

      // Add encryption fields if present
      if (message.encryptedContent) {
        messagePayload.encryptedContent = message.encryptedContent;
        messagePayload.iv = message.iv;
        messagePayload.messageNumber = message.messageNumber;
        messagePayload.senderRatchetKey = message.senderRatchetKey;
        messagePayload.previousChainLength = message.previousChainLength;
      }

      this.io.to(`conversation_${message.conversationId}`).emit('new_message', messagePayload);

      // Acknowledge to sender
      socket.emit('message_sent', {
        tempId: data.tempId, // For optimistic UI updates
        messageId: message.id,
        status: 'DELIVERED'
      });

    } catch (error) {
      console.error('❌ [SOCKET_SERVER] Error sending message:', error);
      console.error('❌ [SOCKET_SERVER] Message data:', {
        userId: socket.userId,
        conversationId: data.conversationId,
        tempId: data.tempId,
        hasContent: !!data.content,
        hasEncryptedContent: !!data.encryptedContent
      });

      // Mark message as failed if it exists
      if (data.tempId) {
        console.log('📤 [SOCKET_SERVER] Emitting message_failed for tempId:', data.tempId);
        socket.emit('message_failed', {
          tempId: data.tempId,
          error: error instanceof Error ? error.message : 'Failed to send message'
        });
      }

      if (error instanceof z.ZodError) {
        console.error('❌ [SOCKET_SERVER] Validation error:', error.issues);
        socket.emit('error', {
          message: 'Invalid message data',
          details: error.issues
        });
      } else {
        console.error('❌ [SOCKET_SERVER] General error:', error instanceof Error ? error.message : 'Unknown error');
        socket.emit('error', {
          message: error instanceof Error ? error.message : 'Failed to send message'
        });
      }
    }
  }

  private async handleTypingStart(socket: AuthenticatedSocket, data: any) {
    try {
      const typingData = TypingEventSchema.parse(data);

      // Verify access to conversation
      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        typingData.conversationId
      );

      if (hasAccess) {
        socket.to(`conversation_${typingData.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: typingData.conversationId,
          isTyping: true
        });
      }
    } catch (error) {
      console.error('Error handling typing start:', error);
    }
  }

  private async handleTypingStop(socket: AuthenticatedSocket, data: any) {
    try {
      const typingData = TypingEventSchema.parse(data);

      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        typingData.conversationId
      );

      if (hasAccess) {
        socket.to(`conversation_${typingData.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: typingData.conversationId,
          isTyping: false
        });
      }
    } catch (error) {
      console.error('Error handling typing stop:', error);
    }
  }

  private async handleJoinConversation(socket: AuthenticatedSocket, data: any) {
    try {
      const { conversationId } = JoinRoomSchema.parse(data);

      const hasAccess = await this.conversationService.joinConversation(socket.userId, conversationId);
      if (hasAccess) {
        socket.join(`conversation_${conversationId}`);
        socket.emit('joined_conversation', { conversationId });
      } else {
        socket.emit('error', { message: 'Access denied to conversation' });
      }
    } catch (error) {
      console.error('Error joining conversation:', error);
      socket.emit('error', { message: 'Failed to join conversation' });
    }
  }

  private async handleUserOnline(socket: AuthenticatedSocket) {
    try {
      await this.messageService.updateUserStatus(socket.userId, true);

      socket.broadcast.emit('user_status_change', {
        userId: socket.userId,
        status: 'online'
      });
    } catch (error) {
      console.error('Error updating user online status:', error);
    }
  }

  private async handleDisconnect(socket: AuthenticatedSocket) {
    console.log(`User ${socket.userId} disconnected from messaging`);

    try {
      await this.messageService.updateUserStatus(socket.userId, false);

      socket.broadcast.emit('user_status_change', {
        userId: socket.userId,
        status: 'offline'
      });
    } catch (error) {
      console.error('Error updating user offline status:', error);
    }
  }

  private async handleMessageDelivered(socket: AuthenticatedSocket, data: any) {
    try {
      const validatedData = MessageStatusInputSchema.parse(data);

      const messageStatus = await this.messageStatusService.markMessageAsDelivered(
        validatedData.messageId,
        socket.userId
      );

      // Emit to the message sender
      const message = await this.messageService.getMessageById(validatedData.messageId);
      if (message) {
        this.io.to(`conversation_${message.conversationId}`).emit('message_status_updated', {
          messageId: validatedData.messageId,
          userId: socket.userId,
          status: 'DELIVERED',
          tempId: validatedData.tempId,
          updatedAt: messageStatus.updatedAt
        });
      }
    } catch (error) {
      console.error('Error handling message delivered:', error);
      socket.emit('error', {
        message: 'Failed to update message status',
        details: error instanceof z.ZodError ? error.issues : undefined
      });
    }
  }

  private async handleMessageRead(socket: AuthenticatedSocket, data: any) {
    try {
      const validatedData = MessageStatusInputSchema.parse(data);

      const messageStatus = await this.messageStatusService.markMessageAsRead(
        validatedData.messageId,
        socket.userId
      );

      // Emit to the message sender
      const message = await this.messageService.getMessageById(validatedData.messageId);
      if (message) {
        this.io.to(`conversation_${message.conversationId}`).emit('message_status_updated', {
          messageId: validatedData.messageId,
          userId: socket.userId,
          status: 'READ',
          tempId: validatedData.tempId,
          updatedAt: messageStatus.updatedAt
        });
      }
    } catch (error) {
      console.error('Error handling message read:', error);
      socket.emit('error', {
        message: 'Failed to update message status',
        details: error instanceof z.ZodError ? error.issues : undefined
      });
    }
  }

  private async handleMessageFailed(socket: AuthenticatedSocket, data: any) {
    try {
      const validatedData = MessageStatusInputSchema.parse(data);

      const messageStatus = await this.messageStatusService.markMessageAsFailed(
        validatedData.messageId,
        socket.userId
      );

      // Emit back to the sender
      socket.emit('message_status_updated', {
        messageId: validatedData.messageId,
        userId: socket.userId,
        status: 'FAILED',
        tempId: validatedData.tempId,
        updatedAt: messageStatus.updatedAt
      });
    } catch (error) {
      console.error('Error handling message failed:', error);
      socket.emit('error', {
        message: 'Failed to update message status',
        details: error instanceof z.ZodError ? error.issues : undefined
      });
    }
  }

  private async handleCreateConversation(socket: AuthenticatedSocket, data: any) {
    try {
      console.log('🆕 [SOCKET_SERVER] Creating conversation via socket:', {
        userId: socket.userId,
        data
      });

      // Validate input data
      const validatedData = z.object({
        type: z.enum(['DIRECT', 'GROUP']),
        participant_ids: z.array(z.string()),
        name: z.string().optional()
      }).parse(data);

      // Transform to match ConversationService interface
      const conversationData = {
        type: validatedData.type,
        participantIds: validatedData.participant_ids,
        name: validatedData.name
      };

      // Create conversation using the conversation service
      const conversation = await this.conversationService.createConversation(
        conversationData,
        socket.userId
      );

      if (!conversation) {
        throw new Error('Failed to create conversation');
      }

      console.log('✅ [SOCKET_SERVER] Conversation created successfully:', conversation.id);

      // Emit success back to creator
      socket.emit('conversation_created', conversation);

      // Notify other participants about the new conversation
      const otherParticipants = conversation.participants?.filter(
        p => p.userId !== socket.userId
      ) || [];

      for (const participant of otherParticipants) {
        this.io.to(`user:${participant.userId}`).emit('new_conversation', {
          conversation
        });
        console.log(`📢 [SOCKET_SERVER] Notified user:${participant.userId} about new conversation`);
      }

    } catch (error) {
      console.error('❌ [SOCKET_SERVER] Error creating conversation:', error);

      if (error instanceof z.ZodError) {
        socket.emit('conversation_error', {
          message: 'Invalid conversation data',
          details: error.issues
        });
      } else {
        socket.emit('conversation_error', {
          message: error instanceof Error ? error.message : 'Failed to create conversation'
        });
      }
    }
  }

  private async handleGetConversations(socket: AuthenticatedSocket, _data: any) {
    try {
      console.log('📋 [SOCKET_SERVER] Getting conversations for user:', socket.userId);

      const conversations = await this.conversationService.getUserConversations(socket.userId);

      socket.emit('conversations_received', {
        conversations
      });

      console.log(`✅ [SOCKET_SERVER] Sent ${conversations.length} conversations to user:${socket.userId}`);

    } catch (error) {
      console.error('❌ [SOCKET_SERVER] Error getting conversations:', error);
      socket.emit('conversations_error', {
        message: error instanceof Error ? error.message : 'Failed to load conversations'
      });
    }
  }

  private async handleGetConversationMessages(socket: AuthenticatedSocket, data: any) {
    try {
      console.log('💬 [SOCKET_SERVER] Getting messages for conversation:', data);

      // Validate input data
      const validatedData = z.object({
        conversationId: z.string(),
        page: z.number().optional().default(1),
        limit: z.number().optional().default(50)
      }).parse(data);

      // Get messages using the message service
      const messages = await this.messageService.getConversationMessages(
        validatedData.conversationId,
        socket.userId,
        validatedData.page,
        validatedData.limit
      );

      socket.emit('conversation_messages_loaded', {
        conversationId: validatedData.conversationId,
        messages,
        page: validatedData.page
      });

      console.log(`✅ [SOCKET_SERVER] Sent ${messages.length} messages for conversation:${validatedData.conversationId}`);

    } catch (error) {
      console.error('❌ [SOCKET_SERVER] Error getting conversation messages:', error);

      if (error instanceof z.ZodError) {
        socket.emit('conversation_messages_error', {
          message: 'Invalid request data',
          details: error.issues
        });
      } else {
        socket.emit('conversation_messages_error', {
          message: error instanceof Error ? error.message : 'Failed to load messages'
        });
      }
    }
  }
}
