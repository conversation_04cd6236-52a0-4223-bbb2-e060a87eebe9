// socket-server/src/server.ts
import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import { PrismaClient } from '@prisma/client';
import { authenticateSocket, AuthenticatedSocket } from './middleware/auth';
import { MessageService } from './services/messageService';
import { ConversationService } from './services/conversationService';
import { MessageStatusService } from './services/messageStatusService';
import { SocketEventHandler } from './events/socketEvents';

const app = express();
const server = createServer(app);
const prisma = new PrismaClient();

// Initialize services
const messageService = new MessageService(prisma);
const conversationService = new ConversationService(prisma);
const messageStatusService = new MessageStatusService(prisma);

// CORS configuration
app.use(cors({
  origin: ['http://localhost:5000', 'http://127.0.0.1:5000', 'http://localhost:6000', 'http://127.0.0.1:6000', 'http://localhost:8000', 'http://127.0.0.1:8000'],
  credentials: true,
}));

// Middleware for parsing JSON
app.use(express.json());

// Socket.io server setup
const io = new Server(server, {
  cors: {
    origin: ['http://localhost:5000', 'http://127.0.0.1:5000', 'http://localhost:6000', 'http://127.0.0.1:6000'],
    methods: ['GET', 'POST'],
    credentials: true,
  },
});

// Initialize socket event handler
const socketEventHandler = new SocketEventHandler(io, messageService, conversationService, messageStatusService);

// API endpoint for backend notifications
app.post('/api/notify', (req, res) => {
  try {
    const { event, conversation, participants } = req.body;

    if (event === 'new_conversation' && conversation && participants) {
      console.log(`📢 [NOTIFY] Broadcasting new conversation to ${participants.length} participants`);

      // Emit to each participant's personal room
      participants.forEach((userId: string) => {
        io.to(`user:${userId}`).emit('new_conversation', {
          conversation: conversation
        });
        console.log(`📢 [NOTIFY] Sent new_conversation event to user:${userId}`);
      });

      res.json({ success: true, notified: participants.length });
    } else {
      res.status(400).json({ error: 'Invalid notification data' });
    }
  } catch (error) {
    console.error('Error handling notification:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Authentication middleware
io.use(authenticateSocket);

// Socket connection handler
io.on('connection', (socket) => {
  const authSocket = socket as AuthenticatedSocket;
  console.log(`User ${authSocket.user.username} connected`);

  // Join user to their personal room
  authSocket.join(`user:${authSocket.userId}`);

  // Initialize message handlers using the service layer
  socketEventHandler.handleConnection(authSocket);
});

const PORT = process.env.PORT || 7000;

server.listen(PORT, () => {
  console.log(`Socket server running on port ${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  server.close();
});

process.on('SIGINT', async () => {
  await prisma.$disconnect();
  server.close();
});
