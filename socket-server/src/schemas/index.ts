// socket-server/src/schemas/index.ts
import { z } from 'zod';

// User schemas
export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  username: z.string().min(3).max(30),
  firstName: z.string().min(1).max(30),
  lastName: z.string().min(1).max(30),
  profilePicture: z.string().url().optional(),
  isVerified: z.boolean(),
  lastSeen: z.date(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Message schemas
export const MessageTypeSchema = z.enum(['TEXT', 'IMAGE', 'FILE', 'SYSTEM']);
export const MessageStatusTypeSchema = z.enum(['SENT', 'DELIVERED', 'READ', 'FAILED']);

export const MessageSchema = z.object({
  id: z.string().uuid(),
  content: z.string().min(1).max(4000),
  senderId: z.string().uuid(),
  conversationId: z.string().uuid(),
  messageType: MessageTypeSchema.default('TEXT'),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const MessageCreateSchema = z.object({
  content: z.string().max(4000).optional(), // Optional for encrypted messages
  conversationId: z.string().uuid(),
  messageType: MessageTypeSchema.default('TEXT'),
  tempId: z.string().optional(),
  // Encryption fields
  encryptedContent: z.string().optional(),
  iv: z.string().optional(),
  messageNumber: z.number().int().min(0).optional(),
  senderRatchetKey: z.string().optional(),
  previousChainLength: z.number().int().min(0).optional(),
  messageKeyId: z.string().optional(),
}).refine(
  (data) => data.content || data.encryptedContent,
  {
    message: "Either content or encryptedContent must be provided",
    path: ["content"],
  }
);

export const MessageStatusSchema = z.object({
  id: z.string().uuid(),
  messageId: z.string().uuid(),
  userId: z.string().uuid(),
  status: MessageStatusTypeSchema,
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const MessageStatusUpdateSchema = z.object({
  messageId: z.string().uuid(),
  status: MessageStatusTypeSchema,
  tempId: z.string().optional(),
});

export const MessageStatusInputSchema = z.object({
  messageId: z.string().uuid(),
  tempId: z.string().optional(),
});

// Conversation schemas
export const ConversationTypeSchema = z.enum(['DIRECT', 'GROUP']);
export const ParticipantRoleSchema = z.enum(['ADMIN', 'MEMBER']);

export const ConversationSchema = z.object({
  id: z.string().uuid(),
  type: ConversationTypeSchema.default('DIRECT'),
  name: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const ConversationCreateSchema = z.object({
  type: ConversationTypeSchema,
  name: z.string().max(100).optional(),
  participantIds: z.array(z.string().uuid()).min(1),
});

// Socket event schemas
export const JoinRoomSchema = z.object({
  conversationId: z.string().uuid(),
});

export const LeaveRoomSchema = z.object({
  conversationId: z.string().uuid(),
});

export const TypingEventSchema = z.object({
  conversationId: z.string().uuid(),
});

// Authentication schemas
export const AuthTokenSchema = z.object({
  token: z.string().min(1),
});

// Response schemas
export const SuccessResponseSchema = z.object({
  success: z.literal(true),
  data: z.any(),
  timestamp: z.date(),
});

export const ErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  timestamp: z.date(),
});

export const ResponseSchema = z.union([SuccessResponseSchema, ErrorResponseSchema]);

// Type exports
export type User = z.infer<typeof UserSchema>;
export type Message = z.infer<typeof MessageSchema>;
export type MessageCreate = z.infer<typeof MessageCreateSchema>;
export type MessageType = z.infer<typeof MessageTypeSchema>;
export type MessageStatus = z.infer<typeof MessageStatusSchema>;
export type MessageStatusType = z.infer<typeof MessageStatusTypeSchema>;
export type MessageStatusUpdate = z.infer<typeof MessageStatusUpdateSchema>;
export type MessageStatusInput = z.infer<typeof MessageStatusInputSchema>;
export type Conversation = z.infer<typeof ConversationSchema>;
export type ConversationCreate = z.infer<typeof ConversationCreateSchema>;
export type ConversationType = z.infer<typeof ConversationTypeSchema>;
export type ParticipantRole = z.infer<typeof ParticipantRoleSchema>;
export type JoinRoom = z.infer<typeof JoinRoomSchema>;
export type LeaveRoom = z.infer<typeof LeaveRoomSchema>;
export type TypingEvent = z.infer<typeof TypingEventSchema>;
export type AuthToken = z.infer<typeof AuthTokenSchema>;
export type SuccessResponse = z.infer<typeof SuccessResponseSchema>;
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
export type Response = z.infer<typeof ResponseSchema>;
