#!/usr/bin/env python3
import requests

# Test user search endpoint
BASE_URL = "http://localhost:8000"

def login_user(email, password):
    """Login and return token"""
    login_data = {"email": email, "password": password}
    response = requests.post(f"{BASE_URL}/api/auth/login/", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            return data['data']['tokens']['access']
    return None

def test_user_search():
    print("=== Testing User Search API ===\n")
    
    # Login as Alice
    print("1. Logging in as Alice...")
    token = login_user("<EMAIL>", "password123")
    if not token:
        print("Failed to login")
        return
    print("✅ Login successful\n")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test search queries
    test_queries = ["bob", "charlie", "smith", "nonexistent"]
    
    for query in test_queries:
        print(f"2. Searching for '{query}'...")
        response = requests.get(f"{BASE_URL}/api/messaging/users/search/?q={query}", headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                users = data.get('data', [])
                print(f"Found {len(users)} users:")
                for user in users:
                    print(f"  - {user['full_name']} (@{user['username']}) - ID: {user['id']}")
            else:
                print(f"Error: {data.get('error')}")
        else:
            print(f"HTTP Error: {response.text}")
        print()

if __name__ == "__main__":
    test_user_search()
